## ComfyUI-Manager: installing dependencies done.
[2025-08-31 01:11:58.315] ** ComfyUI startup time: 2025-08-31 01:11:58.314
[2025-08-31 01:11:58.315] ** Platform: Windows
[2025-08-31 01:11:58.315] ** Python version: 3.10.18 | packaged by conda-forge | (main, Jun  4 2025, 14:42:04) [MSC v.1943 64 bit (AMD64)]
[2025-08-31 01:11:58.315] ** Python executable: C:\Users\<USER>\Desktop\indextts\python\python.exe
[2025-08-31 01:11:58.315] ** ComfyUI Path: C:\Users\<USER>\Desktop\indextts\ComfyUI
[2025-08-31 01:11:58.315] ** ComfyUI Base Folder Path: C:\Users\<USER>\Desktop\indextts\ComfyUI
[2025-08-31 01:11:58.315] ** User directory: C:\Users\<USER>\Desktop\indextts\ComfyUI\user
[2025-08-31 01:11:58.316] ** ComfyUI-Manager config path: C:\Users\<USER>\Desktop\indextts\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-08-31 01:11:58.316] ** Log path: C:\Users\<USER>\Desktop\indextts\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-08-31 01:11:59.039]    1.7 seconds: C:\Users\<USER>\Desktop\indextts\ComfyUI\custom_nodes\ComfyUI-Manager
[2025-08-31 01:11:59.040] 
[2025-08-31 01:12:00.022] Warning, you are using an old pytorch version and some ckpt/pt files might be loaded unsafely. Upgrading to 2.4 or above is recommended.
[2025-08-31 01:12:00.137] Total VRAM 24564 MB, total RAM 98085 MB
[2025-08-31 01:12:00.137] pytorch version: 2.3.1
[2025-08-31 01:12:00.137] Set vram state to: NORMAL_VRAM
[2025-08-31 01:12:00.137] Device: cuda:0 NVIDIA GeForce RTX 3090 Ti : native
[2025-08-31 01:12:00.153] Please update pytorch to use native RMSNorm
[2025-08-31 01:12:00.153] Torch version too old to set sdpa backend priority.
[2025-08-31 01:12:00.307] Traceback (most recent call last):
[2025-08-31 01:12:00.307]   File "C:\Users\<USER>\Desktop\indextts\ComfyUI\main.py", line 147, in <module>
[2025-08-31 01:12:00.308]     import execution
[2025-08-31 01:12:00.308]   File "C:\Users\<USER>\Desktop\indextts\ComfyUI\execution.py", line 16, in <module>
[2025-08-31 01:12:00.308]     import nodes
[2025-08-31 01:12:00.309]   File "C:\Users\<USER>\Desktop\indextts\ComfyUI\nodes.py", line 24, in <module>
[2025-08-31 01:12:00.309]     import comfy.diffusers_load
[2025-08-31 01:12:00.309]   File "C:\Users\<USER>\Desktop\indextts\ComfyUI\comfy\diffusers_load.py", line 3, in <module>
[2025-08-31 01:12:00.310]     import comfy.sd
[2025-08-31 01:12:00.310]   File "C:\Users\<USER>\Desktop\indextts\ComfyUI\comfy\sd.py", line 11, in <module>
[2025-08-31 01:12:00.310]     from .ldm.cascade.stage_c_coder import StageC_coder
[2025-08-31 01:12:00.311]   File "C:\Users\<USER>\Desktop\indextts\ComfyUI\comfy\ldm\cascade\stage_c_coder.py", line 19, in <module>
[2025-08-31 01:12:00.311]     import torchvision
[2025-08-31 01:12:00.311]   File "C:\Users\<USER>\Desktop\indextts\python\lib\site-packages\torchvision\__init__.py", line 6, in <module>
[2025-08-31 01:12:00.312]     from torchvision import _meta_registrations, datasets, io, models, ops, transforms, utils
[2025-08-31 01:12:00.312]   File "C:\Users\<USER>\Desktop\indextts\python\lib\site-packages\torchvision\models\__init__.py", line 2, in <module>
[2025-08-31 01:12:00.312]     from .convnext import *
[2025-08-31 01:12:00.312]   File "C:\Users\<USER>\Desktop\indextts\python\lib\site-packages\torchvision\models\convnext.py", line 8, in <module>
[2025-08-31 01:12:00.313]     from ..ops.misc import Conv2dNormActivation, Permute
[2025-08-31 01:12:00.313]   File "C:\Users\<USER>\Desktop\indextts\python\lib\site-packages\torchvision\ops\__init__.py", line 1, in <module>
[2025-08-31 01:12:00.313]     from ._register_onnx_ops import _register_custom_op
[2025-08-31 01:12:00.314]   File "C:\Users\<USER>\Desktop\indextts\python\lib\site-packages\torchvision\ops\_register_onnx_ops.py", line 5, in <module>
[2025-08-31 01:12:00.314]     from torch.onnx import symbolic_opset11 as opset11
[2025-08-31 01:12:00.314]   File "C:\Users\<USER>\Desktop\indextts\python\lib\site-packages\torch\onnx\__init__.py", line 46, in <module>
[2025-08-31 01:12:00.314]     from ._internal.exporter import (  # usort:skip. needs to be last to avoid circular import
[2025-08-31 01:12:00.315] ImportError: cannot import name 'DiagnosticOptions' from 'torch.onnx._internal.exporter' (C:\Users\<USER>\Desktop\indextts\python\lib\site-packages\torch\onnx\_internal\exporter\__init__.py)
