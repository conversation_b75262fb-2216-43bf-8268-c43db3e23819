# Copyright © 2018 <PERSON><PERSON> <<EMAIL>>
#
# Permission to use, copy, modify, and/or distribute this software for
# any purpose with or without fee is hereby granted, provided that the
# above copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
# SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER
# RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF
# CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
# CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.


class ProtocolError(Exception):
    pass


class LocalProtocolError(ProtocolError):
    pass


class MembersSyncError(LocalProtocolError):
    pass


class SendRetryError(LocalProtocolError):
    pass


class RemoteProtocolError(ProtocolError):
    pass


class LocalTransportError(ProtocolError):
    pass


class RemoteTransportError(ProtocolError):
    pass


class OlmTrustError(Exception):
    pass


class OlmUnverifiedDeviceError(OlmTrustError):
    def __init__(self, unverified_device, *args):
        super().__init__(*args)
        self.device = unverified_device


class VerificationError(Exception):
    pass


class EncryptionError(Exception):
    pass


class GroupEncryptionError(Exception):
    pass


class TransferCancelledError(Exception):
    pass
