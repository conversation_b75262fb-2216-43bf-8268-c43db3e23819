LICENSE
MANIFEST.in
README.md
pyproject.toml
setup.cfg
setup.py
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/vision.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/io/image/image.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/io/image/cpu/common_jpeg.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/io/image/cpu/decode_image.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/io/image/cpu/decode_jpeg.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/io/image/cpu/decode_png.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/io/image/cpu/encode_jpeg.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/io/image/cpu/encode_png.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/io/image/cpu/read_write_file.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/io/image/cuda/decode_jpeg_cuda.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/deform_conv2d.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/nms.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/ps_roi_align.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/ps_roi_pool.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/roi_align.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/roi_pool.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autocast/deform_conv2d_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autocast/nms_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autocast/ps_roi_align_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autocast/ps_roi_pool_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autocast/roi_align_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autocast/roi_pool_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autograd/deform_conv2d_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autograd/ps_roi_align_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autograd/ps_roi_pool_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autograd/roi_align_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/autograd/roi_pool_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cpu/deform_conv2d_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cpu/nms_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cpu/ps_roi_align_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cpu/ps_roi_pool_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cpu/roi_align_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cpu/roi_pool_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cuda/deform_conv2d_kernel.cu
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cuda/nms_kernel.cu
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cuda/ps_roi_align_kernel.cu
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cuda/ps_roi_pool_kernel.cu
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cuda/roi_align_kernel.cu
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/cuda/roi_pool_kernel.cu
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/quantized/cpu/qnms_kernel.cpp
C:/Jenkins/Miniconda3/conda-bld/torchvision_1716985612068/work/torchvision/csrc/ops/quantized/cpu/qroi_align_kernel.cpp
test/test_architecture_ops.py
test/test_backbone_utils.py
test/test_datasets.py
test/test_datasets_download.py
test/test_datasets_samplers.py
test/test_datasets_utils.py
test/test_datasets_video_utils.py
test/test_datasets_video_utils_opt.py
test/test_extended_models.py
test/test_functional_tensor.py
test/test_image.py
test/test_internal_utils.py
test/test_internet.py
test/test_io.py
test/test_io_opt.py
test/test_models.py
test/test_models_detection_anchor_utils.py
test/test_models_detection_negative_samples.py
test/test_models_detection_utils.py
test/test_onnx.py
test/test_ops.py
test/test_transforms.py
test/test_transforms_tensor.py
test/test_transforms_v2.py
test/test_transforms_v2_utils.py
test/test_transforms_video.py
test/test_tv_tensors.py
test/test_utils.py
test/test_video_gpu_decoder.py
test/test_video_reader.py
test/test_videoapi.py
torchvision/__init__.py
torchvision/_internally_replaced_utils.py
torchvision/_meta_registrations.py
torchvision/_utils.py
torchvision/extension.py
torchvision/utils.py
torchvision/version.py
torchvision.egg-info/PKG-INFO
torchvision.egg-info/SOURCES.txt
torchvision.egg-info/dependency_links.txt
torchvision.egg-info/not-zip-safe
torchvision.egg-info/requires.txt
torchvision.egg-info/top_level.txt
torchvision/datasets/__init__.py
torchvision/datasets/_optical_flow.py
torchvision/datasets/_stereo_matching.py
torchvision/datasets/caltech.py
torchvision/datasets/celeba.py
torchvision/datasets/cifar.py
torchvision/datasets/cityscapes.py
torchvision/datasets/clevr.py
torchvision/datasets/coco.py
torchvision/datasets/country211.py
torchvision/datasets/dtd.py
torchvision/datasets/eurosat.py
torchvision/datasets/fakedata.py
torchvision/datasets/fer2013.py
torchvision/datasets/fgvc_aircraft.py
torchvision/datasets/flickr.py
torchvision/datasets/flowers102.py
torchvision/datasets/folder.py
torchvision/datasets/food101.py
torchvision/datasets/gtsrb.py
torchvision/datasets/hmdb51.py
torchvision/datasets/imagenet.py
torchvision/datasets/imagenette.py
torchvision/datasets/inaturalist.py
torchvision/datasets/kinetics.py
torchvision/datasets/kitti.py
torchvision/datasets/lfw.py
torchvision/datasets/lsun.py
torchvision/datasets/mnist.py
torchvision/datasets/moving_mnist.py
torchvision/datasets/omniglot.py
torchvision/datasets/oxford_iiit_pet.py
torchvision/datasets/pcam.py
torchvision/datasets/phototour.py
torchvision/datasets/places365.py
torchvision/datasets/rendered_sst2.py
torchvision/datasets/sbd.py
torchvision/datasets/sbu.py
torchvision/datasets/semeion.py
torchvision/datasets/stanford_cars.py
torchvision/datasets/stl10.py
torchvision/datasets/sun397.py
torchvision/datasets/svhn.py
torchvision/datasets/ucf101.py
torchvision/datasets/usps.py
torchvision/datasets/utils.py
torchvision/datasets/video_utils.py
torchvision/datasets/vision.py
torchvision/datasets/voc.py
torchvision/datasets/widerface.py
torchvision/datasets/samplers/__init__.py
torchvision/datasets/samplers/clip_sampler.py
torchvision/io/__init__.py
torchvision/io/_load_gpu_decoder.py
torchvision/io/_video_opt.py
torchvision/io/image.py
torchvision/io/video.py
torchvision/io/video_reader.py
torchvision/models/__init__.py
torchvision/models/_api.py
torchvision/models/_meta.py
torchvision/models/_utils.py
torchvision/models/alexnet.py
torchvision/models/convnext.py
torchvision/models/densenet.py
torchvision/models/efficientnet.py
torchvision/models/feature_extraction.py
torchvision/models/googlenet.py
torchvision/models/inception.py
torchvision/models/maxvit.py
torchvision/models/mnasnet.py
torchvision/models/mobilenet.py
torchvision/models/mobilenetv2.py
torchvision/models/mobilenetv3.py
torchvision/models/regnet.py
torchvision/models/resnet.py
torchvision/models/shufflenetv2.py
torchvision/models/squeezenet.py
torchvision/models/swin_transformer.py
torchvision/models/vgg.py
torchvision/models/vision_transformer.py
torchvision/models/detection/__init__.py
torchvision/models/detection/_utils.py
torchvision/models/detection/anchor_utils.py
torchvision/models/detection/backbone_utils.py
torchvision/models/detection/faster_rcnn.py
torchvision/models/detection/fcos.py
torchvision/models/detection/generalized_rcnn.py
torchvision/models/detection/image_list.py
torchvision/models/detection/keypoint_rcnn.py
torchvision/models/detection/mask_rcnn.py
torchvision/models/detection/retinanet.py
torchvision/models/detection/roi_heads.py
torchvision/models/detection/rpn.py
torchvision/models/detection/ssd.py
torchvision/models/detection/ssdlite.py
torchvision/models/detection/transform.py
torchvision/models/optical_flow/__init__.py
torchvision/models/optical_flow/_utils.py
torchvision/models/optical_flow/raft.py
torchvision/models/quantization/__init__.py
torchvision/models/quantization/googlenet.py
torchvision/models/quantization/inception.py
torchvision/models/quantization/mobilenet.py
torchvision/models/quantization/mobilenetv2.py
torchvision/models/quantization/mobilenetv3.py
torchvision/models/quantization/resnet.py
torchvision/models/quantization/shufflenetv2.py
torchvision/models/quantization/utils.py
torchvision/models/segmentation/__init__.py
torchvision/models/segmentation/_utils.py
torchvision/models/segmentation/deeplabv3.py
torchvision/models/segmentation/fcn.py
torchvision/models/segmentation/lraspp.py
torchvision/models/video/__init__.py
torchvision/models/video/mvit.py
torchvision/models/video/resnet.py
torchvision/models/video/s3d.py
torchvision/models/video/swin_transformer.py
torchvision/ops/__init__.py
torchvision/ops/_box_convert.py
torchvision/ops/_register_onnx_ops.py
torchvision/ops/_utils.py
torchvision/ops/boxes.py
torchvision/ops/ciou_loss.py
torchvision/ops/deform_conv.py
torchvision/ops/diou_loss.py
torchvision/ops/drop_block.py
torchvision/ops/feature_pyramid_network.py
torchvision/ops/focal_loss.py
torchvision/ops/giou_loss.py
torchvision/ops/misc.py
torchvision/ops/poolers.py
torchvision/ops/ps_roi_align.py
torchvision/ops/ps_roi_pool.py
torchvision/ops/roi_align.py
torchvision/ops/roi_pool.py
torchvision/ops/stochastic_depth.py
torchvision/transforms/__init__.py
torchvision/transforms/_functional_pil.py
torchvision/transforms/_functional_tensor.py
torchvision/transforms/_functional_video.py
torchvision/transforms/_presets.py
torchvision/transforms/_transforms_video.py
torchvision/transforms/autoaugment.py
torchvision/transforms/functional.py
torchvision/transforms/transforms.py
torchvision/transforms/v2/__init__.py
torchvision/transforms/v2/_augment.py
torchvision/transforms/v2/_auto_augment.py
torchvision/transforms/v2/_color.py
torchvision/transforms/v2/_container.py
torchvision/transforms/v2/_deprecated.py
torchvision/transforms/v2/_geometry.py
torchvision/transforms/v2/_meta.py
torchvision/transforms/v2/_misc.py
torchvision/transforms/v2/_temporal.py
torchvision/transforms/v2/_transform.py
torchvision/transforms/v2/_type_conversion.py
torchvision/transforms/v2/_utils.py
torchvision/transforms/v2/functional/__init__.py
torchvision/transforms/v2/functional/_augment.py
torchvision/transforms/v2/functional/_color.py
torchvision/transforms/v2/functional/_deprecated.py
torchvision/transforms/v2/functional/_geometry.py
torchvision/transforms/v2/functional/_meta.py
torchvision/transforms/v2/functional/_misc.py
torchvision/transforms/v2/functional/_temporal.py
torchvision/transforms/v2/functional/_type_conversion.py
torchvision/transforms/v2/functional/_utils.py
torchvision/tv_tensors/__init__.py
torchvision/tv_tensors/_bounding_boxes.py
torchvision/tv_tensors/_dataset_wrapper.py
torchvision/tv_tensors/_image.py
torchvision/tv_tensors/_mask.py
torchvision/tv_tensors/_torch_function_helpers.py
torchvision/tv_tensors/_tv_tensor.py
torchvision/tv_tensors/_video.py