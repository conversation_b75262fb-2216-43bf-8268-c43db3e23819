//===----------------------------------------------------------------------===//
//
// Part of the CUDA Toolkit, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
// SPDX-FileCopyrightText: Copyright (c) 2024 NVIDIA CORPORATION & AFFILIATES.
//
//===----------------------------------------------------------------------===//

#ifndef _CUDA_MEMORY_RESOURCE
#define _CUDA_MEMORY_RESOURCE

#include <cuda/std/detail/__config>

#if defined(_CCCL_IMPLICIT_SYSTEM_HEADER_GCC)
#  pragma GCC system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_CLANG)
#  pragma clang system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_MSVC)
#  pragma system_header
#endif // no system header

//!@rst
//! @file
//! Defines facilities to allocate and deallocate memory in a type safe manner
//!
//! .. note::
//!
//!    The content of ``<cuda/memory_resource>`` is only available from C++14 onwards. The design is still experimental
//!    and does not provide the usual API stability guarantees. To enable these features define
//!    ``LIBCUDACXX_ENABLE_EXPERIMENTAL_MEMORY_RESOURCE``
//!
//!@endrst

#include <cuda/__memory_resource/get_property.h>
#include <cuda/__memory_resource/properties.h>
#include <cuda/__memory_resource/resource.h>
#include <cuda/__memory_resource/resource_ref.h>

#endif //_LIBCUDACXX_BEGIN_NAMESPACE_CUDA
