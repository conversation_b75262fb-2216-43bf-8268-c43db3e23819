/*
 *  Copyright 2008-2013 NVIDIA Corporation
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

/*! \file binary_search.h
 *  \brief Generic implementations of binary search functions.
 */

#pragma once

#include <thrust/detail/config.h>

#if defined(_CCCL_IMPLICIT_SYSTEM_HEADER_GCC)
#  pragma GCC system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_CLANG)
#  pragma clang system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_MSVC)
#  pragma system_header
#endif // no system header
#include <thrust/system/detail/generic/tag.h>

THRUST_NAMESPACE_BEGIN
namespace system
{
namespace detail
{
namespace generic
{

template <typename DerivedPolicy, typename ForwardIterator, typename T>
_CCCL_HOST_DEVICE ForwardIterator
lower_bound(thrust::execution_policy<DerivedPolicy>& exec, ForwardIterator begin, ForwardIterator end, const T& value);

template <typename DerivedPolicy, typename ForwardIterator, typename T, typename StrictWeakOrdering>
_CCCL_HOST_DEVICE ForwardIterator lower_bound(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator begin,
  ForwardIterator end,
  const T& value,
  StrictWeakOrdering comp);

template <typename DerivedPolicy, typename ForwardIterator, typename T>
_CCCL_HOST_DEVICE ForwardIterator
upper_bound(thrust::execution_policy<DerivedPolicy>& exec, ForwardIterator begin, ForwardIterator end, const T& value);

template <typename DerivedPolicy, typename ForwardIterator, typename T, typename StrictWeakOrdering>
_CCCL_HOST_DEVICE ForwardIterator upper_bound(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator begin,
  ForwardIterator end,
  const T& value,
  StrictWeakOrdering comp);

template <typename DerivedPolicy, typename ForwardIterator, typename T>
_CCCL_HOST_DEVICE bool binary_search(
  thrust::execution_policy<DerivedPolicy>& exec, ForwardIterator begin, ForwardIterator end, const T& value);

template <typename DerivedPolicy, typename ForwardIterator, typename T, typename StrictWeakOrdering>
_CCCL_HOST_DEVICE bool binary_search(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator begin,
  ForwardIterator end,
  const T& value,
  StrictWeakOrdering comp);

template <typename DerivedPolicy, typename ForwardIterator, typename InputIterator, typename OutputIterator>
_CCCL_HOST_DEVICE OutputIterator lower_bound(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator begin,
  ForwardIterator end,
  InputIterator values_begin,
  InputIterator values_end,
  OutputIterator output);

template <typename DerivedPolicy,
          typename ForwardIterator,
          typename InputIterator,
          typename OutputIterator,
          typename StrictWeakOrdering>
_CCCL_HOST_DEVICE OutputIterator lower_bound(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator begin,
  ForwardIterator end,
  InputIterator values_begin,
  InputIterator values_end,
  OutputIterator output,
  StrictWeakOrdering comp);

template <typename DerivedPolicy, typename ForwardIterator, typename InputIterator, typename OutputIterator>
_CCCL_HOST_DEVICE OutputIterator upper_bound(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator begin,
  ForwardIterator end,
  InputIterator values_begin,
  InputIterator values_end,
  OutputIterator output);

template <typename DerivedPolicy,
          typename ForwardIterator,
          typename InputIterator,
          typename OutputIterator,
          typename StrictWeakOrdering>
_CCCL_HOST_DEVICE OutputIterator upper_bound(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator begin,
  ForwardIterator end,
  InputIterator values_begin,
  InputIterator values_end,
  OutputIterator output,
  StrictWeakOrdering comp);

template <typename DerivedPolicy, typename ForwardIterator, typename InputIterator, typename OutputIterator>
_CCCL_HOST_DEVICE OutputIterator binary_search(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator begin,
  ForwardIterator end,
  InputIterator values_begin,
  InputIterator values_end,
  OutputIterator output);

template <typename DerivedPolicy,
          typename ForwardIterator,
          typename InputIterator,
          typename OutputIterator,
          typename StrictWeakOrdering>
_CCCL_HOST_DEVICE OutputIterator binary_search(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator begin,
  ForwardIterator end,
  InputIterator values_begin,
  InputIterator values_end,
  OutputIterator output,
  StrictWeakOrdering comp);

template <typename DerivedPolicy, typename ForwardIterator, typename LessThanComparable>
_CCCL_HOST_DEVICE thrust::pair<ForwardIterator, ForwardIterator> equal_range(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator first,
  ForwardIterator last,
  const LessThanComparable& value);

template <typename DerivedPolicy, typename ForwardIterator, typename LessThanComparable, typename StrictWeakOrdering>
_CCCL_HOST_DEVICE thrust::pair<ForwardIterator, ForwardIterator> equal_range(
  thrust::execution_policy<DerivedPolicy>& exec,
  ForwardIterator first,
  ForwardIterator last,
  const LessThanComparable& value,
  StrictWeakOrdering comp);

} // end namespace generic
} // end namespace detail
} // end namespace system
THRUST_NAMESPACE_END

#include <thrust/system/detail/generic/binary_search.inl>
