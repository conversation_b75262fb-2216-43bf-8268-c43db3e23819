{"build": "mkl", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["mkl"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\blas-1.0-mkl", "files": [], "fn": "blas-1.0-mkl.tar.bz2", "license": "BSD 3-clause", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\blas-1.0-mkl", "type": 1}, "md5": "f621d7bbe874786272e62d72c5a7b4fc", "name": "blas", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\blas-1.0-mkl.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "4d605a266a84dfb4845dcffdca741b26a8e5ec5445254a998bca1fbbc231836f", "size": 1390, "subdir": "win-64", "track_features": "blas_mkl", "url": "https://conda.anaconda.org/conda-forge/win-64/blas-1.0-mkl.tar.bz2", "version": "1.0"}