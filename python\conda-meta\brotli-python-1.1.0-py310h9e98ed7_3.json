{"build": "py310h9e98ed7_3", "build_number": 3, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": ["libbrotlicommon 1.1.0 h2466b09_3"], "depends": ["python >=3.10,<3.11.0a0", "python_abi 3.10.* *_cp310", "ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\brotli-python-1.1.0-py310h9e98ed7_3", "files": ["Lib/site-packages/__pycache__/brotli.cpython-310.pyc", "Lib/site-packages/_brotli.cp310-win_amd64.pyd", "Lib/site-packages/brotli-1.1.0.dist-info/INSTALLER", "Lib/site-packages/brotli-1.1.0.dist-info/METADATA", "Lib/site-packages/brotli-1.1.0.dist-info/RECORD", "Lib/site-packages/brotli-1.1.0.dist-info/REQUESTED", "Lib/site-packages/brotli-1.1.0.dist-info/WHEEL", "Lib/site-packages/brotli-1.1.0.dist-info/direct_url.json", "Lib/site-packages/brotli-1.1.0.dist-info/licenses/LICENSE", "Lib/site-packages/brotli-1.1.0.dist-info/top_level.txt", "Lib/site-packages/brotli.py"], "fn": "brotli-python-1.1.0-py310h9e98ed7_3.conda", "license": "MIT", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\brotli-python-1.1.0-py310h9e98ed7_3", "type": 1}, "md5": "52d37d0f3a9286d295fbf72cf0aa99ee", "name": "brotli-python", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\brotli-python-1.1.0-py310h9e98ed7_3.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/__pycache__/brotli.cpython-310.pyc", "path_type": "hardlink", "sha256": "b654b902e9ef58ec71ca6d39f24d18131efa7e794b989d29c9d64c613c9d53c7", "sha256_in_prefix": "b654b902e9ef58ec71ca6d39f24d18131efa7e794b989d29c9d64c613c9d53c7", "size_in_bytes": 1511}, {"_path": "Lib/site-packages/_brotli.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "03e8b35dcb01642d5f889b205f9f13938e352e6b41e7c21103df7be0dd6cc183", "sha256_in_prefix": "03e8b35dcb01642d5f889b205f9f13938e352e6b41e7c21103df7be0dd6cc183", "size_in_bytes": 821760}, {"_path": "Lib/site-packages/brotli-1.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/brotli-1.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "34fc9d81a6e70c4c0887f897cbb1bf3d9d1a975134ed59179b3ace72a90a098f", "sha256_in_prefix": "34fc9d81a6e70c4c0887f897cbb1bf3d9d1a975134ed59179b3ace72a90a098f", "size_in_bytes": 1526}, {"_path": "Lib/site-packages/brotli-1.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "f878c8b1dc82a9a8815bc47ce131aac4ca1c5c3a9659f05a5e033b857688089e", "sha256_in_prefix": "f878c8b1dc82a9a8815bc47ce131aac4ca1c5c3a9659f05a5e033b857688089e", "size_in_bytes": 856}, {"_path": "Lib/site-packages/brotli-1.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/brotli-1.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "294b810ba97101b1c22a294ab9af11f56fd333bd7ffbd4a0e6e10fdee0d67285", "sha256_in_prefix": "294b810ba97101b1c22a294ab9af11f56fd333bd7ffbd4a0e6e10fdee0d67285", "size_in_bytes": 101}, {"_path": "Lib/site-packages/brotli-1.1.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "4ef8b53053a400a5dd957adf543caac8b732d402e1f836c0ce306f5727b501d0", "sha256_in_prefix": "4ef8b53053a400a5dd957adf543caac8b732d402e1f836c0ce306f5727b501d0", "size_in_bytes": 73}, {"_path": "Lib/site-packages/brotli-1.1.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "3d180008e36922a4e8daec11c34c7af264fed5962d07924aea928c38e8663c94", "sha256_in_prefix": "3d180008e36922a4e8daec11c34c7af264fed5962d07924aea928c38e8663c94", "size_in_bytes": 1084}, {"_path": "Lib/site-packages/brotli-1.1.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "82c4b9e07ae13b766f785c5e32b2a8ffba87e129b84db43b8c62d5044a89e0d2", "sha256_in_prefix": "82c4b9e07ae13b766f785c5e32b2a8ffba87e129b84db43b8c62d5044a89e0d2", "size_in_bytes": 15}, {"_path": "Lib/site-packages/brotli.py", "path_type": "hardlink", "sha256": "3e71885667851851d23b07ab986c28861776172af8e058607a32d09242081620", "sha256_in_prefix": "3e71885667851851d23b07ab986c28861776172af8e058607a32d09242081620", "size_in_bytes": 1866}], "paths_version": 1}, "requested_spec": "None", "sha256": "6eac109d40bd36d158064a552babc3da069662ad93712453eb43320f330b7c82", "size": 321491, "subdir": "win-64", "timestamp": 1749231194000, "url": "https://conda.anaconda.org/conda-forge/win-64/brotli-python-1.1.0-py310h9e98ed7_3.conda", "version": "1.1.0"}