{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\certifi-2025.8.3-pyhd8ed1ab_0", "files": ["Lib/site-packages/certifi-2025.8.3.dist-info/INSTALLER", "Lib/site-packages/certifi-2025.8.3.dist-info/LICENSE", "Lib/site-packages/certifi-2025.8.3.dist-info/METADATA", "Lib/site-packages/certifi-2025.8.3.dist-info/RECORD", "Lib/site-packages/certifi-2025.8.3.dist-info/REQUESTED", "Lib/site-packages/certifi-2025.8.3.dist-info/WHEEL", "Lib/site-packages/certifi-2025.8.3.dist-info/direct_url.json", "Lib/site-packages/certifi-2025.8.3.dist-info/top_level.txt", "Lib/site-packages/certifi/__init__.py", "Lib/site-packages/certifi/__main__.py", "Lib/site-packages/certifi/cacert.pem", "Lib/site-packages/certifi/core.py", "Lib/site-packages/certifi/py.typed", "Lib/site-packages/certifi/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/certifi/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/certifi/__pycache__/core.cpython-310.pyc"], "fn": "certifi-2025.8.3-pyhd8ed1ab_0.conda", "license": "ISC", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\certifi-2025.8.3-pyhd8ed1ab_0", "type": 1}, "md5": "11f59985f49df4620890f3e746ed7102", "name": "certifi", "noarch": "python", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\certifi-2025.8.3-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/certifi-2025.8.3.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/certifi-2025.8.3.dist-info/LICENSE", "path_type": "hardlink", "sha256": "e93716da6b9c0d5a4a1df60fe695b370f0695603d21f6f83f053e42cfc10caf7", "sha256_in_prefix": "e93716da6b9c0d5a4a1df60fe695b370f0695603d21f6f83f053e42cfc10caf7", "size_in_bytes": 989}, {"_path": "site-packages/certifi-2025.8.3.dist-info/METADATA", "path_type": "hardlink", "sha256": "779a192261b54d205ccf60375de1e23bd76e6369a0423f3a0c96bdfa36b8874e", "sha256_in_prefix": "779a192261b54d205ccf60375de1e23bd76e6369a0423f3a0c96bdfa36b8874e", "size_in_bytes": 2400}, {"_path": "site-packages/certifi-2025.8.3.dist-info/RECORD", "path_type": "hardlink", "sha256": "988980d2b3b846d1a78d69ceb2bd3eb99b6e9a1a490cdea0f1c930580682722e", "sha256_in_prefix": "988980d2b3b846d1a78d69ceb2bd3eb99b6e9a1a490cdea0f1c930580682722e", "size_in_bytes": 1195}, {"_path": "site-packages/certifi-2025.8.3.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/certifi-2025.8.3.dist-info/WHEEL", "path_type": "hardlink", "sha256": "227f454cdc5e3fad0a9d3906c3bc24ea624f61dfdd4128c46665dd05d30223ef", "sha256_in_prefix": "227f454cdc5e3fad0a9d3906c3bc24ea624f61dfdd4128c46665dd05d30223ef", "size_in_bytes": 91}, {"_path": "site-packages/certifi-2025.8.3.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "9e88930d9c5f50f92e570111706d5a30ae40a22fecce2644c36caaecacd858d9", "sha256_in_prefix": "9e88930d9c5f50f92e570111706d5a30ae40a22fecce2644c36caaecacd858d9", "size_in_bytes": 111}, {"_path": "site-packages/certifi-2025.8.3.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "28cbb8bd409fb232eb90f6d235d81d7a44bea552730402453bffe723c345ebe5", "sha256_in_prefix": "28cbb8bd409fb232eb90f6d235d81d7a44bea552730402453bffe723c345ebe5", "size_in_bytes": 8}, {"_path": "site-packages/certifi/__init__.py", "path_type": "hardlink", "sha256": "d1ae6ba3829361ea77ecea3467c4f27023d768394e12dbee8f6a4d599ff5b7c6", "sha256_in_prefix": "d1ae6ba3829361ea77ecea3467c4f27023d768394e12dbee8f6a4d599ff5b7c6", "size_in_bytes": 94}, {"_path": "site-packages/certifi/__main__.py", "path_type": "hardlink", "sha256": "c410688fdd394d45812d118034e71fee88ba7beddd30fe1c1281bd3b232cd758", "sha256_in_prefix": "c410688fdd394d45812d118034e71fee88ba7beddd30fe1c1281bd3b232cd758", "size_in_bytes": 243}, {"_path": "site-packages/certifi/cacert.pem", "path_type": "hardlink", "sha256": "9102e6a3644a071ba6cdbd4a53698f291c4a64b18450a08bc046548b6db5cc8b", "sha256_in_prefix": "9102e6a3644a071ba6cdbd4a53698f291c4a64b18450a08bc046548b6db5cc8b", "size_in_bytes": 287634}, {"_path": "site-packages/certifi/core.py", "path_type": "hardlink", "sha256": "5c55f2727746e697f7edac9e17c377d8752e0da7ecca191531b3b80403d61dad", "sha256_in_prefix": "5c55f2727746e697f7edac9e17c377d8752e0da7ecca191531b3b80403d61dad", "size_in_bytes": 3394}, {"_path": "site-packages/certifi/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/certifi/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/certifi/__pycache__/__main__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/certifi/__pycache__/core.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "a1ad5b0a2a242f439608f22a538d2175cac4444b7b3f4e2b8c090ac337aaea40", "size": 158692, "subdir": "noarch", "timestamp": 1754231530000, "url": "https://conda.anaconda.org/conda-forge/noarch/certifi-2025.8.3-pyhd8ed1ab_0.conda", "version": "2025.8.3"}