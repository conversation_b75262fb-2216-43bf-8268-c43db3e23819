{"build": "py310ha8f682b_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["pyc<PERSON><PERSON>", "python >=3.10,<3.11.0a0", "python_abi 3.10.* *_cp310", "ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cffi-1.17.1-py310ha8f682b_0", "files": ["Lib/site-packages/_cffi_backend.cp310-win_amd64.pyd", "Lib/site-packages/cffi-1.17.1.dist-info/INSTALLER", "Lib/site-packages/cffi-1.17.1.dist-info/LICENSE", "Lib/site-packages/cffi-1.17.1.dist-info/METADATA", "Lib/site-packages/cffi-1.17.1.dist-info/RECORD", "Lib/site-packages/cffi-1.17.1.dist-info/REQUESTED", "Lib/site-packages/cffi-1.17.1.dist-info/WHEEL", "Lib/site-packages/cffi-1.17.1.dist-info/direct_url.json", "Lib/site-packages/cffi-1.17.1.dist-info/entry_points.txt", "Lib/site-packages/cffi-1.17.1.dist-info/top_level.txt", "Lib/site-packages/cffi/__init__.py", "Lib/site-packages/cffi/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/_imp_emulation.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/_shimmed_dist_utils.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/api.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/backend_ctypes.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/cffi_opcode.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/commontypes.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/cparser.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/error.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/ffiplatform.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/lock.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/model.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/pkgconfig.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/recompiler.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/setuptools_ext.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/vengine_cpy.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/vengine_gen.cpython-310.pyc", "Lib/site-packages/cffi/__pycache__/verifier.cpython-310.pyc", "Lib/site-packages/cffi/_cffi_errors.h", "Lib/site-packages/cffi/_cffi_include.h", "Lib/site-packages/cffi/_embedding.h", "Lib/site-packages/cffi/_imp_emulation.py", "Lib/site-packages/cffi/_shimmed_dist_utils.py", "Lib/site-packages/cffi/api.py", "Lib/site-packages/cffi/backend_ctypes.py", "Lib/site-packages/cffi/cffi_opcode.py", "Lib/site-packages/cffi/commontypes.py", "Lib/site-packages/cffi/cparser.py", "Lib/site-packages/cffi/error.py", "Lib/site-packages/cffi/ffiplatform.py", "Lib/site-packages/cffi/lock.py", "Lib/site-packages/cffi/model.py", "Lib/site-packages/cffi/parse_c_type.h", "Lib/site-packages/cffi/pkgconfig.py", "Lib/site-packages/cffi/recompiler.py", "Lib/site-packages/cffi/setuptools_ext.py", "Lib/site-packages/cffi/vengine_cpy.py", "Lib/site-packages/cffi/vengine_gen.py", "Lib/site-packages/cffi/verifier.py"], "fn": "cffi-1.17.1-py310ha8f682b_0.conda", "license": "MIT", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cffi-1.17.1-py310ha8f682b_0", "type": 1}, "md5": "9c7ec967f4ae263aec56cff05bdbfc07", "name": "cffi", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cffi-1.17.1-py310ha8f682b_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/_cffi_backend.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "8a7b794f8941d4a9df8773165d8a64c6250750ff8f23bf4f5d0654b6813113fd", "sha256_in_prefix": "8a7b794f8941d4a9df8773165d8a64c6250750ff8f23bf4f5d0654b6813113fd", "size_in_bytes": 181760}, {"_path": "Lib/site-packages/cffi-1.17.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/cffi-1.17.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "04b80f5b077bbed68808cfebadeb5e3523f2a8c9a96495c587bd96df1eac2a33", "sha256_in_prefix": "04b80f5b077bbed68808cfebadeb5e3523f2a8c9a96495c587bd96df1eac2a33", "size_in_bytes": 1294}, {"_path": "Lib/site-packages/cffi-1.17.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "6af26bbe8fa450dc7a897244699563197372e36412f987e334774975e8819457", "sha256_in_prefix": "6af26bbe8fa450dc7a897244699563197372e36412f987e334774975e8819457", "size_in_bytes": 1571}, {"_path": "Lib/site-packages/cffi-1.17.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "d6ff5b4f8d6a33637bc962d0486c864c13ab2efac643e4deb33b5623c784bf6b", "sha256_in_prefix": "d6ff5b4f8d6a33637bc962d0486c864c13ab2efac643e4deb33b5623c784bf6b", "size_in_bytes": 3352}, {"_path": "Lib/site-packages/cffi-1.17.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/cffi-1.17.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "4d85037c7059428776f786acd71eeba3ddbc1e4f964863ec3d93d562bc1cfdb2", "sha256_in_prefix": "4d85037c7059428776f786acd71eeba3ddbc1e4f964863ec3d93d562bc1cfdb2", "size_in_bytes": 101}, {"_path": "Lib/site-packages/cffi-1.17.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "91019c56d8871ef633b831ba372833f1b08e1c40e0f92d4a618aaec1b80d578b", "sha256_in_prefix": "91019c56d8871ef633b831ba372833f1b08e1c40e0f92d4a618aaec1b80d578b", "size_in_bytes": 65}, {"_path": "Lib/site-packages/cffi-1.17.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "cba8d3c67c9eb8b9cbf9725c0eff2e30bde7eb0c98886460f0c4e9fd0189f47a", "sha256_in_prefix": "cba8d3c67c9eb8b9cbf9725c0eff2e30bde7eb0c98886460f0c4e9fd0189f47a", "size_in_bytes": 75}, {"_path": "Lib/site-packages/cffi-1.17.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "ac4ed6477ad97cd2b1588f7e8e7ea1b0708097b303901f859ae41bc568c57a14", "sha256_in_prefix": "ac4ed6477ad97cd2b1588f7e8e7ea1b0708097b303901f859ae41bc568c57a14", "size_in_bytes": 19}, {"_path": "Lib/site-packages/cffi/__init__.py", "path_type": "hardlink", "sha256": "1fab7f79bbdae84787a54b88b452d6d606d1a7de1e6513493832da58a75bc752", "sha256_in_prefix": "1fab7f79bbdae84787a54b88b452d6d606d1a7de1e6513493832da58a75bc752", "size_in_bytes": 513}, {"_path": "Lib/site-packages/cffi/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "cce735c61178e69f2ac7f8a61896ca03a70ab1b5e64cfce4c66b32cc3bfc8860", "sha256_in_prefix": "cce735c61178e69f2ac7f8a61896ca03a70ab1b5e64cfce4c66b32cc3bfc8860", "size_in_bytes": 468}, {"_path": "Lib/site-packages/cffi/__pycache__/_imp_emulation.cpython-310.pyc", "path_type": "hardlink", "sha256": "590c30cea02683923c261d321bd532016df3669d9ba048f60f495b1e5eab34da", "sha256_in_prefix": "590c30cea02683923c261d321bd532016df3669d9ba048f60f495b1e5eab34da", "size_in_bytes": 2443}, {"_path": "Lib/site-packages/cffi/__pycache__/_shimmed_dist_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "ed11346c81a094b3eaf3e2df356f1d0b950f8aed36612e8eab519f84c1ba926d", "sha256_in_prefix": "ed11346c81a094b3eaf3e2df356f1d0b950f8aed36612e8eab519f84c1ba926d", "size_in_bytes": 1692}, {"_path": "Lib/site-packages/cffi/__pycache__/api.cpython-310.pyc", "path_type": "hardlink", "sha256": "d4c7487855a8ba7dc0037be2b9d8eaf09b8fbbb4c7b3adde42d778dff042ce87", "sha256_in_prefix": "d4c7487855a8ba7dc0037be2b9d8eaf09b8fbbb4c7b3adde42d778dff042ce87", "size_in_bytes": 34335}, {"_path": "Lib/site-packages/cffi/__pycache__/backend_ctypes.cpython-310.pyc", "path_type": "hardlink", "sha256": "febad28df119071049b68b63efae9a242d76444967e921f6f8f43fe982618dbf", "sha256_in_prefix": "febad28df119071049b68b63efae9a242d76444967e921f6f8f43fe982618dbf", "size_in_bytes": 39551}, {"_path": "Lib/site-packages/cffi/__pycache__/cffi_opcode.cpython-310.pyc", "path_type": "hardlink", "sha256": "515c90b7e4eded7e32e9b9fdd30472f668b6e256f03c4c09d3c6b9a8a566013d", "sha256_in_prefix": "515c90b7e4eded7e32e9b9fdd30472f668b6e256f03c4c09d3c6b9a8a566013d", "size_in_bytes": 5036}, {"_path": "Lib/site-packages/cffi/__pycache__/commontypes.cpython-310.pyc", "path_type": "hardlink", "sha256": "81933e2866d135445ed3500173b2459e5c8ecdb04c1919a1e7c05b536f7e5339", "sha256_in_prefix": "81933e2866d135445ed3500173b2459e5c8ecdb04c1919a1e7c05b536f7e5339", "size_in_bytes": 1955}, {"_path": "Lib/site-packages/cffi/__pycache__/cparser.cpython-310.pyc", "path_type": "hardlink", "sha256": "8fbc24a3f18086faf30a3807e709853f0d23472d9a5bae89d13c5f792b3ccdff", "sha256_in_prefix": "8fbc24a3f18086faf30a3807e709853f0d23472d9a5bae89d13c5f792b3ccdff", "size_in_bytes": 23897}, {"_path": "Lib/site-packages/cffi/__pycache__/error.cpython-310.pyc", "path_type": "hardlink", "sha256": "0e9eab559c34b777ee542595a06799f9ceeb7482d9a569f6572a565f623efd40", "sha256_in_prefix": "0e9eab559c34b777ee542595a06799f9ceeb7482d9a569f6572a565f623efd40", "size_in_bytes": 1435}, {"_path": "Lib/site-packages/cffi/__pycache__/ffiplatform.cpython-310.pyc", "path_type": "hardlink", "sha256": "b94740365e03f947545f65ca9189a198e09ad19dd7b5f6ef1319ca7cc4ce684c", "sha256_in_prefix": "b94740365e03f947545f65ca9189a198e09ad19dd7b5f6ef1319ca7cc4ce684c", "size_in_bytes": 3264}, {"_path": "Lib/site-packages/cffi/__pycache__/lock.cpython-310.pyc", "path_type": "hardlink", "sha256": "7e71ba7e73b88dc9a2373f64220df4117ac035f846271a629aedbf8d29fb83c9", "sha256_in_prefix": "7e71ba7e73b88dc9a2373f64220df4117ac035f846271a629aedbf8d29fb83c9", "size_in_bytes": 372}, {"_path": "Lib/site-packages/cffi/__pycache__/model.cpython-310.pyc", "path_type": "hardlink", "sha256": "92a750d369777a10a616608af0d12ff3bd380c5ff69beae499080c08b47fcf48", "sha256_in_prefix": "92a750d369777a10a616608af0d12ff3bd380c5ff69beae499080c08b47fcf48", "size_in_bytes": 19817}, {"_path": "Lib/site-packages/cffi/__pycache__/pkgconfig.cpython-310.pyc", "path_type": "hardlink", "sha256": "c8601d9089a19073285450a58e7074b5cc9d8f2056c2d5d6cd9011dcf371ac30", "sha256_in_prefix": "c8601d9089a19073285450a58e7074b5cc9d8f2056c2d5d6cd9011dcf371ac30", "size_in_bytes": 4906}, {"_path": "Lib/site-packages/cffi/__pycache__/recompiler.cpython-310.pyc", "path_type": "hardlink", "sha256": "c9fbb7664395b44255241e9731826f96214eb2a2474cda30ee7a341e45bfbcca", "sha256_in_prefix": "c9fbb7664395b44255241e9731826f96214eb2a2474cda30ee7a341e45bfbcca", "size_in_bytes": 46857}, {"_path": "Lib/site-packages/cffi/__pycache__/setuptools_ext.cpython-310.pyc", "path_type": "hardlink", "sha256": "8a90b20f8f171330ae14fd3ced5b9bb84abbccbb72b9ccbfa0e0f3a5a87fa50e", "sha256_in_prefix": "8a90b20f8f171330ae14fd3ced5b9bb84abbccbb72b9ccbfa0e0f3a5a87fa50e", "size_in_bytes": 7177}, {"_path": "Lib/site-packages/cffi/__pycache__/vengine_cpy.cpython-310.pyc", "path_type": "hardlink", "sha256": "f9c96f8b4cc75804c31cc93a3e623405f213bfac7fd2e00e54f9aa307c320c79", "sha256_in_prefix": "f9c96f8b4cc75804c31cc93a3e623405f213bfac7fd2e00e54f9aa307c320c79", "size_in_bytes": 35698}, {"_path": "Lib/site-packages/cffi/__pycache__/vengine_gen.cpython-310.pyc", "path_type": "hardlink", "sha256": "6199012f2e9c7f2fc05d5ca01c00b88559d294297b39a2369b2ed3aac9857b1c", "sha256_in_prefix": "6199012f2e9c7f2fc05d5ca01c00b88559d294297b39a2369b2ed3aac9857b1c", "size_in_bytes": 21155}, {"_path": "Lib/site-packages/cffi/__pycache__/verifier.cpython-310.pyc", "path_type": "hardlink", "sha256": "02c841cf35349e00d0fffbfd29a4336d64523087505ccc3970d275e28811303a", "sha256_in_prefix": "02c841cf35349e00d0fffbfd29a4336d64523087505ccc3970d275e28811303a", "size_in_bytes": 9299}, {"_path": "Lib/site-packages/cffi/_cffi_errors.h", "path_type": "hardlink", "sha256": "cd05edeee47f9bc8145be7c8da1260d0aa129091705eff111949040d9d7bedd4", "sha256_in_prefix": "cd05edeee47f9bc8145be7c8da1260d0aa129091705eff111949040d9d7bedd4", "size_in_bytes": 3908}, {"_path": "Lib/site-packages/cffi/_cffi_include.h", "path_type": "hardlink", "sha256": "131866826f6acc75b35a2be37d37b40fb5e9e2b3d4915c5d36ec0684c4cccdbc", "sha256_in_prefix": "131866826f6acc75b35a2be37d37b40fb5e9e2b3d4915c5d36ec0684c4cccdbc", "size_in_bytes": 15055}, {"_path": "Lib/site-packages/cffi/_embedding.h", "path_type": "hardlink", "sha256": "1032b0e50acbbd0a1edeea2c5c1dc7d713d54d8c6c9f7dde577038df3b00fc5c", "sha256_in_prefix": "1032b0e50acbbd0a1edeea2c5c1dc7d713d54d8c6c9f7dde577038df3b00fc5c", "size_in_bytes": 18787}, {"_path": "Lib/site-packages/cffi/_imp_emulation.py", "path_type": "hardlink", "sha256": "4714441bccc06c8d913c6070c3dd2eff97e2f2c59d6a1a5d8a93a83f3929ec2d", "sha256_in_prefix": "4714441bccc06c8d913c6070c3dd2eff97e2f2c59d6a1a5d8a93a83f3929ec2d", "size_in_bytes": 2960}, {"_path": "Lib/site-packages/cffi/_shimmed_dist_utils.py", "path_type": "hardlink", "sha256": "0638f6c26f3265bbc5bd6131e4011f9aa6aa6726458587e8c8b2d01e45d9b9aa", "sha256_in_prefix": "0638f6c26f3265bbc5bd6131e4011f9aa6aa6726458587e8c8b2d01e45d9b9aa", "size_in_bytes": 2230}, {"_path": "Lib/site-packages/cffi/api.py", "path_type": "hardlink", "sha256": "6a506fea1650923a66669941a6175a467da53cef7e08e46cfccee2c5a6ef6562", "sha256_in_prefix": "6a506fea1650923a66669941a6175a467da53cef7e08e46cfccee2c5a6ef6562", "size_in_bytes": 42169}, {"_path": "Lib/site-packages/cffi/backend_ctypes.py", "path_type": "hardlink", "sha256": "879648ccb73a0455579c6c9cf713ea656512eea1b2ef21520ea5deebc49af33e", "sha256_in_prefix": "879648ccb73a0455579c6c9cf713ea656512eea1b2ef21520ea5deebc49af33e", "size_in_bytes": 42454}, {"_path": "Lib/site-packages/cffi/cffi_opcode.py", "path_type": "hardlink", "sha256": "243579974474fce69d057fee13bc4f3d362d31d9a9a7c23d51877a6afeda8835", "sha256_in_prefix": "243579974474fce69d057fee13bc4f3d362d31d9a9a7c23d51877a6afeda8835", "size_in_bytes": 5731}, {"_path": "Lib/site-packages/cffi/commontypes.py", "path_type": "hardlink", "sha256": "ecdeb33ed08596fc57316847574f29b148dd6082b65e0b0ddf2a39760b9afefe", "sha256_in_prefix": "ecdeb33ed08596fc57316847574f29b148dd6082b65e0b0ddf2a39760b9afefe", "size_in_bytes": 2805}, {"_path": "Lib/site-packages/cffi/cparser.py", "path_type": "hardlink", "sha256": "d2a237984cd948d55c09a9e0a325cea0070bf9186940bd3c786f3bf7c4f4db8b", "sha256_in_prefix": "d2a237984cd948d55c09a9e0a325cea0070bf9186940bd3c786f3bf7c4f4db8b", "size_in_bytes": 44789}, {"_path": "Lib/site-packages/cffi/error.py", "path_type": "hardlink", "sha256": "bfac53892e14d24bc3732e21fc10d1a39bf7f5942e8fe20c4582efe444dd759b", "sha256_in_prefix": "bfac53892e14d24bc3732e21fc10d1a39bf7f5942e8fe20c4582efe444dd759b", "size_in_bytes": 877}, {"_path": "Lib/site-packages/cffi/ffiplatform.py", "path_type": "hardlink", "sha256": "6afc458dd8a460626812d9893bb7b0566c06fd511597a119fd668d859602aafe", "sha256_in_prefix": "6afc458dd8a460626812d9893bb7b0566c06fd511597a119fd668d859602aafe", "size_in_bytes": 3584}, {"_path": "Lib/site-packages/cffi/lock.py", "path_type": "hardlink", "sha256": "97d4d37703083298ba8c39091a742013d72f4c847b0809ed209afc1061edde96", "sha256_in_prefix": "97d4d37703083298ba8c39091a742013d72f4c847b0809ed209afc1061edde96", "size_in_bytes": 747}, {"_path": "Lib/site-packages/cffi/model.py", "path_type": "hardlink", "sha256": "5b7d14150644ef78cbe4cc7937cd584fbeeeb365b68898d39b45d87e7c33d5c8", "sha256_in_prefix": "5b7d14150644ef78cbe4cc7937cd584fbeeeb365b68898d39b45d87e7c33d5c8", "size_in_bytes": 21797}, {"_path": "Lib/site-packages/cffi/parse_c_type.h", "path_type": "hardlink", "sha256": "39dc107f033d92dababe5081e377b11509b10c1b63d8c04d74af0b625d79b63c", "sha256_in_prefix": "39dc107f033d92dababe5081e377b11509b10c1b63d8c04d74af0b625d79b63c", "size_in_bytes": 5976}, {"_path": "Lib/site-packages/cffi/pkgconfig.py", "path_type": "hardlink", "sha256": "2cfd70eef996be62b0caa2da535676e3714e58635032b80c519aef805b8e95ca", "sha256_in_prefix": "2cfd70eef996be62b0caa2da535676e3714e58635032b80c519aef805b8e95ca", "size_in_bytes": 4374}, {"_path": "Lib/site-packages/cffi/recompiler.py", "path_type": "hardlink", "sha256": "b229b84e6ee56a6b76267f2ecca374c0c629e8e0c1ca4de0ee87f8efe87d2c3e", "sha256_in_prefix": "b229b84e6ee56a6b76267f2ecca374c0c629e8e0c1ca4de0ee87f8efe87d2c3e", "size_in_bytes": 65367}, {"_path": "Lib/site-packages/cffi/setuptools_ext.py", "path_type": "hardlink", "sha256": "f9e6e3efd94edbf0141fe91171a8dada9298d59ff9b65a06c2c260ccaf0fdc27", "sha256_in_prefix": "f9e6e3efd94edbf0141fe91171a8dada9298d59ff9b65a06c2c260ccaf0fdc27", "size_in_bytes": 8871}, {"_path": "Lib/site-packages/cffi/vengine_cpy.py", "path_type": "hardlink", "sha256": "f146a04fa6443997fa0e3bbbfc27cdba5b9ef029ec1cb133258867a94868174e", "sha256_in_prefix": "f146a04fa6443997fa0e3bbbfc27cdba5b9ef029ec1cb133258867a94868174e", "size_in_bytes": 43752}, {"_path": "Lib/site-packages/cffi/vengine_gen.py", "path_type": "hardlink", "sha256": "0d494422b0e25629f53e7867d6c7e86a69d2e4d2ea7c970e76146879235e2518", "sha256_in_prefix": "0d494422b0e25629f53e7867d6c7e86a69d2e4d2ea7c970e76146879235e2518", "size_in_bytes": 26939}, {"_path": "Lib/site-packages/cffi/verifier.py", "path_type": "hardlink", "sha256": "a17f23a5aa21836426dda1dcce789d01dbeb566e4de2c4181b46b7128e66225e", "sha256_in_prefix": "a17f23a5aa21836426dda1dcce789d01dbeb566e4de2c4181b46b7128e66225e", "size_in_bytes": 11182}], "paths_version": 1}, "requested_spec": "None", "sha256": "32638e79658f76e3700f783c519025290110f207833ae1d166d262572cbec8a8", "size": 238887, "subdir": "win-64", "timestamp": 1725561032000, "url": "https://conda.anaconda.org/conda-forge/win-64/cffi-1.17.1-py310ha8f682b_0.conda", "version": "1.17.1"}