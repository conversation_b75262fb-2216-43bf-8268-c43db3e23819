{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-cccl_win-64 12.9.27", "cuda-version >=12.9,<12.10.0a0"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cccl-12.9.27-0", "files": [], "fn": "cuda-cccl-12.9.27-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cccl-12.9.27-0", "type": 1}, "md5": "f9bfa5069ee62bfd0f488679be236598", "name": "cuda-cccl", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cccl-12.9.27-0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "a92528a2461f632def1863396f0e48439ca075569a6c9d6a00562f2f5947fc88", "size": 16858, "subdir": "win-64", "timestamp": 1742008492000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-cccl-12.9.27-0.conda", "version": "12.9.27"}