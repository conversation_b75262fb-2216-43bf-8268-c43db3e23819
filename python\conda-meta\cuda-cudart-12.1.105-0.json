{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": [], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cudart-12.1.105-0", "files": ["lib/x64/cuda.lib", "lib/x64/cudadevrt.lib", "lib/x64/cudart.lib", "lib/x64/cudart_static.lib"], "fn": "cuda-cudart-12.1.105-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cudart-12.1.105-0", "type": 1}, "md5": "78c814d921464c0c5c5dabd101941784", "name": "cuda-cudart", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cudart-12.1.105-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/cuda.lib", "path_type": "hardlink", "sha256": "cd209396fce7d1ed725701b6f52a69c6bb6db0cccf1fb923728bec22db0b5102", "sha256_in_prefix": "cd209396fce7d1ed725701b6f52a69c6bb6db0cccf1fb923728bec22db0b5102", "size_in_bytes": 145306}, {"_path": "lib/x64/cudadevrt.lib", "path_type": "hardlink", "sha256": "aab010a849d6e968e28e45b0efca008e3a8c39f7f54e71c8b391984417a39e80", "sha256_in_prefix": "aab010a849d6e968e28e45b0efca008e3a8c39f7f54e71c8b391984417a39e80", "size_in_bytes": 1606230}, {"_path": "lib/x64/cudart.lib", "path_type": "hardlink", "sha256": "552cdbf82d3d1803608f4a06c32ad44ccb8bedf042afa7404ea853740846ae81", "sha256_in_prefix": "552cdbf82d3d1803608f4a06c32ad44ccb8bedf042afa7404ea853740846ae81", "size_in_bytes": 106556}, {"_path": "lib/x64/cudart_static.lib", "path_type": "hardlink", "sha256": "b145fc0b71f3b4c7256d8e83428c920fe3fe29a6cbde8c4d7ea46c988cf8cd9f", "sha256_in_prefix": "b145fc0b71f3b4c7256d8e83428c920fe3fe29a6cbde8c4d7ea46c988cf8cd9f", "size_in_bytes": 2603756}], "paths_version": 1}, "requested_spec": "None", "sha256": "76430a7d185b2980702266042bd5af6b8e934f28e22f9c1a2d290812fbc1bc25", "size": 987484, "subdir": "win-64", "timestamp": 1680574584000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-cudart-12.1.105-0.tar.bz2", "version": "12.1.105"}