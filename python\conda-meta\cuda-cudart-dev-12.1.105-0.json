{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-cccl", "cuda-cudart >=12.1.105"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cudart-dev-12.1.105-0", "files": ["bin/cudart64_12.dll", "include/builtin_types.h", "include/channel_descriptor.h", "include/common_functions.h", "include/cooperative_groups.h", "include/cooperative_groups/details/async.h", "include/cooperative_groups/details/coalesced_reduce.h", "include/cooperative_groups/details/coalesced_scan.h", "include/cooperative_groups/details/driver_abi.h", "include/cooperative_groups/details/functional.h", "include/cooperative_groups/details/helpers.h", "include/cooperative_groups/details/info.h", "include/cooperative_groups/details/invoke.h", "include/cooperative_groups/details/memory.h", "include/cooperative_groups/details/partitioning.h", "include/cooperative_groups/details/reduce.h", "include/cooperative_groups/details/scan.h", "include/cooperative_groups/details/sync.h", "include/cooperative_groups/memcpy_async.h", "include/cooperative_groups/reduce.h", "include/cooperative_groups/scan.h", "include/cuComplex.h", "include/cuda.h", "include/cudaD3D10.h", "include/cudaD3D10Typedefs.h", "include/cudaD3D11.h", "include/cudaD3D11Typedefs.h", "include/cudaD3D9.h", "include/cudaD3D9Typedefs.h", "include/cudaGL.h", "include/cudaGLTypedefs.h", "include/cudaProfilerTypedefs.h", "include/cudaTypedefs.h", "include/cuda_awbarrier.h", "include/cuda_awbarrier_helpers.h", "include/cuda_awbarrier_primitives.h", "include/cuda_bf16.h", "include/cuda_bf16.hpp", "include/cuda_d3d10_interop.h", "include/cuda_d3d11_interop.h", "include/cuda_d3d9_interop.h", "include/cuda_device_runtime_api.h", "include/cuda_egl_interop.h", "include/cuda_fp16.h", "include/cuda_fp16.hpp", "include/cuda_fp8.h", "include/cuda_fp8.hpp", "include/cuda_gl_interop.h", "include/cuda_occupancy.h", "include/cuda_pipeline.h", "include/cuda_pipeline_helpers.h", "include/cuda_pipeline_primitives.h", "include/cuda_runtime.h", "include/cuda_runtime_api.h", "include/cuda_surface_types.h", "include/cuda_texture_types.h", "include/cudart_platform.h", "include/device_atomic_functions.h", "include/device_atomic_functions.hpp", "include/device_double_functions.h", "include/device_functions.h", "include/device_launch_parameters.h", "include/device_types.h", "include/driver_functions.h", "include/driver_types.h", "include/host_config.h", "include/host_defines.h", "include/library_types.h", "include/math_constants.h", "include/math_functions.h", "include/mma.h", "include/nvfunctional", "include/sm_20_atomic_functions.h", "include/sm_20_atomic_functions.hpp", "include/sm_20_intrinsics.h", "include/sm_20_intrinsics.hpp", "include/sm_30_intrinsics.h", "include/sm_30_intrinsics.hpp", "include/sm_32_atomic_functions.h", "include/sm_32_atomic_functions.hpp", "include/sm_32_intrinsics.h", "include/sm_32_intrinsics.hpp", "include/sm_35_atomic_functions.h", "include/sm_35_intrinsics.h", "include/sm_60_atomic_functions.h", "include/sm_60_atomic_functions.hpp", "include/sm_61_intrinsics.h", "include/sm_61_intrinsics.hpp", "include/surface_functions.h", "include/surface_indirect_functions.h", "include/surface_types.h", "include/texture_fetch_functions.h", "include/texture_indirect_functions.h", "include/texture_types.h", "include/vector_functions.h", "include/vector_functions.hpp", "include/vector_types.h"], "fn": "cuda-cudart-dev-12.1.105-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cudart-dev-12.1.105-0", "type": 1}, "md5": "bb69943b0434bbabc57f434e103b81a3", "name": "cuda-cudart-dev", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cudart-dev-12.1.105-0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/cudart64_12.dll", "path_type": "hardlink", "sha256": "f86bc7f6043b5441f9686862403db35ea2006d87381a359deea3599804c6b83e", "sha256_in_prefix": "f86bc7f6043b5441f9686862403db35ea2006d87381a359deea3599804c6b83e", "size_in_bytes": 527872}, {"_path": "include/builtin_types.h", "path_type": "hardlink", "sha256": "e75d7426f7b4e8abcd6e3cdd0631fa9577a6c5f8cd31f1ff2996e79d9b5ab4a3", "sha256_in_prefix": "e75d7426f7b4e8abcd6e3cdd0631fa9577a6c5f8cd31f1ff2996e79d9b5ab4a3", "size_in_bytes": 3214}, {"_path": "include/channel_descriptor.h", "path_type": "hardlink", "sha256": "99a8b1ded9c9b2ae52e10f3db44a407db49515c98c0c8362609e6602d3eba7b0", "sha256_in_prefix": "99a8b1ded9c9b2ae52e10f3db44a407db49515c98c0c8362609e6602d3eba7b0", "size_in_bytes": 22070}, {"_path": "include/common_functions.h", "path_type": "hardlink", "sha256": "8c2f53f4d2b46fcf8adffb1403d15568ec39be63bd98c2f26fef2d670f056dd7", "sha256_in_prefix": "8c2f53f4d2b46fcf8adffb1403d15568ec39be63bd98c2f26fef2d670f056dd7", "size_in_bytes": 3475}, {"_path": "include/cooperative_groups.h", "path_type": "hardlink", "sha256": "34a9e6d85c60e106471fb9a8b59deee856b6464beec97d04deeb2a9fc1d07c27", "sha256_in_prefix": "34a9e6d85c60e106471fb9a8b59deee856b6464beec97d04deeb2a9fc1d07c27", "size_in_bytes": 61018}, {"_path": "include/cooperative_groups/details/async.h", "path_type": "hardlink", "sha256": "fa8fdafc1a55c292b45506c7276c8b8d26952e073c386c5ac52f5cfaf2cc5ef1", "sha256_in_prefix": "fa8fdafc1a55c292b45506c7276c8b8d26952e073c386c5ac52f5cfaf2cc5ef1", "size_in_bytes": 19574}, {"_path": "include/cooperative_groups/details/coalesced_reduce.h", "path_type": "hardlink", "sha256": "ac47c18e8cab24327e66cd102ca377ec9f72d7e59e3dca7b9fa3d7890ca3d7b8", "sha256_in_prefix": "ac47c18e8cab24327e66cd102ca377ec9f72d7e59e3dca7b9fa3d7890ca3d7b8", "size_in_bytes": 4669}, {"_path": "include/cooperative_groups/details/coalesced_scan.h", "path_type": "hardlink", "sha256": "3273c657f293c1cd66096a066120c5512fdd391715de323ee55ffce1ee22d6ba", "sha256_in_prefix": "3273c657f293c1cd66096a066120c5512fdd391715de323ee55ffce1ee22d6ba", "size_in_bytes": 7487}, {"_path": "include/cooperative_groups/details/driver_abi.h", "path_type": "hardlink", "sha256": "234b5d0be27516cc2f34d2243f7e64469cc779d63d432abd0e6dae77303efcf8", "sha256_in_prefix": "234b5d0be27516cc2f34d2243f7e64469cc779d63d432abd0e6dae77303efcf8", "size_in_bytes": 4063}, {"_path": "include/cooperative_groups/details/functional.h", "path_type": "hardlink", "sha256": "d66e968211b98265ee813ff74d2cf94cfc49a514c090c1283432ffdb565f3948", "sha256_in_prefix": "d66e968211b98265ee813ff74d2cf94cfc49a514c090c1283432ffdb565f3948", "size_in_bytes": 9117}, {"_path": "include/cooperative_groups/details/helpers.h", "path_type": "hardlink", "sha256": "607cf35f2870793b56c2f9db43e8737c70fdc2d5e9216007a1765aaeadfb7f1f", "sha256_in_prefix": "607cf35f2870793b56c2f9db43e8737c70fdc2d5e9216007a1765aaeadfb7f1f", "size_in_bytes": 24203}, {"_path": "include/cooperative_groups/details/info.h", "path_type": "hardlink", "sha256": "4243b2c151d8cce8c7e3c425de4ebf0df264dfb27c03a5a032352312d239bcd7", "sha256_in_prefix": "4243b2c151d8cce8c7e3c425de4ebf0df264dfb27c03a5a032352312d239bcd7", "size_in_bytes": 12624}, {"_path": "include/cooperative_groups/details/invoke.h", "path_type": "hardlink", "sha256": "60729e6b39912e209a91d9c63710528c932278e3de9a317614ef02f76df4e2a9", "sha256_in_prefix": "60729e6b39912e209a91d9c63710528c932278e3de9a317614ef02f76df4e2a9", "size_in_bytes": 8805}, {"_path": "include/cooperative_groups/details/memory.h", "path_type": "hardlink", "sha256": "e72e6f470137ddf6948ea28024b38f93bb2e25fd583d59cd552efe81c7f00bbd", "sha256_in_prefix": "e72e6f470137ddf6948ea28024b38f93bb2e25fd583d59cd552efe81c7f00bbd", "size_in_bytes": 5619}, {"_path": "include/cooperative_groups/details/partitioning.h", "path_type": "hardlink", "sha256": "160f4c4770b09085cb3e27c0be7a305317cb934ef3742e104d3a79e37ca9bb38", "sha256_in_prefix": "160f4c4770b09085cb3e27c0be7a305317cb934ef3742e104d3a79e37ca9bb38", "size_in_bytes": 6134}, {"_path": "include/cooperative_groups/details/reduce.h", "path_type": "hardlink", "sha256": "f533a85b95b250ec073a719a1a0f01414a05889a7747aac6707e6bfe3329c78b", "sha256_in_prefix": "f533a85b95b250ec073a719a1a0f01414a05889a7747aac6707e6bfe3329c78b", "size_in_bytes": 23173}, {"_path": "include/cooperative_groups/details/scan.h", "path_type": "hardlink", "sha256": "a455988d98bc04f52cee25b37066533d9a3083e93d37eae1af17f5643164c83c", "sha256_in_prefix": "a455988d98bc04f52cee25b37066533d9a3083e93d37eae1af17f5643164c83c", "size_in_bytes": 17486}, {"_path": "include/cooperative_groups/details/sync.h", "path_type": "hardlink", "sha256": "1e1b1032a92ce9d6ddb1a7d53287d4d4cf47ae678a6de470803ea49a247691e5", "sha256_in_prefix": "1e1b1032a92ce9d6ddb1a7d53287d4d4cf47ae678a6de470803ea49a247691e5", "size_in_bytes": 10607}, {"_path": "include/cooperative_groups/memcpy_async.h", "path_type": "hardlink", "sha256": "0e05ef6e5c38acb6be4f678d23338ba12ddc43c37a9f9a29363f38b1e48c3352", "sha256_in_prefix": "0e05ef6e5c38acb6be4f678d23338ba12ddc43c37a9f9a29363f38b1e48c3352", "size_in_bytes": 3022}, {"_path": "include/cooperative_groups/reduce.h", "path_type": "hardlink", "sha256": "9a86fd41b5ae66ec5abdf03a131c8f13207ae21e0a2e1e4c99fcade6748e647d", "sha256_in_prefix": "9a86fd41b5ae66ec5abdf03a131c8f13207ae21e0a2e1e4c99fcade6748e647d", "size_in_bytes": 3012}, {"_path": "include/cooperative_groups/scan.h", "path_type": "hardlink", "sha256": "c1131b91109572fabf6f8d91a91f1b48c0cee960537b0381aeabb9e02bbf94b9", "sha256_in_prefix": "c1131b91109572fabf6f8d91a91f1b48c0cee960537b0381aeabb9e02bbf94b9", "size_in_bytes": 3003}, {"_path": "include/cuComplex.h", "path_type": "hardlink", "sha256": "8c04f75b56a2d57ad07aa59d8cb903fcabd6366658a89dad00a4b4a00939150f", "sha256_in_prefix": "8c04f75b56a2d57ad07aa59d8cb903fcabd6366658a89dad00a4b4a00939150f", "size_in_bytes": 12534}, {"_path": "include/cuda.h", "path_type": "hardlink", "sha256": "f0eec0cdb09c0af91c1ebaf68bba739a5f02675c6848f0aa5512771aa44b13fc", "sha256_in_prefix": "f0eec0cdb09c0af91c1ebaf68bba739a5f02675c6848f0aa5512771aa44b13fc", "size_in_bytes": 955128}, {"_path": "include/cudaD3D10.h", "path_type": "hardlink", "sha256": "5305ee1d46db529ed2aa94719c8e641a96874e86cdf3db994eb8f4b6b38c65d5", "sha256_in_prefix": "5305ee1d46db529ed2aa94719c8e641a96874e86cdf3db994eb8f4b6b38c65d5", "size_in_bytes": 32930}, {"_path": "include/cudaD3D10Typedefs.h", "path_type": "hardlink", "sha256": "f4eeb57be3783e56086f3b4f7aa3349d170b27f33e7575fcb16e1d29f1d5226c", "sha256_in_prefix": "f4eeb57be3783e56086f3b4f7aa3349d170b27f33e7575fcb16e1d29f1d5226c", "size_in_bytes": 7333}, {"_path": "include/cudaD3D11.h", "path_type": "hardlink", "sha256": "895853fb979ba1f086fdf706f8a13ca8eff35f613391badadfb590ecb5cdd9bb", "sha256_in_prefix": "895853fb979ba1f086fdf706f8a13ca8eff35f613391badadfb590ecb5cdd9bb", "size_in_bytes": 14444}, {"_path": "include/cudaD3D11Typedefs.h", "path_type": "hardlink", "sha256": "edb21242e2ab914b8c658da0463dedb786cf34995926629669ffe39b11ee8965", "sha256_in_prefix": "edb21242e2ab914b8c658da0463dedb786cf34995926629669ffe39b11ee8965", "size_in_bytes": 4484}, {"_path": "include/cudaD3D9.h", "path_type": "hardlink", "sha256": "a6f4d42a14dae5c56dfdc3abce11e14a7f84af40ec71e1c6696d4969447119fa", "sha256_in_prefix": "a6f4d42a14dae5c56dfdc3abce11e14a7f84af40ec71e1c6696d4969447119fa", "size_in_bytes": 37091}, {"_path": "include/cudaD3D9Typedefs.h", "path_type": "hardlink", "sha256": "e2e94f7c2d0f4bf23ccbca2e18854899fbcc0aab34a886be748959b2a2e2908f", "sha256_in_prefix": "e2e94f7c2d0f4bf23ccbca2e18854899fbcc0aab34a886be748959b2a2e2908f", "size_in_bytes": 8533}, {"_path": "include/cudaGL.h", "path_type": "hardlink", "sha256": "299bf3c583aca0eba8344263a33c596e07bf6647b7f2ed125af9040381d5f1f7", "sha256_in_prefix": "299bf3c583aca0eba8344263a33c596e07bf6647b7f2ed125af9040381d5f1f7", "size_in_bytes": 23109}, {"_path": "include/cudaGLTypedefs.h", "path_type": "hardlink", "sha256": "e52f155f43813a55a6a8d361731cef9058d4563cd98a330ab4a87621704af543", "sha256_in_prefix": "e52f155f43813a55a6a8d361731cef9058d4563cd98a330ab4a87621704af543", "size_in_bytes": 6699}, {"_path": "include/cudaProfilerTypedefs.h", "path_type": "hardlink", "sha256": "c8983a9a436d2ffbda06c32d6ea4fbcd6a25aa3eb20b0c4d04da8b97d4030eec", "sha256_in_prefix": "c8983a9a436d2ffbda06c32d6ea4fbcd6a25aa3eb20b0c4d04da8b97d4030eec", "size_in_bytes": 3375}, {"_path": "include/cudaTypedefs.h", "path_type": "hardlink", "sha256": "6f745d04995433ba9df3459b10a56d3585e34a788ff392e016af2da34928bade", "sha256_in_prefix": "6f745d04995433ba9df3459b10a56d3585e34a788ff392e016af2da34928bade", "size_in_bytes": 102884}, {"_path": "include/cuda_awbarrier.h", "path_type": "hardlink", "sha256": "425d45616992029b798ad78a8f53553a15fe76c62bf3c9ff6c72c65a855fc3af", "sha256_in_prefix": "425d45616992029b798ad78a8f53553a15fe76c62bf3c9ff6c72c65a855fc3af", "size_in_bytes": 9620}, {"_path": "include/cuda_awbarrier_helpers.h", "path_type": "hardlink", "sha256": "3fb7af882e74b95c329e0be94efbbd4b164a4a1bda0b81926fde6396e5b6b65c", "sha256_in_prefix": "3fb7af882e74b95c329e0be94efbbd4b164a4a1bda0b81926fde6396e5b6b65c", "size_in_bytes": 12854}, {"_path": "include/cuda_awbarrier_primitives.h", "path_type": "hardlink", "sha256": "73e548b39b2867086da71dfd2c70268ea211c0caa4d0adc801be6919d239db14", "sha256_in_prefix": "73e548b39b2867086da71dfd2c70268ea211c0caa4d0adc801be6919d239db14", "size_in_bytes": 4808}, {"_path": "include/cuda_bf16.h", "path_type": "hardlink", "sha256": "458a47aeb1e0c6161450dca111da678d80f1f356eda556cffbe920ae9d9f37d4", "sha256_in_prefix": "458a47aeb1e0c6161450dca111da678d80f1f356eda556cffbe920ae9d9f37d4", "size_in_bytes": 153223}, {"_path": "include/cuda_bf16.hpp", "path_type": "hardlink", "sha256": "b1d37d1ab32f7e402f791a6fdb5d0400f3fd98c1c784d9d1d99afa72b12dac97", "sha256_in_prefix": "b1d37d1ab32f7e402f791a6fdb5d0400f3fd98c1c784d9d1d99afa72b12dac97", "size_in_bytes": 107722}, {"_path": "include/cuda_d3d10_interop.h", "path_type": "hardlink", "sha256": "55039312e63d29dde634e4e707fbd22c4e439739dadac4e0abd03a506e29d981", "sha256_in_prefix": "55039312e63d29dde634e4e707fbd22c4e439739dadac4e0abd03a506e29d981", "size_in_bytes": 29316}, {"_path": "include/cuda_d3d11_interop.h", "path_type": "hardlink", "sha256": "ce04ecb5dccb3b27e48b97c1b1f8aa801bb44760ff575e60750aaac78d543d14", "sha256_in_prefix": "ce04ecb5dccb3b27e48b97c1b1f8aa801bb44760ff575e60750aaac78d543d14", "size_in_bytes": 12787}, {"_path": "include/cuda_d3d9_interop.h", "path_type": "hardlink", "sha256": "50ad1133b84981533ad42e74c8a6afc73a9d83c9f6182311801a5ccf3e7c6e84", "sha256_in_prefix": "50ad1133b84981533ad42e74c8a6afc73a9d83c9f6182311801a5ccf3e7c6e84", "size_in_bytes": 31646}, {"_path": "include/cuda_device_runtime_api.h", "path_type": "hardlink", "sha256": "28d372f47435a1a9510b358244212f370bcbb33d5da102189db7a2b7058a9db0", "sha256_in_prefix": "28d372f47435a1a9510b358244212f370bcbb33d5da102189db7a2b7058a9db0", "size_in_bytes": 40490}, {"_path": "include/cuda_egl_interop.h", "path_type": "hardlink", "sha256": "fc048c14d8ca0d4ef6bf21d1246c50d5b28b5e54a80f1c62e1e363ce27cff1fa", "sha256_in_prefix": "fc048c14d8ca0d4ef6bf21d1246c50d5b28b5e54a80f1c62e1e363ce27cff1fa", "size_in_bytes": 37751}, {"_path": "include/cuda_fp16.h", "path_type": "hardlink", "sha256": "bb785a59094231baafd91169ca8ee5cd4a8cfd246cb0b143407d51760ddf9c1c", "sha256_in_prefix": "bb785a59094231baafd91169ca8ee5cd4a8cfd246cb0b143407d51760ddf9c1c", "size_in_bytes": 145805}, {"_path": "include/cuda_fp16.hpp", "path_type": "hardlink", "sha256": "faf7f030502fa4d4f893f39cb127ddd1dbcd4f28e17b319d1c17d7a12debc4ab", "sha256_in_prefix": "faf7f030502fa4d4f893f39cb127ddd1dbcd4f28e17b319d1c17d7a12debc4ab", "size_in_bytes": 101344}, {"_path": "include/cuda_fp8.h", "path_type": "hardlink", "sha256": "aa27c152169f8eaee6353170aeb7cfec1872f335e9351d710ce302d002fe8725", "sha256_in_prefix": "aa27c152169f8eaee6353170aeb7cfec1872f335e9351d710ce302d002fe8725", "size_in_bytes": 14200}, {"_path": "include/cuda_fp8.hpp", "path_type": "hardlink", "sha256": "44f9d9d76e739ee35eafd99a4effd867cdd245bcb9857661f23b09d92ab8e10a", "sha256_in_prefix": "44f9d9d76e739ee35eafd99a4effd867cdd245bcb9857661f23b09d92ab8e10a", "size_in_bytes": 58037}, {"_path": "include/cuda_gl_interop.h", "path_type": "hardlink", "sha256": "a4a4041049b59206543d55fdc6c0720b245afd933bf3c298e9d9a2f4a8ac1de1", "sha256_in_prefix": "a4a4041049b59206543d55fdc6c0720b245afd933bf3c298e9d9a2f4a8ac1de1", "size_in_bytes": 19664}, {"_path": "include/cuda_occupancy.h", "path_type": "hardlink", "sha256": "4c8e31b63c9c576ce02f995162723bbda511aa32b9c5f209cee41612ad62653d", "sha256_in_prefix": "4c8e31b63c9c576ce02f995162723bbda511aa32b9c5f209cee41612ad62653d", "size_in_bytes": 69137}, {"_path": "include/cuda_pipeline.h", "path_type": "hardlink", "sha256": "aacbf316553e1a34a8f13398ec720846f41cebb70fdedb7fdc97aa0093932262", "sha256_in_prefix": "aacbf316553e1a34a8f13398ec720846f41cebb70fdedb7fdc97aa0093932262", "size_in_bytes": 8354}, {"_path": "include/cuda_pipeline_helpers.h", "path_type": "hardlink", "sha256": "f59a26516dea3b56384ac636c6128b5fc920abc3493bd4bb910306157efa2713", "sha256_in_prefix": "f59a26516dea3b56384ac636c6128b5fc920abc3493bd4bb910306157efa2713", "size_in_bytes": 14225}, {"_path": "include/cuda_pipeline_primitives.h", "path_type": "hardlink", "sha256": "98ac2abfed83ecfe4eddbbf12f824146ef987aedabc0fa94c87b15849feba514", "sha256_in_prefix": "98ac2abfed83ecfe4eddbbf12f824146ef987aedabc0fa94c87b15849feba514", "size_in_bytes": 8823}, {"_path": "include/cuda_runtime.h", "path_type": "hardlink", "sha256": "f7196c0cb339cd693e7f75408351c3f0bb901c439f3feb22f04b881e8319c588", "sha256_in_prefix": "f7196c0cb339cd693e7f75408351c3f0bb901c439f3feb22f04b881e8319c588", "size_in_bytes": 90581}, {"_path": "include/cuda_runtime_api.h", "path_type": "hardlink", "sha256": "6f2fd99fe8b28c397dc291857f6dc02eed73deb28b4bdeddc7c0a31266419e11", "sha256_in_prefix": "6f2fd99fe8b28c397dc291857f6dc02eed73deb28b4bdeddc7c0a31266419e11", "size_in_bytes": 573762}, {"_path": "include/cuda_surface_types.h", "path_type": "hardlink", "sha256": "64fc83dc785b0b1ac9e6ed68d26342a4547382f7a6a97140d8aad205dad8c1e9", "sha256_in_prefix": "64fc83dc785b0b1ac9e6ed68d26342a4547382f7a6a97140d8aad205dad8c1e9", "size_in_bytes": 3764}, {"_path": "include/cuda_texture_types.h", "path_type": "hardlink", "sha256": "e46d1704740d6a0fa338ce66cd9023d0fe432082114fbab9c06acbf69a5f20f9", "sha256_in_prefix": "e46d1704740d6a0fa338ce66cd9023d0fe432082114fbab9c06acbf69a5f20f9", "size_in_bytes": 3764}, {"_path": "include/cudart_platform.h", "path_type": "hardlink", "sha256": "533a3dc955c7e7b3c6bd3b6f6298364c832657311f5b9e3e8e6608c84550dd03", "sha256_in_prefix": "533a3dc955c7e7b3c6bd3b6f6298364c832657311f5b9e3e8e6608c84550dd03", "size_in_bytes": 2774}, {"_path": "include/device_atomic_functions.h", "path_type": "hardlink", "sha256": "e64cacae86972b804463dc62f9713cdf2fcc434cf031f4ef4117d37702f70991", "sha256_in_prefix": "e64cacae86972b804463dc62f9713cdf2fcc434cf031f4ef4117d37702f70991", "size_in_bytes": 12100}, {"_path": "include/device_atomic_functions.hpp", "path_type": "hardlink", "sha256": "dae52ab657248b10502c5146e264539ee2736e6e2e2b7b8652f5d4a6f3b21441", "sha256_in_prefix": "dae52ab657248b10502c5146e264539ee2736e6e2e2b7b8652f5d4a6f3b21441", "size_in_bytes": 8373}, {"_path": "include/device_double_functions.h", "path_type": "hardlink", "sha256": "6d320fba0bec4047535f35190b6ed5c3c559ee7ccc081f53a9cfdfe4fe34f97f", "sha256_in_prefix": "6d320fba0bec4047535f35190b6ed5c3c559ee7ccc081f53a9cfdfe4fe34f97f", "size_in_bytes": 3517}, {"_path": "include/device_functions.h", "path_type": "hardlink", "sha256": "030f0f2c4b270b55a2c3402de9c5c7aa52fa967c02c92d38479c5388cab6c7c0", "sha256_in_prefix": "030f0f2c4b270b55a2c3402de9c5c7aa52fa967c02c92d38479c5388cab6c7c0", "size_in_bytes": 3475}, {"_path": "include/device_launch_parameters.h", "path_type": "hardlink", "sha256": "32e3edf2d180dc0e3025e38779db1f10436ab8fd16069121a8e609c5fd6046e3", "sha256_in_prefix": "32e3edf2d180dc0e3025e38779db1f10436ab8fd16069121a8e609c5fd6046e3", "size_in_bytes": 3964}, {"_path": "include/device_types.h", "path_type": "hardlink", "sha256": "2416b764cb308e6d9e93268826f3918f5a05e760d28e49c112ff9c732491e328", "sha256_in_prefix": "2416b764cb308e6d9e93268826f3918f5a05e760d28e49c112ff9c732491e328", "size_in_bytes": 3669}, {"_path": "include/driver_functions.h", "path_type": "hardlink", "sha256": "ab08532254d6d6829ea9a650901900bf0b4588f0d4636fbc634d7ee31874c334", "sha256_in_prefix": "ab08532254d6d6829ea9a650901900bf0b4588f0d4636fbc634d7ee31874c334", "size_in_bytes": 4770}, {"_path": "include/driver_types.h", "path_type": "hardlink", "sha256": "1633f739de7a000c613cd77305c9108726ab7109e9b80362cfd6169ac2161153", "sha256_in_prefix": "1633f739de7a000c613cd77305c9108726ab7109e9b80362cfd6169ac2161153", "size_in_bytes": 147947}, {"_path": "include/host_config.h", "path_type": "hardlink", "sha256": "087908be5214f2483e13630aa1ee26f2cd00e0c2aee6451016c0b46fc359117e", "sha256_in_prefix": "087908be5214f2483e13630aa1ee26f2cd00e0c2aee6451016c0b46fc359117e", "size_in_bytes": 3445}, {"_path": "include/host_defines.h", "path_type": "hardlink", "sha256": "28047eea24100b91fd397e96fb2755f74ac806494fdc39a8b3d15a48bfd7b5ca", "sha256_in_prefix": "28047eea24100b91fd397e96fb2755f74ac806494fdc39a8b3d15a48bfd7b5ca", "size_in_bytes": 3451}, {"_path": "include/library_types.h", "path_type": "hardlink", "sha256": "655caa787063bfd1285686ddca4ed16a602cc3c2d201c80a508bae356525516c", "sha256_in_prefix": "655caa787063bfd1285686ddca4ed16a602cc3c2d201c80a508bae356525516c", "size_in_bytes": 4869}, {"_path": "include/math_constants.h", "path_type": "hardlink", "sha256": "9040ea02b7486f2637998cd9bcadd8dd59ec855a5d7d74f274579fdeefa6a01a", "sha256_in_prefix": "9040ea02b7486f2637998cd9bcadd8dd59ec855a5d7d74f274579fdeefa6a01a", "size_in_bytes": 7760}, {"_path": "include/math_functions.h", "path_type": "hardlink", "sha256": "e556f9d919cc7d7b0a086a6d6cafdcdb9146fb397b82940e868ebaee6746f6aa", "sha256_in_prefix": "e556f9d919cc7d7b0a086a6d6cafdcdb9146fb397b82940e868ebaee6746f6aa", "size_in_bytes": 3463}, {"_path": "include/mma.h", "path_type": "hardlink", "sha256": "9749dd585be009be5aa5bdeab5f05aa3aa9483f0511aa4397746f999a8209934", "sha256_in_prefix": "9749dd585be009be5aa5bdeab5f05aa3aa9483f0511aa4397746f999a8209934", "size_in_bytes": 2992}, {"_path": "include/nvfunctional", "path_type": "hardlink", "sha256": "0538a04f2586c6a26a82818c93d34c13ad226fe88b34010c38974e9c8f22fcd9", "sha256_in_prefix": "0538a04f2586c6a26a82818c93d34c13ad226fe88b34010c38974e9c8f22fcd9", "size_in_bytes": 3035}, {"_path": "include/sm_20_atomic_functions.h", "path_type": "hardlink", "sha256": "eb719ba5d44ee47e7ed9452295646a9df57cc1019edd3ae3cb7a97ebe8ab07be", "sha256_in_prefix": "eb719ba5d44ee47e7ed9452295646a9df57cc1019edd3ae3cb7a97ebe8ab07be", "size_in_bytes": 5056}, {"_path": "include/sm_20_atomic_functions.hpp", "path_type": "hardlink", "sha256": "527480727a56b45711477999abf01240b8a83339507cbbf6719930d7f9cf2494", "sha256_in_prefix": "527480727a56b45711477999abf01240b8a83339507cbbf6719930d7f9cf2494", "size_in_bytes": 4014}, {"_path": "include/sm_20_intrinsics.h", "path_type": "hardlink", "sha256": "0f0a86d94f50d02d4fc523043d18a337d0b17de3b1f359015ba36879b9863383", "sha256_in_prefix": "0f0a86d94f50d02d4fc523043d18a337d0b17de3b1f359015ba36879b9863383", "size_in_bytes": 52615}, {"_path": "include/sm_20_intrinsics.hpp", "path_type": "hardlink", "sha256": "a35bb49f583fe6a5f1b91000b03a54d2049c984e1e73b069e625e1004fe8506d", "sha256_in_prefix": "a35bb49f583fe6a5f1b91000b03a54d2049c984e1e73b069e625e1004fe8506d", "size_in_bytes": 7915}, {"_path": "include/sm_30_intrinsics.h", "path_type": "hardlink", "sha256": "4eed1e648597a37a01142c47e11e22bfcc0cf85c38e0c8c784bc50889c077a46", "sha256_in_prefix": "4eed1e648597a37a01142c47e11e22bfcc0cf85c38e0c8c784bc50889c077a46", "size_in_bytes": 16596}, {"_path": "include/sm_30_intrinsics.hpp", "path_type": "hardlink", "sha256": "701e63430e8c509fca2ee24eebf55697b53dd3d53128e6d8269f0edd0bc9c0f6", "sha256_in_prefix": "701e63430e8c509fca2ee24eebf55697b53dd3d53128e6d8269f0edd0bc9c0f6", "size_in_bytes": 25171}, {"_path": "include/sm_32_atomic_functions.h", "path_type": "hardlink", "sha256": "4cb8092bfda3c2e97922fb68be5bc80dc4d67314f9abbfe612253b22dc619048", "sha256_in_prefix": "4cb8092bfda3c2e97922fb68be5bc80dc4d67314f9abbfe612253b22dc619048", "size_in_bytes": 6953}, {"_path": "include/sm_32_atomic_functions.hpp", "path_type": "hardlink", "sha256": "0f4c7973fd5dd413275fdb8d9d1d9c2b4e584fa7d6654544eb0bb23344bb054a", "sha256_in_prefix": "0f4c7973fd5dd413275fdb8d9d1d9c2b4e584fa7d6654544eb0bb23344bb054a", "size_in_bytes": 5550}, {"_path": "include/sm_32_intrinsics.h", "path_type": "hardlink", "sha256": "5fecf0b395006e6d2f71542d1806d3c7283548dd9e455daa23c5707454f6bf5b", "sha256_in_prefix": "5fecf0b395006e6d2f71542d1806d3c7283548dd9e455daa23c5707454f6bf5b", "size_in_bytes": 33900}, {"_path": "include/sm_32_intrinsics.hpp", "path_type": "hardlink", "sha256": "74ea3633e759344643ec06bdd061e298a3ad483fcf800b609d26429d5d269057", "sha256_in_prefix": "74ea3633e759344643ec06bdd061e298a3ad483fcf800b609d26429d5d269057", "size_in_bytes": 71204}, {"_path": "include/sm_35_atomic_functions.h", "path_type": "hardlink", "sha256": "7a15dc657fc4844847103e9db16390971c40f5e6db8282acd12586a85309b7b3", "sha256_in_prefix": "7a15dc657fc4844847103e9db16390971c40f5e6db8282acd12586a85309b7b3", "size_in_bytes": 2967}, {"_path": "include/sm_35_intrinsics.h", "path_type": "hardlink", "sha256": "69ba02f340b697ac6f80349cfce0c32aa8f2d327ef983dd3f8efcc14cd8f984e", "sha256_in_prefix": "69ba02f340b697ac6f80349cfce0c32aa8f2d327ef983dd3f8efcc14cd8f984e", "size_in_bytes": 3068}, {"_path": "include/sm_60_atomic_functions.h", "path_type": "hardlink", "sha256": "fb344d16917c505782c3ad56c3548b276600990fcc16a1877d529ecd111e0dd1", "sha256_in_prefix": "fb344d16917c505782c3ad56c3548b276600990fcc16a1877d529ecd111e0dd1", "size_in_bytes": 21445}, {"_path": "include/sm_60_atomic_functions.hpp", "path_type": "hardlink", "sha256": "5f632d7cfeb36458ec87f8d534d96d3160cbe8e3878020a371cf9d1ea0abb791", "sha256_in_prefix": "5f632d7cfeb36458ec87f8d534d96d3160cbe8e3878020a371cf9d1ea0abb791", "size_in_bytes": 15608}, {"_path": "include/sm_61_intrinsics.h", "path_type": "hardlink", "sha256": "f75ad8419557307429ea40577319836f7b252519671e4865191e073f3ec55016", "sha256_in_prefix": "f75ad8419557307429ea40577319836f7b252519671e4865191e073f3ec55016", "size_in_bytes": 6153}, {"_path": "include/sm_61_intrinsics.hpp", "path_type": "hardlink", "sha256": "1c2b2f68dd5513ac773b42e6feef4e5b64ac16e55ad39288d51b951f7e576bf8", "sha256_in_prefix": "1c2b2f68dd5513ac773b42e6feef4e5b64ac16e55ad39288d51b951f7e576bf8", "size_in_bytes": 6948}, {"_path": "include/surface_functions.h", "path_type": "hardlink", "sha256": "aa9aa51007376b766123ee8b6db105d7c20ebe8af4c50faa40bad42b635033ed", "sha256_in_prefix": "aa9aa51007376b766123ee8b6db105d7c20ebe8af4c50faa40bad42b635033ed", "size_in_bytes": 6906}, {"_path": "include/surface_indirect_functions.h", "path_type": "hardlink", "sha256": "082f48c0649bebd37ceb80e81c50d82aa94e2e549d8a013daaae17995c59d790", "sha256_in_prefix": "082f48c0649bebd37ceb80e81c50d82aa94e2e549d8a013daaae17995c59d790", "size_in_bytes": 11120}, {"_path": "include/surface_types.h", "path_type": "hardlink", "sha256": "88415cab52950c320e68c4294e838100df7d20c04e60e365e24a7985630a8900", "sha256_in_prefix": "88415cab52950c320e68c4294e838100df7d20c04e60e365e24a7985630a8900", "size_in_bytes": 4568}, {"_path": "include/texture_fetch_functions.h", "path_type": "hardlink", "sha256": "f889b1a37fc1360eb5aa98ac1ee90b9bfdda6a67c949275c6b659a9114d101bf", "sha256_in_prefix": "f889b1a37fc1360eb5aa98ac1ee90b9bfdda6a67c949275c6b659a9114d101bf", "size_in_bytes": 12691}, {"_path": "include/texture_indirect_functions.h", "path_type": "hardlink", "sha256": "5d3b7b635072266c4bfc22c46c88ce25dd2eb1510fc4a9608bb07e05054281af", "sha256_in_prefix": "5d3b7b635072266c4bfc22c46c88ce25dd2eb1510fc4a9608bb07e05054281af", "size_in_bytes": 21801}, {"_path": "include/texture_types.h", "path_type": "hardlink", "sha256": "5ff8cf72cd8e8100a2ac3382cd8a472bf6cd5a8eda5252031b5c1f9170968945", "sha256_in_prefix": "5ff8cf72cd8e8100a2ac3382cd8a472bf6cd5a8eda5252031b5c1f9170968945", "size_in_bytes": 6467}, {"_path": "include/vector_functions.h", "path_type": "hardlink", "sha256": "655c94fffa78a0c70a71ffdce39fbb706865a062421c4279f9b0492a4612f87e", "sha256_in_prefix": "655c94fffa78a0c70a71ffdce39fbb706865a062421c4279f9b0492a4612f87e", "size_in_bytes": 8022}, {"_path": "include/vector_functions.hpp", "path_type": "hardlink", "sha256": "c7b15cc4d81a15ba93ccad66b086ea71ac22c524ecb98b6c5d94c7ddcca55757", "sha256_in_prefix": "c7b15cc4d81a15ba93ccad66b086ea71ac22c524ecb98b6c5d94c7ddcca55757", "size_in_bytes": 10376}, {"_path": "include/vector_types.h", "path_type": "hardlink", "sha256": "80b37640c68e1100d3a55ebff992cf520b2ebe15f12f8ebb089cc68da1f10c49", "sha256_in_prefix": "80b37640c68e1100d3a55ebff992cf520b2ebe15f12f8ebb089cc68da1f10c49", "size_in_bytes": 13649}], "paths_version": 1}, "requested_spec": "None", "sha256": "8df70e408c0f6792709d7e2161ab0f7c9d0bc9f06cae0091416114e5ba81645a", "size": 561887, "subdir": "win-64", "timestamp": 1680574622000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-cudart-dev-12.1.105-0.tar.bz2", "version": "12.1.105"}