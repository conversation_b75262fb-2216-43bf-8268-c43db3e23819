{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": [], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cupti-12.1.105-0", "files": ["LICENSE", "doc/Cupti/annotated.html", "doc/Cupti/classes.html", "doc/Cupti/doxygen.css", "doc/Cupti/doxygen.png", "doc/Cupti/ftv2blank.png", "doc/Cupti/ftv2doc.png", "doc/Cupti/ftv2folderclosed.png", "doc/Cupti/ftv2folderopen.png", "doc/Cupti/ftv2lastnode.png", "doc/Cupti/ftv2link.png", "doc/Cupti/ftv2mlastnode.png", "doc/Cupti/ftv2mnode.png", "doc/Cupti/ftv2node.png", "doc/Cupti/ftv2plastnode.png", "doc/Cupti/ftv2pnode.png", "doc/Cupti/ftv2vertline.png", "doc/Cupti/functions.html", "doc/Cupti/functions_0x62.html", "doc/Cupti/functions_0x63.html", "doc/Cupti/functions_0x64.html", "doc/Cupti/functions_0x65.html", "doc/Cupti/functions_0x66.html", "doc/Cupti/functions_0x67.html", "doc/Cupti/functions_0x68.html", "doc/Cupti/functions_0x69.html", "doc/Cupti/functions_0x6a.html", "doc/Cupti/functions_0x6b.html", "doc/Cupti/functions_0x6c.html", "doc/Cupti/functions_0x6d.html", "doc/Cupti/functions_0x6e.html", "doc/Cupti/functions_0x6f.html", "doc/Cupti/functions_0x70.html", "doc/Cupti/functions_0x71.html", "doc/Cupti/functions_0x72.html", "doc/Cupti/functions_0x73.html", "doc/Cupti/functions_0x74.html", "doc/Cupti/functions_0x75.html", "doc/Cupti/functions_0x76.html", "doc/Cupti/functions_0x77.html", "doc/Cupti/functions_vars.html", "doc/Cupti/functions_vars_0x62.html", "doc/Cupti/functions_vars_0x63.html", "doc/Cupti/functions_vars_0x64.html", "doc/Cupti/functions_vars_0x65.html", "doc/Cupti/functions_vars_0x66.html", "doc/Cupti/functions_vars_0x67.html", "doc/Cupti/functions_vars_0x68.html", "doc/Cupti/functions_vars_0x69.html", "doc/Cupti/functions_vars_0x6a.html", "doc/Cupti/functions_vars_0x6b.html", "doc/Cupti/functions_vars_0x6c.html", "doc/Cupti/functions_vars_0x6d.html", "doc/Cupti/functions_vars_0x6e.html", "doc/Cupti/functions_vars_0x6f.html", "doc/Cupti/functions_vars_0x70.html", "doc/Cupti/functions_vars_0x71.html", "doc/Cupti/functions_vars_0x72.html", "doc/Cupti/functions_vars_0x73.html", "doc/Cupti/functions_vars_0x74.html", "doc/Cupti/functions_vars_0x75.html", "doc/Cupti/functions_vars_0x76.html", "doc/Cupti/functions_vars_0x77.html", "doc/Cupti/group__CUPTI__ACTIVITY__API.html", "doc/Cupti/group__CUPTI__CALLBACK__API.html", "doc/Cupti/group__CUPTI__CHECKPOINT__API.html", "doc/Cupti/group__CUPTI__EVENT__API.html", "doc/Cupti/group__CUPTI__METRIC__API.html", "doc/Cupti/group__CUPTI__PCSAMPLING__API.html", "doc/Cupti/group__CUPTI__PCSAMPLING__UTILITY.html", "doc/Cupti/group__CUPTI__PROFILER__API.html", "doc/Cupti/group__CUPTI__RESULT__API.html", "doc/Cupti/group__CUPTI__VERSION__API.html", "doc/Cupti/index.html", "doc/Cupti/modules.html", "doc/Cupti/notices-header.html", "doc/<PERSON>ti/r_library_support.html", "doc/<PERSON>ti/r_main.html", "doc/<PERSON>ti/r_overview.html", "doc/<PERSON><PERSON>/r_profiler.html", "doc/<PERSON>ti/r_special_configurations.html", "doc/<PERSON>ti/release_notes.html", "doc/Cupti/structBufferInfo.html", "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html", "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html", "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html", "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html", "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html", "doc/Cupti/structCUpti__Activity.html", "doc/Cupti/structCUpti__ActivityAPI.html", "doc/Cupti/structCUpti__ActivityAutoBoostState.html", "doc/Cupti/structCUpti__ActivityBranch.html", "doc/Cupti/structCUpti__ActivityBranch2.html", "doc/Cupti/structCUpti__ActivityCdpKernel.html", "doc/Cupti/structCUpti__ActivityContext.html", "doc/Cupti/structCUpti__ActivityCudaEvent.html", "doc/Cupti/structCUpti__ActivityDevice.html", "doc/Cupti/structCUpti__ActivityDevice2.html", "doc/Cupti/structCUpti__ActivityDevice3.html", "doc/Cupti/structCUpti__ActivityDevice4.html", "doc/Cupti/structCUpti__ActivityDeviceAttribute.html", "doc/Cupti/structCUpti__ActivityEnvironment.html", "doc/Cupti/structCUpti__ActivityEvent.html", "doc/Cupti/structCUpti__ActivityEventInstance.html", "doc/Cupti/structCUpti__ActivityExternalCorrelation.html", "doc/Cupti/structCUpti__ActivityFunction.html", "doc/Cupti/structCUpti__ActivityGlobalAccess.html", "doc/Cupti/structCUpti__ActivityGlobalAccess2.html", "doc/Cupti/structCUpti__ActivityGlobalAccess3.html", "doc/Cupti/structCUpti__ActivityGraphTrace.html", "doc/Cupti/structCUpti__ActivityInstantaneousEvent.html", "doc/Cupti/structCUpti__ActivityInstantaneousEventInstance.html", "doc/Cupti/structCUpti__ActivityInstantaneousMetric.html", "doc/Cupti/structCUpti__ActivityInstantaneousMetricInstance.html", "doc/Cupti/structCUpti__ActivityInstructionCorrelation.html", "doc/Cupti/structCUpti__ActivityInstructionExecution.html", "doc/Cupti/structCUpti__ActivityJit.html", "doc/Cupti/structCUpti__ActivityKernel.html", "doc/Cupti/structCUpti__ActivityKernel2.html", "doc/Cupti/structCUpti__ActivityKernel3.html", "doc/Cupti/structCUpti__ActivityKernel4.html", "doc/Cupti/structCUpti__ActivityKernel5.html", "doc/Cupti/structCUpti__ActivityKernel6.html", "doc/Cupti/structCUpti__ActivityKernel7.html", "doc/Cupti/structCUpti__ActivityKernel8.html", "doc/Cupti/structCUpti__ActivityKernel9.html", "doc/Cupti/structCUpti__ActivityMarker.html", "doc/Cupti/structCUpti__ActivityMarker2.html", "doc/Cupti/structCUpti__ActivityMarkerData.html", "doc/Cupti/structCUpti__ActivityMemcpy.html", "doc/Cupti/structCUpti__ActivityMemcpy3.html", "doc/Cupti/structCUpti__ActivityMemcpy4.html", "doc/Cupti/structCUpti__ActivityMemcpy5.html", "doc/Cupti/structCUpti__ActivityMemcpyPtoP.html", "doc/Cupti/structCUpti__ActivityMemcpyPtoP2.html", "doc/Cupti/structCUpti__ActivityMemcpyPtoP3.html", "doc/Cupti/structCUpti__ActivityMemcpyPtoP4.html", "doc/Cupti/structCUpti__ActivityMemory.html", "doc/Cupti/structCUpti__ActivityMemory2.html", "doc/Cupti/structCUpti__ActivityMemory3.html", "doc/Cupti/structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html", "doc/Cupti/structCUpti__ActivityMemoryPool.html", "doc/Cupti/structCUpti__ActivityMemoryPool2.html", "doc/Cupti/structCUpti__ActivityMemset.html", "doc/Cupti/structCUpti__ActivityMemset2.html", "doc/Cupti/structCUpti__ActivityMemset3.html", "doc/Cupti/structCUpti__ActivityMemset4.html", "doc/Cupti/structCUpti__ActivityMetric.html", "doc/Cupti/structCUpti__ActivityMetricInstance.html", "doc/Cupti/structCUpti__ActivityModule.html", "doc/Cupti/structCUpti__ActivityName.html", "doc/Cupti/structCUpti__ActivityNvLink.html", "doc/Cupti/structCUpti__ActivityNvLink2.html", "doc/Cupti/structCUpti__ActivityNvLink3.html", "doc/Cupti/structCUpti__ActivityNvLink4.html", "doc/Cupti/structCUpti__ActivityOpenAcc.html", "doc/Cupti/structCUpti__ActivityOpenAccData.html", "doc/Cupti/structCUpti__ActivityOpenAccLaunch.html", "doc/Cupti/structCUpti__ActivityOpenAccOther.html", "doc/Cupti/structCUpti__ActivityOpenMp.html", "doc/Cupti/structCUpti__ActivityOverhead.html", "doc/Cupti/structCUpti__ActivityPCSampling.html", "doc/Cupti/structCUpti__ActivityPCSampling2.html", "doc/Cupti/structCUpti__ActivityPCSampling3.html", "doc/Cupti/structCUpti__ActivityPCSamplingConfig.html", "doc/Cupti/structCUpti__ActivityPCSamplingRecordInfo.html", "doc/Cupti/structCUpti__ActivityPcie.html", "doc/Cupti/structCUpti__ActivityPreemption.html", "doc/Cupti/structCUpti__ActivitySharedAccess.html", "doc/Cupti/structCUpti__ActivitySourceLocator.html", "doc/Cupti/structCUpti__ActivityStream.html", "doc/Cupti/structCUpti__ActivitySynchronization.html", "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounter.html", "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounter2.html", "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounterConfig.html", "doc/<PERSON>ti/structCUpti__CallbackData.html", "doc/Cupti/structCUpti__EventGroupSet.html", "doc/Cupti/structCUpti__EventGroupSets.html", "doc/Cupti/structCUpti__GetCubinCrcParams.html", "doc/Cupti/structCUpti__GetSassToSourceCorrelationParams.html", "doc/Cupti/structCUpti__GraphData.html", "doc/Cupti/structCUpti__ModuleResourceData.html", "doc/Cupti/structCUpti__NvtxData.html", "doc/Cupti/structCUpti__PCSamplingConfigurationInfo.html", "doc/Cupti/structCUpti__PCSamplingConfigurationInfoParams.html", "doc/Cupti/structCUpti__PCSamplingData.html", "doc/Cupti/structCUpti__PCSamplingDisableParams.html", "doc/Cupti/structCUpti__PCSamplingEnableParams.html", "doc/Cupti/structCUpti__PCSamplingGetDataParams.html", "doc/Cupti/structCUpti__PCSamplingGetNumStallReasonsParams.html", "doc/Cupti/structCUpti__PCSamplingGetStallReasonsParams.html", "doc/Cupti/structCUpti__PCSamplingPCData.html", "doc/Cupti/structCUpti__PCSamplingStallReason.html", "doc/Cupti/structCUpti__PCSamplingStartParams.html", "doc/Cupti/structCUpti__PCSamplingStopParams.html", "doc/Cupti/structCUpti__Profiler__BeginPass__Params.html", "doc/<PERSON>ti/structCUpti__Profiler__BeginSession__Params.html", "doc/Cupti/structCUpti__Profiler__CounterDataImageOptions.html", "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html", "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html", "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html", "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__Initialize__Params.html", "doc/<PERSON>ti/structCUpti__Profiler__DeInitialize__Params.html", "doc/Cupti/structCUpti__Profiler__DeviceSupported__Params.html", "doc/Cupti/structCUpti__Profiler__DisableProfiling__Params.html", "doc/Cupti/structCUpti__Profiler__EnableProfiling__Params.html", "doc/Cupti/structCUpti__Profiler__EndPass__Params.html", "doc/Cupti/structCUpti__Profiler__EndSession__Params.html", "doc/Cupti/structCUpti__Profiler__FlushCounterData__Params.html", "doc/Cupti/structCUpti__Profiler__GetCounterAvailability__Params.html", "doc/Cupti/structCUpti__Profiler__Initialize__Params.html", "doc/Cupti/structCUpti__Profiler__IsPassCollected__Params.html", "doc/Cupti/structCUpti__Profiler__SetConfig__Params.html", "doc/Cupti/structCUpti__Profiler__UnsetConfig__Params.html", "doc/Cupti/structCUpti__ResourceData.html", "doc/<PERSON>ti/structCUpti__SynchronizeData.html", "doc/Cupti/structHeader.html", "doc/Cupti/structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html", "doc/Cupti/structPcSamplingStallReasons.html", "doc/<PERSON>ti/tab_b.gif", "doc/<PERSON>ti/tab_l.gif", "doc/<PERSON>ti/tab_r.gif", "doc/Cupti/tabs.css", "doc/Cupti/unionCUpti__ActivityObjectKindId.html", "doc/Cupti/unionCUpti__MetricValue.html", "doc/common/formatting/bg-head.png", "doc/common/formatting/bg-horiz.png", "doc/common/formatting/bg-left.png", "doc/common/formatting/bg-right.png", "doc/common/formatting/bg-sidehead-glow.png", "doc/common/formatting/bg-sidehead.png", "doc/common/formatting/bg-vert.png", "doc/common/formatting/common.min.js", "doc/common/formatting/commonltr.css", "doc/common/formatting/cppapiref.css", "doc/common/formatting/cuda-toolkit-documentation.png", "doc/common/formatting/devtools-documentation.png", "doc/common/formatting/devzone.png", "doc/common/formatting/dita.style.css", "doc/common/formatting/html5shiv-printshiv.min.js", "doc/common/formatting/jquery.ba-hashchange.min.js", "doc/common/formatting/jquery.min.js", "doc/common/formatting/jquery.scrollintoview.min.js", "doc/common/formatting/magnify-dropdown.png", "doc/common/formatting/magnify.png", "doc/common/formatting/nvidia.png", "doc/common/formatting/prettify/lang-Splus.js", "doc/common/formatting/prettify/lang-aea.js", "doc/common/formatting/prettify/lang-agc.js", "doc/common/formatting/prettify/lang-apollo.js", "doc/common/formatting/prettify/lang-basic.js", "doc/common/formatting/prettify/lang-cbm.js", "doc/common/formatting/prettify/lang-cl.js", "doc/common/formatting/prettify/lang-clj.js", "doc/common/formatting/prettify/lang-css.js", "doc/common/formatting/prettify/lang-dart.js", "doc/common/formatting/prettify/lang-el.js", "doc/common/formatting/prettify/lang-erl.js", "doc/common/formatting/prettify/lang-erlang.js", "doc/common/formatting/prettify/lang-fs.js", "doc/common/formatting/prettify/lang-go.js", "doc/common/formatting/prettify/lang-hs.js", "doc/common/formatting/prettify/lang-lasso.js", "doc/common/formatting/prettify/lang-lassoscript.js", "doc/common/formatting/prettify/lang-latex.js", "doc/common/formatting/prettify/lang-lgt.js", "doc/common/formatting/prettify/lang-lisp.js", "doc/common/formatting/prettify/lang-ll.js", "doc/common/formatting/prettify/lang-llvm.js", "doc/common/formatting/prettify/lang-logtalk.js", "doc/common/formatting/prettify/lang-ls.js", "doc/common/formatting/prettify/lang-lsp.js", "doc/common/formatting/prettify/lang-lua.js", "doc/common/formatting/prettify/lang-matlab.js", "doc/common/formatting/prettify/lang-ml.js", "doc/common/formatting/prettify/lang-mumps.js", "doc/common/formatting/prettify/lang-n.js", "doc/common/formatting/prettify/lang-nemerle.js", "doc/common/formatting/prettify/lang-pascal.js", "doc/common/formatting/prettify/lang-proto.js", "doc/common/formatting/prettify/lang-r.js", "doc/common/formatting/prettify/lang-rd.js", "doc/common/formatting/prettify/lang-rkt.js", "doc/common/formatting/prettify/lang-rust.js", "doc/common/formatting/prettify/lang-s.js", "doc/common/formatting/prettify/lang-scala.js", "doc/common/formatting/prettify/lang-scm.js", "doc/common/formatting/prettify/lang-sql.js", "doc/common/formatting/prettify/lang-ss.js", "doc/common/formatting/prettify/lang-swift.js", "doc/common/formatting/prettify/lang-tcl.js", "doc/common/formatting/prettify/lang-tex.js", "doc/common/formatting/prettify/lang-vb.js", "doc/common/formatting/prettify/lang-vbs.js", "doc/common/formatting/prettify/lang-vhd.js", "doc/common/formatting/prettify/lang-vhdl.js", "doc/common/formatting/prettify/lang-wiki.js", "doc/common/formatting/prettify/lang-xq.js", "doc/common/formatting/prettify/lang-xquery.js", "doc/common/formatting/prettify/lang-yaml.js", "doc/common/formatting/prettify/lang-yml.js", "doc/common/formatting/prettify/onLoad.png", "doc/common/formatting/prettify/prettify.css", "doc/common/formatting/prettify/prettify.js", "doc/common/formatting/prettify/run_prettify.js", "doc/common/formatting/qwcode.highlight.css", "doc/common/formatting/search-clear.png", "doc/common/formatting/site.css", "doc/common/scripts/google-analytics/google-analytics-tracker.js", "doc/common/scripts/google-analytics/google-analytics-write.js", "doc/common/scripts/tynt/tynt.js", "doc/index.html", "doc/pdf/Cupti.pdf", "doc/search/check.html", "doc/search/files.js", "doc/search/htmlFileInfoList.js", "doc/search/htmlFileList.js", "doc/search/index-1.js", "doc/search/index-2.js", "doc/search/index-3.js", "doc/search/nwSearchFnt.min.js", "doc/search/stemmers/en_stemmer.min.js", "include/cuda_stdint.h", "include/cupti.h", "include/cupti_activity.h", "include/cupti_callbacks.h", "include/cupti_checkpoint.h", "include/cupti_driver_cbid.h", "include/cupti_events.h", "include/cupti_metrics.h", "include/cupti_nvtx_cbid.h", "include/cupti_pcsampling.h", "include/cupti_pcsampling_util.h", "include/cupti_profiler_target.h", "include/cupti_result.h", "include/cupti_runtime_cbid.h", "include/cupti_target.h", "include/cupti_version.h", "include/generated_cudaD3D10_meta.h", "include/generated_cudaD3D11_meta.h", "include/generated_cudaD3D9_meta.h", "include/generated_cudaGL_meta.h", "include/generated_cuda_d3d10_interop_meta.h", "include/generated_cuda_d3d11_interop_meta.h", "include/generated_cuda_d3d9_interop_meta.h", "include/generated_cuda_gl_interop_meta.h", "include/generated_cuda_meta.h", "include/generated_cuda_runtime_api_meta.h", "include/generated_cudart_removed_meta.h", "include/generated_nvtx_meta.h", "include/nvperf_common.h", "include/nvperf_cuda_host.h", "include/nvperf_host.h", "include/nvperf_target.h", "lib/checkpoint.dll", "lib/checkpoint.lib", "lib/cupti.lib", "lib/cupti64_2023.1.1.dll", "lib/nvperf_host.dll", "lib/nvperf_host.lib", "lib/nvperf_target.dll", "lib/nvperf_target.lib", "lib/pcsamplingutil.dll", "lib/pcsamplingutil.lib", "samples/activity_trace_async/Makefile", "samples/activity_trace_async/activity_trace_async.cu", "samples/autorange_profiling/Makefile", "samples/autorange_profiling/auto_range_profiling.cu", "samples/callback_event/Makefile", "samples/callback_event/callback_event.cu", "samples/callback_metric/Makefile", "samples/callback_metric/callback_metric.cu", "samples/callback_profiling/Makefile", "samples/callback_profiling/callback_profiling.cu", "samples/callback_timestamp/Makefile", "samples/callback_timestamp/callback_timestamp.cu", "samples/checkpoint_kernels/Makefile", "samples/checkpoint_kernels/checkpoint_kernels.cu", "samples/common/helper_cupti.h", "samples/common/helper_cupti_activity.h", "samples/concurrent_profiling/Makefile", "samples/concurrent_profiling/concurrent_profiling.cu", "samples/cuda_graphs_trace/Makefile", "samples/cuda_graphs_trace/cuda_graphs_trace.cu", "samples/cuda_memory_trace/Makefile", "samples/cuda_memory_trace/memory_trace.cu", "samples/cupti_correlation/Makefile", "samples/cupti_correlation/cupti_correlation.cu", "samples/cupti_external_correlation/Makefile", "samples/cupti_external_correlation/cupti_external_correlation.cu", "samples/cupti_metric_properties/Makefile", "samples/cupti_metric_properties/cupti_metric_properties.cpp", "samples/cupti_nvtx/Makefile", "samples/cupti_nvtx/cupti_nvtx.cu", "samples/cupti_query/Makefile", "samples/cupti_query/cupti_query.cpp", "samples/cupti_trace_injection/Makefile", "samples/cupti_trace_injection/README.txt", "samples/cupti_trace_injection/cupti_trace_injection.cpp", "samples/event_multi_gpu/Makefile", "samples/event_multi_gpu/event_multi_gpu.cu", "samples/event_sampling/Makefile", "samples/event_sampling/event_sampling.cu", "samples/extensions/include/c_util/FileOp.h", "samples/extensions/include/c_util/ScopeExit.h", "samples/extensions/include/profilerhost_util/Eval.h", "samples/extensions/include/profilerhost_util/List.h", "samples/extensions/include/profilerhost_util/Metric.h", "samples/extensions/include/profilerhost_util/Parser.h", "samples/extensions/include/profilerhost_util/Utils.h", "samples/extensions/src/profilerhost_util/Eval.cpp", "samples/extensions/src/profilerhost_util/List.cpp", "samples/extensions/src/profilerhost_util/Makefile", "samples/extensions/src/profilerhost_util/Metric.cpp", "samples/nested_range_profiling/Makefile", "samples/nested_range_profiling/nested_range_profiling.cu", "samples/nvlink_bandwidth/Makefile", "samples/nvlink_bandwidth/nvlink_bandwidth.cu", "samples/pc_sampling/Makefile", "samples/pc_sampling/pc_sampling.cu", "samples/pc_sampling_continuous/Makefile", "samples/pc_sampling_continuous/README.txt", "samples/pc_sampling_continuous/libpc_sampling_continuous.pl", "samples/pc_sampling_continuous/pc_sampling_continuous.cpp", "samples/pc_sampling_start_stop/Makefile", "samples/pc_sampling_start_stop/pc_sampling_start_stop.cu", "samples/pc_sampling_utility/Makefile", "samples/pc_sampling_utility/README.txt", "samples/pc_sampling_utility/pc_sampling_utility.cpp", "samples/pc_sampling_utility/pc_sampling_utility_helper.h", "samples/sass_source_map/Makefile", "samples/sass_source_map/sass_source_map.cu", "samples/unified_memory/Makefile", "samples/unified_memory/unified_memory.cu", "samples/userrange_profiling/Makefile", "samples/userrange_profiling/user_range_profiling.cu"], "fn": "cuda-cupti-12.1.105-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cupti-12.1.105-0", "type": 1}, "md5": "b096413829ff917e9fda97491b7ee343", "name": "cuda-cupti", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-cupti-12.1.105-0.tar.bz2", "paths_data": {"paths": [{"_path": "LICENSE", "path_type": "hardlink", "sha256": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "sha256_in_prefix": "b9ee12782ca117b887588d99ae1b8c24deb59105e05417fe28ffbd7a55896dd1", "size_in_bytes": 61498}, {"_path": "doc/Cupti/annotated.html", "path_type": "hardlink", "sha256": "4cc841ef02c204bb63f6451d48b98233023a1c17c27c13a2c7a9ba86b9cef767", "sha256_in_prefix": "4cc841ef02c204bb63f6451d48b98233023a1c17c27c13a2c7a9ba86b9cef767", "size_in_bytes": 2277478}, {"_path": "doc/Cupti/classes.html", "path_type": "hardlink", "sha256": "1ade7997c23ec85f99833b403645bb68b95eab95bd75d2e38c2fd046300caefb", "sha256_in_prefix": "1ade7997c23ec85f99833b403645bb68b95eab95bd75d2e38c2fd046300caefb", "size_in_bytes": 19355}, {"_path": "doc/Cupti/doxygen.css", "path_type": "hardlink", "sha256": "104be0a51409d1fc06fd2c69dfe29a3f5710a1128bff5cc443d792939e3cedfd", "sha256_in_prefix": "104be0a51409d1fc06fd2c69dfe29a3f5710a1128bff5cc443d792939e3cedfd", "size_in_bytes": 5701}, {"_path": "doc/Cupti/doxygen.png", "path_type": "hardlink", "sha256": "c94b0fff66087999935e12fd492b9a1b2160b44b91841860d13ce0e33d20d8f2", "sha256_in_prefix": "c94b0fff66087999935e12fd492b9a1b2160b44b91841860d13ce0e33d20d8f2", "size_in_bytes": 1281}, {"_path": "doc/Cupti/ftv2blank.png", "path_type": "hardlink", "sha256": "fba45dcc498f0e705c1643720555b1c9247f8011667363f68fb94df953a8ae3f", "sha256_in_prefix": "fba45dcc498f0e705c1643720555b1c9247f8011667363f68fb94df953a8ae3f", "size_in_bytes": 174}, {"_path": "doc/Cupti/ftv2doc.png", "path_type": "hardlink", "sha256": "04c67f2ba2d4341ee90c1793449598cb04bf9507cedfcf91661c8fb1d4d63888", "sha256_in_prefix": "04c67f2ba2d4341ee90c1793449598cb04bf9507cedfcf91661c8fb1d4d63888", "size_in_bytes": 255}, {"_path": "doc/Cupti/ftv2folderclosed.png", "path_type": "hardlink", "sha256": "ef3c8f419ea067fdac01bd47cef0f283e8903e34172fa10ec7e5a45d3d6817ff", "sha256_in_prefix": "ef3c8f419ea067fdac01bd47cef0f283e8903e34172fa10ec7e5a45d3d6817ff", "size_in_bytes": 259}, {"_path": "doc/Cupti/ftv2folderopen.png", "path_type": "hardlink", "sha256": "d47ed39698b1ae5821bdf620ade6bfd6397b5dab8fd6dfd539e0188cd3e09592", "sha256_in_prefix": "d47ed39698b1ae5821bdf620ade6bfd6397b5dab8fd6dfd539e0188cd3e09592", "size_in_bytes": 261}, {"_path": "doc/Cupti/ftv2lastnode.png", "path_type": "hardlink", "sha256": "86e061efafe08ea42649b455ca14163360babe5e6ce1d0bd2dcdf2c54ccaba7c", "sha256_in_prefix": "86e061efafe08ea42649b455ca14163360babe5e6ce1d0bd2dcdf2c54ccaba7c", "size_in_bytes": 233}, {"_path": "doc/Cupti/ftv2link.png", "path_type": "hardlink", "sha256": "6d7a716c7a24be8e71649a238c5519c3e5b5d0cf8384bb68177e265dca941c38", "sha256_in_prefix": "6d7a716c7a24be8e71649a238c5519c3e5b5d0cf8384bb68177e265dca941c38", "size_in_bytes": 358}, {"_path": "doc/Cupti/ftv2mlastnode.png", "path_type": "hardlink", "sha256": "f02d0dd7e9633644f03b9e93d864494690508d4f5611e0df95aad99b0ccbc118", "sha256_in_prefix": "f02d0dd7e9633644f03b9e93d864494690508d4f5611e0df95aad99b0ccbc118", "size_in_bytes": 160}, {"_path": "doc/Cupti/ftv2mnode.png", "path_type": "hardlink", "sha256": "758e96c897604842e2fad6c1a7ee1d0cebf6b5d4c116bbf39bf094201a30c341", "sha256_in_prefix": "758e96c897604842e2fad6c1a7ee1d0cebf6b5d4c116bbf39bf094201a30c341", "size_in_bytes": 194}, {"_path": "doc/Cupti/ftv2node.png", "path_type": "hardlink", "sha256": "fd26c7ff32e383ce23b9d36097936c73df43de5712f01141054897a4781ac58a", "sha256_in_prefix": "fd26c7ff32e383ce23b9d36097936c73df43de5712f01141054897a4781ac58a", "size_in_bytes": 235}, {"_path": "doc/Cupti/ftv2plastnode.png", "path_type": "hardlink", "sha256": "d9ca5dbbc01326817c55113f0506fa998fd1907518989423a02bfa444fe44260", "sha256_in_prefix": "d9ca5dbbc01326817c55113f0506fa998fd1907518989423a02bfa444fe44260", "size_in_bytes": 165}, {"_path": "doc/Cupti/ftv2pnode.png", "path_type": "hardlink", "sha256": "31b0d40b12f1b174d9f123061986f1ce5f7d0d5be4685a82a3907c194b54f9bb", "sha256_in_prefix": "31b0d40b12f1b174d9f123061986f1ce5f7d0d5be4685a82a3907c194b54f9bb", "size_in_bytes": 200}, {"_path": "doc/Cupti/ftv2vertline.png", "path_type": "hardlink", "sha256": "df1347a845fcab6fff32f148f69ccbe0e8535b2175d28377e0992002eb885f92", "sha256_in_prefix": "df1347a845fcab6fff32f148f69ccbe0e8535b2175d28377e0992002eb885f92", "size_in_bytes": 229}, {"_path": "doc/Cupti/functions.html", "path_type": "hardlink", "sha256": "e31ffd77a0c25cb9f13bd235367d194ef362d944ae58e1e940cf53daac0d54a0", "sha256_in_prefix": "e31ffd77a0c25cb9f13bd235367d194ef362d944ae58e1e940cf53daac0d54a0", "size_in_bytes": 448436}, {"_path": "doc/Cupti/functions_0x62.html", "path_type": "hardlink", "sha256": "3a5ba4fab07e6e34eb092d5c6bef94761a6909e1d393448f2f35f100faa0ecf9", "sha256_in_prefix": "3a5ba4fab07e6e34eb092d5c6bef94761a6909e1d393448f2f35f100faa0ecf9", "size_in_bytes": 10512}, {"_path": "doc/Cupti/functions_0x63.html", "path_type": "hardlink", "sha256": "afd5a452db5f356ead7450085aaa7cb92c179d4fd4d90f66f978c131c854d28c", "sha256_in_prefix": "afd5a452db5f356ead7450085aaa7cb92c179d4fd4d90f66f978c131c854d28c", "size_in_bytes": 32359}, {"_path": "doc/Cupti/functions_0x64.html", "path_type": "hardlink", "sha256": "98c690a01100a6b7408247f66323b41f35ca617d530c8bf98ec41b81ccf739cb", "sha256_in_prefix": "98c690a01100a6b7408247f66323b41f35ca617d530c8bf98ec41b81ccf739cb", "size_in_bytes": 14291}, {"_path": "doc/Cupti/functions_0x65.html", "path_type": "hardlink", "sha256": "2b8cb7c4d03c1a7d27a7d45305f81bda5df474e9c7b36a4ba36afab8be06f24f", "sha256_in_prefix": "2b8cb7c4d03c1a7d27a7d45305f81bda5df474e9c7b36a4ba36afab8be06f24f", "size_in_bytes": 12307}, {"_path": "doc/Cupti/functions_0x66.html", "path_type": "hardlink", "sha256": "50a7b62e0d9f7097a6e2f9504eaa763c3ad75a014e1f7c6dd0f7097c786c33e3", "sha256_in_prefix": "50a7b62e0d9f7097a6e2f9504eaa763c3ad75a014e1f7c6dd0f7097c786c33e3", "size_in_bytes": 12802}, {"_path": "doc/Cupti/functions_0x67.html", "path_type": "hardlink", "sha256": "e629037c7e78f1b2a3b74789afa76c63ad583ff003d558e82c5c81f8eab369e2", "sha256_in_prefix": "e629037c7e78f1b2a3b74789afa76c63ad583ff003d558e82c5c81f8eab369e2", "size_in_bytes": 12534}, {"_path": "doc/Cupti/functions_0x68.html", "path_type": "hardlink", "sha256": "590abc7eb802d5ec88ea9da131317772933c4cbb209ad2817b8abea82637233d", "sha256_in_prefix": "590abc7eb802d5ec88ea9da131317772933c4cbb209ad2817b8abea82637233d", "size_in_bytes": 3904}, {"_path": "doc/Cupti/functions_0x69.html", "path_type": "hardlink", "sha256": "b38e2c4490ac3d28294118be833a14e91d14584cf62149f48a8cbaac9213a8c1", "sha256_in_prefix": "b38e2c4490ac3d28294118be833a14e91d14584cf62149f48a8cbaac9213a8c1", "size_in_bytes": 9693}, {"_path": "doc/Cupti/functions_0x6a.html", "path_type": "hardlink", "sha256": "3676154c067b0320c03aa918513c290d56116a3837861275fd5767d86bab9901", "sha256_in_prefix": "3676154c067b0320c03aa918513c290d56116a3837861275fd5767d86bab9901", "size_in_bytes": 3659}, {"_path": "doc/Cupti/functions_0x6b.html", "path_type": "hardlink", "sha256": "a5aaa1ea04113623d978586347ca4c341dc619cfa2c665de9ad75e530bd341e4", "sha256_in_prefix": "a5aaa1ea04113623d978586347ca4c341dc619cfa2c665de9ad75e530bd341e4", "size_in_bytes": 13663}, {"_path": "doc/Cupti/functions_0x6c.html", "path_type": "hardlink", "sha256": "f0c6312caf57a49a595d2b3fd1121d9dbeefe36947847cdce6da491acb02aaca", "sha256_in_prefix": "f0c6312caf57a49a595d2b3fd1121d9dbeefe36947847cdce6da491acb02aaca", "size_in_bytes": 8672}, {"_path": "doc/Cupti/functions_0x6d.html", "path_type": "hardlink", "sha256": "60e532ded5ae41181a1348a72f1c1e8a8ec35e8d64c2c47b971dcf520df2edbf", "sha256_in_prefix": "60e532ded5ae41181a1348a72f1c1e8a8ec35e8d64c2c47b971dcf520df2edbf", "size_in_bytes": 14280}, {"_path": "doc/Cupti/functions_0x6e.html", "path_type": "hardlink", "sha256": "0473c51fe482ac36d606edc38e6838840546237f292c892dfdd58c0933206118", "sha256_in_prefix": "0473c51fe482ac36d606edc38e6838840546237f292c892dfdd58c0933206118", "size_in_bytes": 11518}, {"_path": "doc/Cupti/functions_0x6f.html", "path_type": "hardlink", "sha256": "f17f8e48d4178f91c15bf5eacda476a892945432dae88dd935c12903f98e46fc", "sha256_in_prefix": "f17f8e48d4178f91c15bf5eacda476a892945432dae88dd935c12903f98e46fc", "size_in_bytes": 5101}, {"_path": "doc/Cupti/functions_0x70.html", "path_type": "hardlink", "sha256": "4075121d87e7289b1b7ba9aaf6ad4253a1b42d945e5d8ad4cbaf305121ca3307", "sha256_in_prefix": "4075121d87e7289b1b7ba9aaf6ad4253a1b42d945e5d8ad4cbaf305121ca3307", "size_in_bytes": 27231}, {"_path": "doc/Cupti/functions_0x71.html", "path_type": "hardlink", "sha256": "966d997cf408c4e833467c6932531b5c4da73899c6f8e02f6ccd5c2fee3ad82b", "sha256_in_prefix": "966d997cf408c4e833467c6932531b5c4da73899c6f8e02f6ccd5c2fee3ad82b", "size_in_bytes": 4094}, {"_path": "doc/Cupti/functions_0x72.html", "path_type": "hardlink", "sha256": "1f5ae72326cb6bf8629c88b0eedfd823a1c55ac2bdebc3f22f0bb53d8ca116a6", "sha256_in_prefix": "1f5ae72326cb6bf8629c88b0eedfd823a1c55ac2bdebc3f22f0bb53d8ca116a6", "size_in_bytes": 10856}, {"_path": "doc/Cupti/functions_0x73.html", "path_type": "hardlink", "sha256": "458146af7fac4d76d0a380151add584de337124e1256e7a65f8e96a890dcaa88", "sha256_in_prefix": "458146af7fac4d76d0a380151add584de337124e1256e7a65f8e96a890dcaa88", "size_in_bytes": 30796}, {"_path": "doc/Cupti/functions_0x74.html", "path_type": "hardlink", "sha256": "3964189dd12de5b95b0f2e6189acce33d495da357cfb400ada9ca37fe8932e66", "sha256_in_prefix": "3964189dd12de5b95b0f2e6189acce33d495da357cfb400ada9ca37fe8932e66", "size_in_bytes": 9047}, {"_path": "doc/Cupti/functions_0x75.html", "path_type": "hardlink", "sha256": "fc33849186629663f723ea6b14f53e2731a090c6d930054b60f5455668770f8f", "sha256_in_prefix": "fc33849186629663f723ea6b14f53e2731a090c6d930054b60f5455668770f8f", "size_in_bytes": 4191}, {"_path": "doc/Cupti/functions_0x76.html", "path_type": "hardlink", "sha256": "57bb7483143ff8cd7ec6b4bd70e1d789febf6bb47266d7ab4c4b35764d23380f", "sha256_in_prefix": "57bb7483143ff8cd7ec6b4bd70e1d789febf6bb47266d7ab4c4b35764d23380f", "size_in_bytes": 6237}, {"_path": "doc/Cupti/functions_0x77.html", "path_type": "hardlink", "sha256": "04a812826c6e5470773b327e40deb8743d9072b643b753322a26be546c9ee475", "sha256_in_prefix": "04a812826c6e5470773b327e40deb8743d9072b643b753322a26be546c9ee475", "size_in_bytes": 3603}, {"_path": "doc/Cupti/functions_vars.html", "path_type": "hardlink", "sha256": "9306aa5995cf8c1194c3a6fd928301e6e3960bf0fbf21eb1c2af3ec67d91e1e4", "sha256_in_prefix": "9306aa5995cf8c1194c3a6fd928301e6e3960bf0fbf21eb1c2af3ec67d91e1e4", "size_in_bytes": 6195}, {"_path": "doc/Cupti/functions_vars_0x62.html", "path_type": "hardlink", "sha256": "f222975db91cf78f272540c8aba876434ffe0108976e5ec328221316bc518e41", "sha256_in_prefix": "f222975db91cf78f272540c8aba876434ffe0108976e5ec328221316bc518e41", "size_in_bytes": 10528}, {"_path": "doc/Cupti/functions_vars_0x63.html", "path_type": "hardlink", "sha256": "d51d920392b0ae8efa32bad2e2e9d42003f26aa543e18fe843069f70108369f4", "sha256_in_prefix": "d51d920392b0ae8efa32bad2e2e9d42003f26aa543e18fe843069f70108369f4", "size_in_bytes": 32375}, {"_path": "doc/Cupti/functions_vars_0x64.html", "path_type": "hardlink", "sha256": "69a0e86522b0ad890b198b131ce1f843169d683ff882ce4c9e173dc1b499da7d", "sha256_in_prefix": "69a0e86522b0ad890b198b131ce1f843169d683ff882ce4c9e173dc1b499da7d", "size_in_bytes": 14307}, {"_path": "doc/Cupti/functions_vars_0x65.html", "path_type": "hardlink", "sha256": "2439c07da213c27c5d487ed5a0e60d0d20d091598b4cedd4b1244640f17625c3", "sha256_in_prefix": "2439c07da213c27c5d487ed5a0e60d0d20d091598b4cedd4b1244640f17625c3", "size_in_bytes": 12323}, {"_path": "doc/Cupti/functions_vars_0x66.html", "path_type": "hardlink", "sha256": "e3971dee6bf39972fabf43e3d2b14ff3b6f4b1594306d4496f779fa14bc04308", "sha256_in_prefix": "e3971dee6bf39972fabf43e3d2b14ff3b6f4b1594306d4496f779fa14bc04308", "size_in_bytes": 12818}, {"_path": "doc/Cupti/functions_vars_0x67.html", "path_type": "hardlink", "sha256": "52e0c1c404a48ebf17c7259a5de01c6721eea451254ee1eb714c2cf806959fd1", "sha256_in_prefix": "52e0c1c404a48ebf17c7259a5de01c6721eea451254ee1eb714c2cf806959fd1", "size_in_bytes": 12550}, {"_path": "doc/Cupti/functions_vars_0x68.html", "path_type": "hardlink", "sha256": "4ddc218d8f61f1b53a7c7a7d630cd707264396f3d463b4fcfa69ddea11e2bcbe", "sha256_in_prefix": "4ddc218d8f61f1b53a7c7a7d630cd707264396f3d463b4fcfa69ddea11e2bcbe", "size_in_bytes": 3920}, {"_path": "doc/Cupti/functions_vars_0x69.html", "path_type": "hardlink", "sha256": "821d64df782be7054cba33bc0e03721d5ed1ccaf29e545989b69fd7d24edba31", "sha256_in_prefix": "821d64df782be7054cba33bc0e03721d5ed1ccaf29e545989b69fd7d24edba31", "size_in_bytes": 9709}, {"_path": "doc/Cupti/functions_vars_0x6a.html", "path_type": "hardlink", "sha256": "6c38a985b81a8cd3b2b40496badf4f4a74fcc88371cf824ca588fe5e27b41701", "sha256_in_prefix": "6c38a985b81a8cd3b2b40496badf4f4a74fcc88371cf824ca588fe5e27b41701", "size_in_bytes": 3675}, {"_path": "doc/Cupti/functions_vars_0x6b.html", "path_type": "hardlink", "sha256": "82776821e194e42327c918f6e0be2380295d77f52935ee231c17e06d550d4467", "sha256_in_prefix": "82776821e194e42327c918f6e0be2380295d77f52935ee231c17e06d550d4467", "size_in_bytes": 13679}, {"_path": "doc/Cupti/functions_vars_0x6c.html", "path_type": "hardlink", "sha256": "677816f9dfeba5aec8039be48417161e58e06f75e533d30b24238e30f239130b", "sha256_in_prefix": "677816f9dfeba5aec8039be48417161e58e06f75e533d30b24238e30f239130b", "size_in_bytes": 8688}, {"_path": "doc/Cupti/functions_vars_0x6d.html", "path_type": "hardlink", "sha256": "c5b1c3d05812726a3e0b252600f60ed7bfbeac3c9a84d8e8a982cc4e70bf9bc6", "sha256_in_prefix": "c5b1c3d05812726a3e0b252600f60ed7bfbeac3c9a84d8e8a982cc4e70bf9bc6", "size_in_bytes": 14296}, {"_path": "doc/Cupti/functions_vars_0x6e.html", "path_type": "hardlink", "sha256": "45a100d01446466f06c121006171d774d93085e4b1a5fdb3e122b915a7a148c8", "sha256_in_prefix": "45a100d01446466f06c121006171d774d93085e4b1a5fdb3e122b915a7a148c8", "size_in_bytes": 11534}, {"_path": "doc/Cupti/functions_vars_0x6f.html", "path_type": "hardlink", "sha256": "33958e73ca696ef69bf43544901f1d39c3863223f0f0baa2d83ac71e28430ce5", "sha256_in_prefix": "33958e73ca696ef69bf43544901f1d39c3863223f0f0baa2d83ac71e28430ce5", "size_in_bytes": 5117}, {"_path": "doc/Cupti/functions_vars_0x70.html", "path_type": "hardlink", "sha256": "5387bfca212dd58404f586287c172d842ad38d0ea8e95da423a0f7ede2c6d595", "sha256_in_prefix": "5387bfca212dd58404f586287c172d842ad38d0ea8e95da423a0f7ede2c6d595", "size_in_bytes": 27247}, {"_path": "doc/Cupti/functions_vars_0x71.html", "path_type": "hardlink", "sha256": "17f3f6fe54dd1e9e7eaf42a31394e32b439153f04cc6869c0595a2d1e92300ec", "sha256_in_prefix": "17f3f6fe54dd1e9e7eaf42a31394e32b439153f04cc6869c0595a2d1e92300ec", "size_in_bytes": 4110}, {"_path": "doc/Cupti/functions_vars_0x72.html", "path_type": "hardlink", "sha256": "c279753af591aec2870290cb2caacfa8145f5e305afd4b2c0006b522ffac28ee", "sha256_in_prefix": "c279753af591aec2870290cb2caacfa8145f5e305afd4b2c0006b522ffac28ee", "size_in_bytes": 10872}, {"_path": "doc/Cupti/functions_vars_0x73.html", "path_type": "hardlink", "sha256": "f4ba7fa418d21c30ce37264441d2157f94d2123f7e42a4e9d14ca83a71fe1bef", "sha256_in_prefix": "f4ba7fa418d21c30ce37264441d2157f94d2123f7e42a4e9d14ca83a71fe1bef", "size_in_bytes": 30812}, {"_path": "doc/Cupti/functions_vars_0x74.html", "path_type": "hardlink", "sha256": "8ea46c58a14cc627d053170cb8721eee51986a8b10440de6ac671ccb258f989b", "sha256_in_prefix": "8ea46c58a14cc627d053170cb8721eee51986a8b10440de6ac671ccb258f989b", "size_in_bytes": 9063}, {"_path": "doc/Cupti/functions_vars_0x75.html", "path_type": "hardlink", "sha256": "eedc7a64cef5cd4ed21e1da9ccaa8b2a938d0651c0e51ef979d101ebdda830cd", "sha256_in_prefix": "eedc7a64cef5cd4ed21e1da9ccaa8b2a938d0651c0e51ef979d101ebdda830cd", "size_in_bytes": 4207}, {"_path": "doc/Cupti/functions_vars_0x76.html", "path_type": "hardlink", "sha256": "6a8b6e5ceea647451241c35ac8c81271da66657408ca7e4a03feb743b8d032f8", "sha256_in_prefix": "6a8b6e5ceea647451241c35ac8c81271da66657408ca7e4a03feb743b8d032f8", "size_in_bytes": 6253}, {"_path": "doc/Cupti/functions_vars_0x77.html", "path_type": "hardlink", "sha256": "4ef10ca5a3848df22dcb3659fa084346a939aa90c08914a56881e965490abb1a", "sha256_in_prefix": "4ef10ca5a3848df22dcb3659fa084346a939aa90c08914a56881e965490abb1a", "size_in_bytes": 3619}, {"_path": "doc/Cupti/group__CUPTI__ACTIVITY__API.html", "path_type": "hardlink", "sha256": "513f4b96798aa28b2a5bf131cb88e9268242cb5444a3a458dcc6159a6d23a3e1", "sha256_in_prefix": "513f4b96798aa28b2a5bf131cb88e9268242cb5444a3a458dcc6159a6d23a3e1", "size_in_bytes": 390653}, {"_path": "doc/Cupti/group__CUPTI__CALLBACK__API.html", "path_type": "hardlink", "sha256": "59b73c0ddd9aee6c22c30977687316ea68abad7be216f6dd5cc9c49581a1a5a1", "sha256_in_prefix": "59b73c0ddd9aee6c22c30977687316ea68abad7be216f6dd5cc9c49581a1a5a1", "size_in_bytes": 61262}, {"_path": "doc/Cupti/group__CUPTI__CHECKPOINT__API.html", "path_type": "hardlink", "sha256": "346130d4ceb27ced8e59e4264edb31488ea13ac9316fbf03ccc6c3629d04bbc0", "sha256_in_prefix": "346130d4ceb27ced8e59e4264edb31488ea13ac9316fbf03ccc6c3629d04bbc0", "size_in_bytes": 12806}, {"_path": "doc/Cupti/group__CUPTI__EVENT__API.html", "path_type": "hardlink", "sha256": "13a8086881a156cf946372f2c52a82bd717228ad44197f4e1227a047a75fbf53", "sha256_in_prefix": "13a8086881a156cf946372f2c52a82bd717228ad44197f4e1227a047a75fbf53", "size_in_bytes": 168728}, {"_path": "doc/Cupti/group__CUPTI__METRIC__API.html", "path_type": "hardlink", "sha256": "0cbbdb84adb1e0aa12577627ac6739f35967cbd8926ec1e957cc8bb4c0494a20", "sha256_in_prefix": "0cbbdb84adb1e0aa12577627ac6739f35967cbd8926ec1e957cc8bb4c0494a20", "size_in_bytes": 81261}, {"_path": "doc/Cupti/group__CUPTI__PCSAMPLING__API.html", "path_type": "hardlink", "sha256": "acb42087050be86020d4a8c8029eeb5df876b42fdcb25700c46d747c0578fd7a", "sha256_in_prefix": "acb42087050be86020d4a8c8029eeb5df876b42fdcb25700c46d747c0578fd7a", "size_in_bytes": 56315}, {"_path": "doc/Cupti/group__CUPTI__PCSAMPLING__UTILITY.html", "path_type": "hardlink", "sha256": "3aa4267228e865a10408d87aed9c916f21d839632c8f423cbeb0ec5c0b5d985d", "sha256_in_prefix": "3aa4267228e865a10408d87aed9c916f21d839632c8f423cbeb0ec5c0b5d985d", "size_in_bytes": 25548}, {"_path": "doc/Cupti/group__CUPTI__PROFILER__API.html", "path_type": "hardlink", "sha256": "e1e88a430e36631073303c09c7af43afef043a5c30979a6a0a20a481f78b53a8", "sha256_in_prefix": "e1e88a430e36631073303c09c7af43afef043a5c30979a6a0a20a481f78b53a8", "size_in_bytes": 49958}, {"_path": "doc/Cupti/group__CUPTI__RESULT__API.html", "path_type": "hardlink", "sha256": "6d3b4038c273d1e59a62d1ea3a0b5ed0435b202bc61f53ba818e30b51143b556", "sha256_in_prefix": "6d3b4038c273d1e59a62d1ea3a0b5ed0435b202bc61f53ba818e30b51143b556", "size_in_bytes": 31706}, {"_path": "doc/Cupti/group__CUPTI__VERSION__API.html", "path_type": "hardlink", "sha256": "1c79b5378cade0d2346e85c59285045667f92104c6ab3491a6711190751a8d27", "sha256_in_prefix": "1c79b5378cade0d2346e85c59285045667f92104c6ab3491a6711190751a8d27", "size_in_bytes": 5073}, {"_path": "doc/Cupti/index.html", "path_type": "hardlink", "sha256": "93df5b3c72dbc691cc368b3dbc01cfc06bb845151307b0875f15e535bcfc6e08", "sha256_in_prefix": "93df5b3c72dbc691cc368b3dbc01cfc06bb845151307b0875f15e535bcfc6e08", "size_in_bytes": 103922}, {"_path": "doc/Cupti/modules.html", "path_type": "hardlink", "sha256": "5311ec33d03483156cb77446e5071ea4656863f3be3133c5f2f22418e5d7c6e2", "sha256_in_prefix": "5311ec33d03483156cb77446e5071ea4656863f3be3133c5f2f22418e5d7c6e2", "size_in_bytes": 1077906}, {"_path": "doc/Cupti/notices-header.html", "path_type": "hardlink", "sha256": "e1615fe43dafc0d02af6d7e51308e7549681fff198cc12dd68aa23fd8e23c6ba", "sha256_in_prefix": "e1615fe43dafc0d02af6d7e51308e7549681fff198cc12dd68aa23fd8e23c6ba", "size_in_bytes": 63199}, {"_path": "doc/<PERSON>ti/r_library_support.html", "path_type": "hardlink", "sha256": "069093302fa80d75656b9db628e28b0fd3bba8353bada4d94c24412b12d7dae0", "sha256_in_prefix": "069093302fa80d75656b9db628e28b0fd3bba8353bada4d94c24412b12d7dae0", "size_in_bytes": 63669}, {"_path": "doc/<PERSON>ti/r_main.html", "path_type": "hardlink", "sha256": "3d78d4eb390c9e83b1c860b77528809eac5f01f309ded8d8506ace85fd4ddc27", "sha256_in_prefix": "3d78d4eb390c9e83b1c860b77528809eac5f01f309ded8d8506ace85fd4ddc27", "size_in_bytes": 840459}, {"_path": "doc/<PERSON>ti/r_overview.html", "path_type": "hardlink", "sha256": "f416ae43e179f1006e792da4b502a5a8b41a663dbcbc84cc959083ba1e392b7b", "sha256_in_prefix": "f416ae43e179f1006e792da4b502a5a8b41a663dbcbc84cc959083ba1e392b7b", "size_in_bytes": 68007}, {"_path": "doc/<PERSON><PERSON>/r_profiler.html", "path_type": "hardlink", "sha256": "7006103707895d39b6893e63a8914f66e5fcf75153cf5bc3a4f12b05427b1a72", "sha256_in_prefix": "7006103707895d39b6893e63a8914f66e5fcf75153cf5bc3a4f12b05427b1a72", "size_in_bytes": 62649}, {"_path": "doc/<PERSON>ti/r_special_configurations.html", "path_type": "hardlink", "sha256": "0c1d487cc50b86a8fac43e3369778caa1c2d8584944810e46df19cc00a2e5d92", "sha256_in_prefix": "0c1d487cc50b86a8fac43e3369778caa1c2d8584944810e46df19cc00a2e5d92", "size_in_bytes": 69077}, {"_path": "doc/<PERSON>ti/release_notes.html", "path_type": "hardlink", "sha256": "5049b2e033b7d58542361e1de943024b92b4076e88e5d185781d1aa5351e2c4e", "sha256_in_prefix": "5049b2e033b7d58542361e1de943024b92b4076e88e5d185781d1aa5351e2c4e", "size_in_bytes": 211300}, {"_path": "doc/Cupti/structBufferInfo.html", "path_type": "hardlink", "sha256": "5a1aa9a7b42f138587dd06c1c20f16f0c2d15a16ae6b515c4aae5a58bd06bf1c", "sha256_in_prefix": "5a1aa9a7b42f138587dd06c1c20f16f0c2d15a16ae6b515c4aae5a58bd06bf1c", "size_in_bytes": 4849}, {"_path": "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetBufferInfoParams.html", "path_type": "hardlink", "sha256": "14f36cfd1af55e53f015ee8b8ee43f49ac353ea0e53f1266c9dbd392ce586ff8", "sha256_in_prefix": "14f36cfd1af55e53f015ee8b8ee43f49ac353ea0e53f1266c9dbd392ce586ff8", "size_in_bytes": 4910}, {"_path": "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetHeaderDataParams.html", "path_type": "hardlink", "sha256": "6e06bd057dd44b689b88e38d46e671509d12aeed9b2d05ef4c7fc65ba21cf591", "sha256_in_prefix": "6e06bd057dd44b689b88e38d46e671509d12aeed9b2d05ef4c7fc65ba21cf591", "size_in_bytes": 4969}, {"_path": "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__GetPcSampDataParams.html", "path_type": "hardlink", "sha256": "cd3d1178cc7a0122b48fd5dc7af1b87568450781fee01924638bbe56607bb87d", "sha256_in_prefix": "cd3d1178cc7a0122b48fd5dc7af1b87568450781fee01924638bbe56607bb87d", "size_in_bytes": 10567}, {"_path": "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__MergePcSampDataParams.html", "path_type": "hardlink", "sha256": "490869cbe0688330bf558df0d3fb6aa916a9c93afc79b8d6c752f17e5d9f1118", "sha256_in_prefix": "490869cbe0688330bf558df0d3fb6aa916a9c93afc79b8d6c752f17e5d9f1118", "size_in_bytes": 7099}, {"_path": "doc/Cupti/structCUPTI_1_1PcSamplingUtil_1_1CUptiUtil__PutPcSampDataParams.html", "path_type": "hardlink", "sha256": "c0afdfe33f306c520442c7bc6ebec8bcea74d89d2a7e5186895e105bf9fb481b", "sha256_in_prefix": "c0afdfe33f306c520442c7bc6ebec8bcea74d89d2a7e5186895e105bf9fb481b", "size_in_bytes": 9414}, {"_path": "doc/Cupti/structCUpti__Activity.html", "path_type": "hardlink", "sha256": "6283c5166e018311a7a000e9add7c0d227a423fd7192ddb0c23ef897c6155f23", "sha256_in_prefix": "6283c5166e018311a7a000e9add7c0d227a423fd7192ddb0c23ef897c6155f23", "size_in_bytes": 3361}, {"_path": "doc/Cupti/structCUpti__ActivityAPI.html", "path_type": "hardlink", "sha256": "1a4becedbcdf383158dc51717709ea8000e4db1e64266a7dcdeeed506e879c7c", "sha256_in_prefix": "1a4becedbcdf383158dc51717709ea8000e4db1e64266a7dcdeeed506e879c7c", "size_in_bytes": 9143}, {"_path": "doc/Cupti/structCUpti__ActivityAutoBoostState.html", "path_type": "hardlink", "sha256": "4620c63ee056243b2bd072298ce1798b866f564f538d62ea35564b2d0986cc88", "sha256_in_prefix": "4620c63ee056243b2bd072298ce1798b866f564f538d62ea35564b2d0986cc88", "size_in_bytes": 3709}, {"_path": "doc/Cupti/structCUpti__ActivityBranch.html", "path_type": "hardlink", "sha256": "9dbfd40e132a2c667008bf4718446054cc8ec751b1c902cbc98b7371643a2ff8", "sha256_in_prefix": "9dbfd40e132a2c667008bf4718446054cc8ec751b1c902cbc98b7371643a2ff8", "size_in_bytes": 7917}, {"_path": "doc/Cupti/structCUpti__ActivityBranch2.html", "path_type": "hardlink", "sha256": "86f0d2d7007bad1c1ea810232b6cd84102481e287d5f5d3943eef5aee88d08de", "sha256_in_prefix": "86f0d2d7007bad1c1ea810232b6cd84102481e287d5f5d3943eef5aee88d08de", "size_in_bytes": 9304}, {"_path": "doc/Cupti/structCUpti__ActivityCdpKernel.html", "path_type": "hardlink", "sha256": "75390bed60dc9426730a3a4c4c22430e12039350b164244609f33d1824d2349a", "sha256_in_prefix": "75390bed60dc9426730a3a4c4c22430e12039350b164244609f33d1824d2349a", "size_in_bytes": 27250}, {"_path": "doc/Cupti/structCUpti__ActivityContext.html", "path_type": "hardlink", "sha256": "0bf0343174860e1d36172b4b95da9d235a63076e25966bde1cec3ce5e694aebb", "sha256_in_prefix": "0bf0343174860e1d36172b4b95da9d235a63076e25966bde1cec3ce5e694aebb", "size_in_bytes": 6106}, {"_path": "doc/Cupti/structCUpti__ActivityCudaEvent.html", "path_type": "hardlink", "sha256": "55798defefb7e3e140a0ea9983c4d31c3a755deb4b3d94a2acf50b227c1268eb", "sha256_in_prefix": "55798defefb7e3e140a0ea9983c4d31c3a755deb4b3d94a2acf50b227c1268eb", "size_in_bytes": 6807}, {"_path": "doc/Cupti/structCUpti__ActivityDevice.html", "path_type": "hardlink", "sha256": "ce2fcb33d411c4e836dd6c72a745ca7abcd08ba0ba76c84e584b524d473fd267", "sha256_in_prefix": "ce2fcb33d411c4e836dd6c72a745ca7abcd08ba0ba76c84e584b524d473fd267", "size_in_bytes": 23881}, {"_path": "doc/Cupti/structCUpti__ActivityDevice2.html", "path_type": "hardlink", "sha256": "1e22ead14d2f80780c2a2e99b5032396c185f41ccd6fd5170ae9c695f44419c5", "sha256_in_prefix": "1e22ead14d2f80780c2a2e99b5032396c185f41ccd6fd5170ae9c695f44419c5", "size_in_bytes": 28141}, {"_path": "doc/Cupti/structCUpti__ActivityDevice3.html", "path_type": "hardlink", "sha256": "a122c19cfc1358810557a34ef365f527dbf08575a0d641a849531812b1433fc6", "sha256_in_prefix": "a122c19cfc1358810557a34ef365f527dbf08575a0d641a849531812b1433fc6", "size_in_bytes": 29039}, {"_path": "doc/Cupti/structCUpti__ActivityDevice4.html", "path_type": "hardlink", "sha256": "9bf0901972e0b449d6d7971c38eb5e2b9a7ac0a48db257ce057886c361722776", "sha256_in_prefix": "9bf0901972e0b449d6d7971c38eb5e2b9a7ac0a48db257ce057886c361722776", "size_in_bytes": 32187}, {"_path": "doc/Cupti/structCUpti__ActivityDeviceAttribute.html", "path_type": "hardlink", "sha256": "5cfdcf4540e47088a0d70e6f9f229bac625c145deac1a7a7955eb786ada72914", "sha256_in_prefix": "5cfdcf4540e47088a0d70e6f9f229bac625c145deac1a7a7955eb786ada72914", "size_in_bytes": 7055}, {"_path": "doc/Cupti/structCUpti__ActivityEnvironment.html", "path_type": "hardlink", "sha256": "28e6f039d2a9598ef9238af8e4fac017d85e213e5b7a61aed7ec97ac6ee1fb09", "sha256_in_prefix": "28e6f039d2a9598ef9238af8e4fac017d85e213e5b7a61aed7ec97ac6ee1fb09", "size_in_bytes": 17996}, {"_path": "doc/Cupti/structCUpti__ActivityEvent.html", "path_type": "hardlink", "sha256": "518e5cab848dadb0898a86c4d180c7adecb46ad64b7b8a2e5164f4f124dd7954", "sha256_in_prefix": "518e5cab848dadb0898a86c4d180c7adecb46ad64b7b8a2e5164f4f124dd7954", "size_in_bytes": 6536}, {"_path": "doc/Cupti/structCUpti__ActivityEventInstance.html", "path_type": "hardlink", "sha256": "ecdd8792d926e758b0365ec7f78644226e0049bc92638c6243141a4e8154e174", "sha256_in_prefix": "ecdd8792d926e758b0365ec7f78644226e0049bc92638c6243141a4e8154e174", "size_in_bytes": 8513}, {"_path": "doc/Cupti/structCUpti__ActivityExternalCorrelation.html", "path_type": "hardlink", "sha256": "fca7a41900d0df691b92122c57969cb36b373124b465b34e5824650ffdb0d29d", "sha256_in_prefix": "fca7a41900d0df691b92122c57969cb36b373124b465b34e5824650ffdb0d29d", "size_in_bytes": 7090}, {"_path": "doc/Cupti/structCUpti__ActivityFunction.html", "path_type": "hardlink", "sha256": "9df5dd79c82cfa48bd94f56f45c778b330a46e76e73edfc2af7277985244f776", "sha256_in_prefix": "9df5dd79c82cfa48bd94f56f45c778b330a46e76e73edfc2af7277985244f776", "size_in_bytes": 6894}, {"_path": "doc/Cupti/structCUpti__ActivityGlobalAccess.html", "path_type": "hardlink", "sha256": "de4530faf6e6ff6b46e18387d05571b02559e7241ef2f4ec835e0cf3f0af6d2f", "sha256_in_prefix": "de4530faf6e6ff6b46e18387d05571b02559e7241ef2f4ec835e0cf3f0af6d2f", "size_in_bytes": 9322}, {"_path": "doc/Cupti/structCUpti__ActivityGlobalAccess2.html", "path_type": "hardlink", "sha256": "4ca0ba4c2086260c956da707723f3972331f298b601054fe85c0b0bc16fac2bc", "sha256_in_prefix": "4ca0ba4c2086260c956da707723f3972331f298b601054fe85c0b0bc16fac2bc", "size_in_bytes": 11889}, {"_path": "doc/Cupti/structCUpti__ActivityGlobalAccess3.html", "path_type": "hardlink", "sha256": "b6db5953c9d2fbc77da30966152a033f582bd3a2a5b2ea68345ac8cf9077d489", "sha256_in_prefix": "b6db5953c9d2fbc77da30966152a033f582bd3a2a5b2ea68345ac8cf9077d489", "size_in_bytes": 10846}, {"_path": "doc/Cupti/structCUpti__ActivityGraphTrace.html", "path_type": "hardlink", "sha256": "2a282efe201ca7ae8660709e5531dfe38979a657fb430a8607aa707eb4fbf2db", "sha256_in_prefix": "2a282efe201ca7ae8660709e5531dfe38979a657fb430a8607aa707eb4fbf2db", "size_in_bytes": 9811}, {"_path": "doc/Cupti/structCUpti__ActivityInstantaneousEvent.html", "path_type": "hardlink", "sha256": "8c49d6f15f51659226ea9d1d16727fc2b507d11986b929f63dc01ae76a5c5cc5", "sha256_in_prefix": "8c49d6f15f51659226ea9d1d16727fc2b507d11986b929f63dc01ae76a5c5cc5", "size_in_bytes": 7403}, {"_path": "doc/Cupti/structCUpti__ActivityInstantaneousEventInstance.html", "path_type": "hardlink", "sha256": "c50d30a0adc14f2e2808b63cf0cf02b93131cca3db2886c2c0e55d733d3c1cc4", "sha256_in_prefix": "c50d30a0adc14f2e2808b63cf0cf02b93131cca3db2886c2c0e55d733d3c1cc4", "size_in_bytes": 8651}, {"_path": "doc/Cupti/structCUpti__ActivityInstantaneousMetric.html", "path_type": "hardlink", "sha256": "e0209a6bfbdfddd5d03949f19aab582227d06795436e0eac480ee48537294428", "sha256_in_prefix": "e0209a6bfbdfddd5d03949f19aab582227d06795436e0eac480ee48537294428", "size_in_bytes": 8591}, {"_path": "doc/Cupti/structCUpti__ActivityInstantaneousMetricInstance.html", "path_type": "hardlink", "sha256": "2094865b982739af07caabc4cd080f80840821cd7340ffbac8f6594963590ca4", "sha256_in_prefix": "2094865b982739af07caabc4cd080f80840821cd7340ffbac8f6594963590ca4", "size_in_bytes": 9875}, {"_path": "doc/Cupti/structCUpti__ActivityInstructionCorrelation.html", "path_type": "hardlink", "sha256": "ebe41af5c9a99fa77c4ec678052c547cb0d69bef2baf0b2ab97b5f18cc9f98f6", "sha256_in_prefix": "ebe41af5c9a99fa77c4ec678052c547cb0d69bef2baf0b2ab97b5f18cc9f98f6", "size_in_bytes": 7385}, {"_path": "doc/Cupti/structCUpti__ActivityInstructionExecution.html", "path_type": "hardlink", "sha256": "72377eeffa1663980198e01c4015cb4417c2d07433cebe3094a66291a400a1d5", "sha256_in_prefix": "72377eeffa1663980198e01c4015cb4417c2d07433cebe3094a66291a400a1d5", "size_in_bytes": 11123}, {"_path": "doc/Cupti/structCUpti__ActivityJit.html", "path_type": "hardlink", "sha256": "1f7c9c9f838cd977b25889b6898142a2ffe29b9bff0cb2375606283fc1a345bc", "sha256_in_prefix": "1f7c9c9f838cd977b25889b6898142a2ffe29b9bff0cb2375606283fc1a345bc", "size_in_bytes": 11668}, {"_path": "doc/Cupti/structCUpti__ActivityKernel.html", "path_type": "hardlink", "sha256": "39f4498e3e7cc15a9cc6f3bed79dcd68523331023cd896b184156ff87955197a", "sha256_in_prefix": "39f4498e3e7cc15a9cc6f3bed79dcd68523331023cd896b184156ff87955197a", "size_in_bytes": 22272}, {"_path": "doc/Cupti/structCUpti__ActivityKernel2.html", "path_type": "hardlink", "sha256": "b4e223e7af67ebe8c971f89c3e3a7926d097c46059d1bbeec10f373fb8e79eac", "sha256_in_prefix": "b4e223e7af67ebe8c971f89c3e3a7926d097c46059d1bbeec10f373fb8e79eac", "size_in_bytes": 23244}, {"_path": "doc/Cupti/structCUpti__ActivityKernel3.html", "path_type": "hardlink", "sha256": "89ab60df27942e8f841d4344f17ef87554f4b0d7f23a21461b22a7c04c3b3116", "sha256_in_prefix": "89ab60df27942e8f841d4344f17ef87554f4b0d7f23a21461b22a7c04c3b3116", "size_in_bytes": 25873}, {"_path": "doc/Cupti/structCUpti__ActivityKernel4.html", "path_type": "hardlink", "sha256": "c5ef18a27e1aebe4bfb71a097f0907d8ae3ebd4e17796b0b41faa83917fbfdcf", "sha256_in_prefix": "c5ef18a27e1aebe4bfb71a097f0907d8ae3ebd4e17796b0b41faa83917fbfdcf", "size_in_bytes": 33992}, {"_path": "doc/Cupti/structCUpti__ActivityKernel5.html", "path_type": "hardlink", "sha256": "dc333263d39f7559b87cd8352090a765c916c18647a1c59456163d50d86fc340", "sha256_in_prefix": "dc333263d39f7559b87cd8352090a765c916c18647a1c59456163d50d86fc340", "size_in_bytes": 36897}, {"_path": "doc/Cupti/structCUpti__ActivityKernel6.html", "path_type": "hardlink", "sha256": "7c00a217aaa2307d86fa3c82b979e327cc32ad66c8b5cc00dba3dfb3f8407b57", "sha256_in_prefix": "7c00a217aaa2307d86fa3c82b979e327cc32ad66c8b5cc00dba3dfb3f8407b57", "size_in_bytes": 37850}, {"_path": "doc/Cupti/structCUpti__ActivityKernel7.html", "path_type": "hardlink", "sha256": "e75bdce11fdc6248a2b717525d727d850b065f9d8f2ca4c2a721ed12e3a64c5a", "sha256_in_prefix": "e75bdce11fdc6248a2b717525d727d850b065f9d8f2ca4c2a721ed12e3a64c5a", "size_in_bytes": 39458}, {"_path": "doc/Cupti/structCUpti__ActivityKernel8.html", "path_type": "hardlink", "sha256": "8111a836c4c6bd1bf3cd12af1529572bd3fdb95e86c169e4242a1e9d3e03cfe8", "sha256_in_prefix": "8111a836c4c6bd1bf3cd12af1529572bd3fdb95e86c169e4242a1e9d3e03cfe8", "size_in_bytes": 43632}, {"_path": "doc/Cupti/structCUpti__ActivityKernel9.html", "path_type": "hardlink", "sha256": "94c8f9ed99241bdb5e836072185d43e2ca0fec4c988fc3b54f09a8b04a019616", "sha256_in_prefix": "94c8f9ed99241bdb5e836072185d43e2ca0fec4c988fc3b54f09a8b04a019616", "size_in_bytes": 45317}, {"_path": "doc/Cupti/structCUpti__ActivityMarker.html", "path_type": "hardlink", "sha256": "27eb912b999f6cbffd615427bfcba63066ebd533fbd2076590990e15e780731a", "sha256_in_prefix": "27eb912b999f6cbffd615427bfcba63066ebd533fbd2076590990e15e780731a", "size_in_bytes": 8756}, {"_path": "doc/Cupti/structCUpti__ActivityMarker2.html", "path_type": "hardlink", "sha256": "c6d728cef8626f4d42a05957643cf8cdc825db3075ccd51cf647289ed1dd8afc", "sha256_in_prefix": "c6d728cef8626f4d42a05957643cf8cdc825db3075ccd51cf647289ed1dd8afc", "size_in_bytes": 10147}, {"_path": "doc/Cupti/structCUpti__ActivityMarkerData.html", "path_type": "hardlink", "sha256": "7439d13ffad91e6e56ddc78aa979812ecf955eb42745116c213ba02a7c44c935", "sha256_in_prefix": "7439d13ffad91e6e56ddc78aa979812ecf955eb42745116c213ba02a7c44c935", "size_in_bytes": 8304}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpy.html", "path_type": "hardlink", "sha256": "b97938c09c2caac09d395574dc3c6c72892626c8b96b93b576d886fc8eacb3be", "sha256_in_prefix": "b97938c09c2caac09d395574dc3c6c72892626c8b96b93b576d886fc8eacb3be", "size_in_bytes": 14744}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpy3.html", "path_type": "hardlink", "sha256": "a10d050c69e21f281693abe9e6142b772aea5fb4b1dcbbc1b4be9286058a36a4", "sha256_in_prefix": "a10d050c69e21f281693abe9e6142b772aea5fb4b1dcbbc1b4be9286058a36a4", "size_in_bytes": 15722}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpy4.html", "path_type": "hardlink", "sha256": "fd215fa368bd6080fd3b54cd921dfa71680e4ca9ba1d435afd6abb2611e3a4de", "sha256_in_prefix": "fd215fa368bd6080fd3b54cd921dfa71680e4ca9ba1d435afd6abb2611e3a4de", "size_in_bytes": 17394}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpy5.html", "path_type": "hardlink", "sha256": "936a8818b37ef5ae75c30c7fd5afd198931dddf89c211cb388d8bc21cb55c013", "sha256_in_prefix": "936a8818b37ef5ae75c30c7fd5afd198931dddf89c211cb388d8bc21cb55c013", "size_in_bytes": 18960}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpyPtoP.html", "path_type": "hardlink", "sha256": "bc0ba6fac883428e5c2841cad4ef90001eff5b42aefa28e1e35eeefa1ff9e334", "sha256_in_prefix": "bc0ba6fac883428e5c2841cad4ef90001eff5b42aefa28e1e35eeefa1ff9e334", "size_in_bytes": 18380}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpyPtoP2.html", "path_type": "hardlink", "sha256": "cc1800550a8870e1c431fbee4a5ded7e4d9c14a1941fd3590c2aeccd163534c0", "sha256_in_prefix": "cc1800550a8870e1c431fbee4a5ded7e4d9c14a1941fd3590c2aeccd163534c0", "size_in_bytes": 19104}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpyPtoP3.html", "path_type": "hardlink", "sha256": "18ad4d5ec4b0c870c8e4e5ed4bb758c4df6611ef306ba3ec5c5c0e3d993ef638", "sha256_in_prefix": "18ad4d5ec4b0c870c8e4e5ed4bb758c4df6611ef306ba3ec5c5c0e3d993ef638", "size_in_bytes": 20808}, {"_path": "doc/Cupti/structCUpti__ActivityMemcpyPtoP4.html", "path_type": "hardlink", "sha256": "936e7287a0acee93a09d203f018c90f1177a674732accdc4034a22614fc7552d", "sha256_in_prefix": "936e7287a0acee93a09d203f018c90f1177a674732accdc4034a22614fc7552d", "size_in_bytes": 21627}, {"_path": "doc/Cupti/structCUpti__ActivityMemory.html", "path_type": "hardlink", "sha256": "9ba14883099cb8e58ecdf9e2e510a837d59b955c6ae9fa56853fdf1554706a94", "sha256_in_prefix": "9ba14883099cb8e58ecdf9e2e510a837d59b955c6ae9fa56853fdf1554706a94", "size_in_bytes": 12671}, {"_path": "doc/Cupti/structCUpti__ActivityMemory2.html", "path_type": "hardlink", "sha256": "230918589935ac37a9a4fee4b2c3479d5d8b50e72ba9dcfcead78b33664f8803", "sha256_in_prefix": "230918589935ac37a9a4fee4b2c3479d5d8b50e72ba9dcfcead78b33664f8803", "size_in_bytes": 20115}, {"_path": "doc/Cupti/structCUpti__ActivityMemory3.html", "path_type": "hardlink", "sha256": "f4baed9b4f2bfb89cee8de246a324d981b44dbd9d110df60534d226d4ba11208", "sha256_in_prefix": "f4baed9b4f2bfb89cee8de246a324d981b44dbd9d110df60534d226d4ba11208", "size_in_bytes": 16299}, {"_path": "doc/Cupti/structCUpti__ActivityMemory3_1_1PACKED__ALIGNMENT.html", "path_type": "hardlink", "sha256": "a6e637739d7939500d18bed630dd02cbc6d0278efada7b630ecea33839649b94", "sha256_in_prefix": "a6e637739d7939500d18bed630dd02cbc6d0278efada7b630ecea33839649b94", "size_in_bytes": 7757}, {"_path": "doc/Cupti/structCUpti__ActivityMemoryPool.html", "path_type": "hardlink", "sha256": "fc24349d0a4e0d0f96e54526fdd5c18b01ad19620fe7553126ec6719301ff12c", "sha256_in_prefix": "fc24349d0a4e0d0f96e54526fdd5c18b01ad19620fe7553126ec6719301ff12c", "size_in_bytes": 13510}, {"_path": "doc/Cupti/structCUpti__ActivityMemoryPool2.html", "path_type": "hardlink", "sha256": "a58434d1964771fbb8b85c624c4ef908ad7f7ddaa0d341f5bd2f9d968267ed20", "sha256_in_prefix": "a58434d1964771fbb8b85c624c4ef908ad7f7ddaa0d341f5bd2f9d968267ed20", "size_in_bytes": 14575}, {"_path": "doc/Cupti/structCUpti__ActivityMemset.html", "path_type": "hardlink", "sha256": "378a8892bbb5bfebc6a3e85698d9449d139f73d61d020b6a621f0d3bbd018923", "sha256_in_prefix": "378a8892bbb5bfebc6a3e85698d9449d139f73d61d020b6a621f0d3bbd018923", "size_in_bytes": 12316}, {"_path": "doc/Cupti/structCUpti__ActivityMemset2.html", "path_type": "hardlink", "sha256": "94135940ee722d591dd5967f1ce0f8b49f5039b17b8683ec55b4ea3cf4caec14", "sha256_in_prefix": "94135940ee722d591dd5967f1ce0f8b49f5039b17b8683ec55b4ea3cf4caec14", "size_in_bytes": 13290}, {"_path": "doc/Cupti/structCUpti__ActivityMemset3.html", "path_type": "hardlink", "sha256": "0fb092c2aa110c09ef3b1f571188aea539f6652f3dee8f804895845656eeac45", "sha256_in_prefix": "0fb092c2aa110c09ef3b1f571188aea539f6652f3dee8f804895845656eeac45", "size_in_bytes": 14966}, {"_path": "doc/Cupti/structCUpti__ActivityMemset4.html", "path_type": "hardlink", "sha256": "ab237bbe40eb64bc84b0f1a60b1f9807c06be67c93daaa520fd6135f292ad3c7", "sha256_in_prefix": "ab237bbe40eb64bc84b0f1a60b1f9807c06be67c93daaa520fd6135f292ad3c7", "size_in_bytes": 16541}, {"_path": "doc/Cupti/structCUpti__ActivityMetric.html", "path_type": "hardlink", "sha256": "e700863330f838172ded00347a572da27474fe9c5c3c1fbb549247be20366d4a", "sha256_in_prefix": "e700863330f838172ded00347a572da27474fe9c5c3c1fbb549247be20366d4a", "size_in_bytes": 7520}, {"_path": "doc/Cupti/structCUpti__ActivityMetricInstance.html", "path_type": "hardlink", "sha256": "09fffdaa6f873472e1f8cc6fee5c9f27f98759f757bfef4dd539b2c62c9e2376", "sha256_in_prefix": "09fffdaa6f873472e1f8cc6fee5c9f27f98759f757bfef4dd539b2c62c9e2376", "size_in_bytes": 8720}, {"_path": "doc/Cupti/structCUpti__ActivityModule.html", "path_type": "hardlink", "sha256": "e6ad0201929bc2f303aedffed368089bbe6d0e928d75d2d64917f4f0f9d77963", "sha256_in_prefix": "e6ad0201929bc2f303aedffed368089bbe6d0e928d75d2d64917f4f0f9d77963", "size_in_bytes": 6845}, {"_path": "doc/Cupti/structCUpti__ActivityName.html", "path_type": "hardlink", "sha256": "b4433b8afd81ceb78349e54d87ab6f4818caeede9871240d92b3c1adb7d47902", "sha256_in_prefix": "b4433b8afd81ceb78349e54d87ab6f4818caeede9871240d92b3c1adb7d47902", "size_in_bytes": 5535}, {"_path": "doc/Cupti/structCUpti__ActivityNvLink.html", "path_type": "hardlink", "sha256": "8723be755dc4619b8e9e854e81007ffd6a945d24045348057a3d8dd36e658dab", "sha256_in_prefix": "8723be755dc4619b8e9e854e81007ffd6a945d24045348057a3d8dd36e658dab", "size_in_bytes": 14242}, {"_path": "doc/Cupti/structCUpti__ActivityNvLink2.html", "path_type": "hardlink", "sha256": "e655cbeec912db833ef42948e639c98f7ede85b274c7ca91997f63d6ec0c255e", "sha256_in_prefix": "e655cbeec912db833ef42948e639c98f7ede85b274c7ca91997f63d6ec0c255e", "size_in_bytes": 14399}, {"_path": "doc/Cupti/structCUpti__ActivityNvLink3.html", "path_type": "hardlink", "sha256": "0429c66ded230f489556e4be6b1fb29f5ee024732c931b2d34de949b698fc7f6", "sha256_in_prefix": "0429c66ded230f489556e4be6b1fb29f5ee024732c931b2d34de949b698fc7f6", "size_in_bytes": 15970}, {"_path": "doc/Cupti/structCUpti__ActivityNvLink4.html", "path_type": "hardlink", "sha256": "29952c94ae10ea8f4765a7f3a8652450d6faaaaadc4a7ac67ba50578e1e0e35a", "sha256_in_prefix": "29952c94ae10ea8f4765a7f3a8652450d6faaaaadc4a7ac67ba50578e1e0e35a", "size_in_bytes": 15798}, {"_path": "doc/Cupti/structCUpti__ActivityOpenAcc.html", "path_type": "hardlink", "sha256": "1ba5acc749c383ab13164c1a9b0ea02a55263ac397cc2f6ddcda97469d5ef0ac", "sha256_in_prefix": "1ba5acc749c383ab13164c1a9b0ea02a55263ac397cc2f6ddcda97469d5ef0ac", "size_in_bytes": 21899}, {"_path": "doc/Cupti/structCUpti__ActivityOpenAccData.html", "path_type": "hardlink", "sha256": "0b776adc9bfae9549661a1165c3ab3d1345b5ed814fd619bedd10a7d387278f3", "sha256_in_prefix": "0b776adc9bfae9549661a1165c3ab3d1345b5ed814fd619bedd10a7d387278f3", "size_in_bytes": 14642}, {"_path": "doc/Cupti/structCUpti__ActivityOpenAccLaunch.html", "path_type": "hardlink", "sha256": "997f4596efc3a922c85665fc8df8a68a846715d79e8074f1e32a590d9c8a91fa", "sha256_in_prefix": "997f4596efc3a922c85665fc8df8a68a846715d79e8074f1e32a590d9c8a91fa", "size_in_bytes": 27672}, {"_path": "doc/Cupti/structCUpti__ActivityOpenAccOther.html", "path_type": "hardlink", "sha256": "f23477e02633b4a84065d4ebea9045f95736eec70d1b7de3ccae6664ece2d740", "sha256_in_prefix": "f23477e02633b4a84065d4ebea9045f95736eec70d1b7de3ccae6664ece2d740", "size_in_bytes": 23368}, {"_path": "doc/Cupti/structCUpti__ActivityOpenMp.html", "path_type": "hardlink", "sha256": "3c951ccda8419ab7727b421d17ec10dbd653342aa698a61ad9b4e0ecdbf1e094", "sha256_in_prefix": "3c951ccda8419ab7727b421d17ec10dbd653342aa698a61ad9b4e0ecdbf1e094", "size_in_bytes": 8381}, {"_path": "doc/Cupti/structCUpti__ActivityOverhead.html", "path_type": "hardlink", "sha256": "baf4e0fe8f9ba51c049733bb0f5efe9237b9d9c6b3bdc3da30d70702a8f844e8", "sha256_in_prefix": "baf4e0fe8f9ba51c049733bb0f5efe9237b9d9c6b3bdc3da30d70702a8f844e8", "size_in_bytes": 7702}, {"_path": "doc/Cupti/structCUpti__ActivityPCSampling.html", "path_type": "hardlink", "sha256": "2c18cf16a76d72bb89c64a3d3687d7b0fea35cd10fa6ec36720d2da34cec7f28", "sha256_in_prefix": "2c18cf16a76d72bb89c64a3d3687d7b0fea35cd10fa6ec36720d2da34cec7f28", "size_in_bytes": 9374}, {"_path": "doc/Cupti/structCUpti__ActivityPCSampling2.html", "path_type": "hardlink", "sha256": "09796cfc601ebac7c40e7198672ee187de4133db2d03621126646f2020b309c4", "sha256_in_prefix": "09796cfc601ebac7c40e7198672ee187de4133db2d03621126646f2020b309c4", "size_in_bytes": 10462}, {"_path": "doc/Cupti/structCUpti__ActivityPCSampling3.html", "path_type": "hardlink", "sha256": "c60f473993e576485e48b1b9188d49eed7aac5fd3ee9b65caa4f9e979c946233", "sha256_in_prefix": "c60f473993e576485e48b1b9188d49eed7aac5fd3ee9b65caa4f9e979c946233", "size_in_bytes": 10235}, {"_path": "doc/Cupti/structCUpti__ActivityPCSamplingConfig.html", "path_type": "hardlink", "sha256": "c4083a59c50dc49c2ee1eadc5622a8bbdaee6a4830999fb0d165fd8d5895f955", "sha256_in_prefix": "c4083a59c50dc49c2ee1eadc5622a8bbdaee6a4830999fb0d165fd8d5895f955", "size_in_bytes": 5437}, {"_path": "doc/Cupti/structCUpti__ActivityPCSamplingRecordInfo.html", "path_type": "hardlink", "sha256": "9709597931cb0a6a375de1b9c96c1c8ffcdc4d1c419f4e68033519ff42ac93a2", "sha256_in_prefix": "9709597931cb0a6a375de1b9c96c1c8ffcdc4d1c419f4e68033519ff42ac93a2", "size_in_bytes": 6478}, {"_path": "doc/Cupti/structCUpti__ActivityPcie.html", "path_type": "hardlink", "sha256": "01b64da2b6db05c7c7745f51c84c9647936181564d3fe6f153b8663c7d72ad92", "sha256_in_prefix": "01b64da2b6db05c7c7745f51c84c9647936181564d3fe6f153b8663c7d72ad92", "size_in_bytes": 16029}, {"_path": "doc/Cupti/structCUpti__ActivityPreemption.html", "path_type": "hardlink", "sha256": "b5e17140529e7b6ca28f503e1253903d6cb3ea02912410f2e5e6ddd40368e5d0", "sha256_in_prefix": "b5e17140529e7b6ca28f503e1253903d6cb3ea02912410f2e5e6ddd40368e5d0", "size_in_bytes": 8680}, {"_path": "doc/Cupti/structCUpti__ActivitySharedAccess.html", "path_type": "hardlink", "sha256": "b528d1c6f317b2e00d72771c0033e204d1ebe305a7a831d0983a9a0c0b997a72", "sha256_in_prefix": "b528d1c6f317b2e00d72771c0033e204d1ebe305a7a831d0983a9a0c0b997a72", "size_in_bytes": 11619}, {"_path": "doc/Cupti/structCUpti__ActivitySourceLocator.html", "path_type": "hardlink", "sha256": "b4fd415c91dd2d32c08516e47eb90f8103854ab8df02ab6a030417ebfd5a862d", "sha256_in_prefix": "b4fd415c91dd2d32c08516e47eb90f8103854ab8df02ab6a030417ebfd5a862d", "size_in_bytes": 5255}, {"_path": "doc/Cupti/structCUpti__ActivityStream.html", "path_type": "hardlink", "sha256": "7974336536a1fe5423dbb38c40a7fccdc63f57d0162ddde4a7035a88ce31f60e", "sha256_in_prefix": "7974336536a1fe5423dbb38c40a7fccdc63f57d0162ddde4a7035a88ce31f60e", "size_in_bytes": 6920}, {"_path": "doc/Cupti/structCUpti__ActivitySynchronization.html", "path_type": "hardlink", "sha256": "fcf7e7677ed6f7a37fce59e3447566a0fe3ce33a1c519e5f60dec12f0fbf3770", "sha256_in_prefix": "fcf7e7677ed6f7a37fce59e3447566a0fe3ce33a1c519e5f60dec12f0fbf3770", "size_in_bytes": 9660}, {"_path": "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounter.html", "path_type": "hardlink", "sha256": "81245cfa4ec4ec29a077dd52f6126354e6d2c6ae043d087bd587b17502383c27", "sha256_in_prefix": "81245cfa4ec4ec29a077dd52f6126354e6d2c6ae043d087bd587b17502383c27", "size_in_bytes": 9759}, {"_path": "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounter2.html", "path_type": "hardlink", "sha256": "ab07301ed425a2a15aacc2d97da61944bdffdf63cefb99aa7c6ce40ddda5d2f0", "sha256_in_prefix": "ab07301ed425a2a15aacc2d97da61944bdffdf63cefb99aa7c6ce40ddda5d2f0", "size_in_bytes": 16320}, {"_path": "doc/Cupti/structCUpti__ActivityUnifiedMemoryCounterConfig.html", "path_type": "hardlink", "sha256": "e74e480b8d0813354493b058dc7903962eab5f3f0b14c9a951e1621cacb41b02", "sha256_in_prefix": "e74e480b8d0813354493b058dc7903962eab5f3f0b14c9a951e1621cacb41b02", "size_in_bytes": 6105}, {"_path": "doc/<PERSON>ti/structCUpti__CallbackData.html", "path_type": "hardlink", "sha256": "c9ed24713913b64ee3ed350442fc2a5d9e6998f121ebfec9dfecbe6540a22dc4", "sha256_in_prefix": "c9ed24713913b64ee3ed350442fc2a5d9e6998f121ebfec9dfecbe6540a22dc4", "size_in_bytes": 12150}, {"_path": "doc/Cupti/structCUpti__EventGroupSet.html", "path_type": "hardlink", "sha256": "2419b187bc82cddf4d11b157bf9646930dde26efa82925635ff6f91bb7a6d841", "sha256_in_prefix": "2419b187bc82cddf4d11b157bf9646930dde26efa82925635ff6f91bb7a6d841", "size_in_bytes": 3862}, {"_path": "doc/Cupti/structCUpti__EventGroupSets.html", "path_type": "hardlink", "sha256": "1b2208f6c00e3b9c27d27745b98603f8d8849a44c5696a894378b83a2ade0deb", "sha256_in_prefix": "1b2208f6c00e3b9c27d27745b98603f8d8849a44c5696a894378b83a2ade0deb", "size_in_bytes": 3877}, {"_path": "doc/Cupti/structCUpti__GetCubinCrcParams.html", "path_type": "hardlink", "sha256": "b7b56b9ee89e59709c912e83262122311e3bdc286b54c9ee8d8470ef66766ed1", "sha256_in_prefix": "b7b56b9ee89e59709c912e83262122311e3bdc286b54c9ee8d8470ef66766ed1", "size_in_bytes": 4893}, {"_path": "doc/Cupti/structCUpti__GetSassToSourceCorrelationParams.html", "path_type": "hardlink", "sha256": "cd52428d8cd7d1f603eab12fde6aecb287f9e899b4c7f2665fb3cd66497692e1", "sha256_in_prefix": "cd52428d8cd7d1f603eab12fde6aecb287f9e899b4c7f2665fb3cd66497692e1", "size_in_bytes": 8658}, {"_path": "doc/Cupti/structCUpti__GraphData.html", "path_type": "hardlink", "sha256": "0d0aa73859ac2932502056406d26075ddb5435b1dd9d82eeed085cb987c5da83", "sha256_in_prefix": "0d0aa73859ac2932502056406d26075ddb5435b1dd9d82eeed085cb987c5da83", "size_in_bytes": 8324}, {"_path": "doc/Cupti/structCUpti__ModuleResourceData.html", "path_type": "hardlink", "sha256": "da7fa46ae121c2f07c8dced3344778e4c98e7da2b112fb9f1787c6a79b901b79", "sha256_in_prefix": "da7fa46ae121c2f07c8dced3344778e4c98e7da2b112fb9f1787c6a79b901b79", "size_in_bytes": 4593}, {"_path": "doc/Cupti/structCUpti__NvtxData.html", "path_type": "hardlink", "sha256": "9da01562de7c78b21cc0df11d388f6c5e4a5920f880458e0a2735df114dfe56e", "sha256_in_prefix": "9da01562de7c78b21cc0df11d388f6c5e4a5920f880458e0a2735df114dfe56e", "size_in_bytes": 4821}, {"_path": "doc/Cupti/structCUpti__PCSamplingConfigurationInfo.html", "path_type": "hardlink", "sha256": "7e8cc1ada530f0a773bec17b955f7d3e8f8b43fdff0850c85e83b0a239a94205", "sha256_in_prefix": "7e8cc1ada530f0a773bec17b955f7d3e8f8b43fdff0850c85e83b0a239a94205", "size_in_bytes": 13729}, {"_path": "doc/Cupti/structCUpti__PCSamplingConfigurationInfoParams.html", "path_type": "hardlink", "sha256": "254211f4316f343f6fa36bb24e664c06471eba6c20b62faf7c2ff43c151dddb7", "sha256_in_prefix": "254211f4316f343f6fa36bb24e664c06471eba6c20b62faf7c2ff43c151dddb7", "size_in_bytes": 7144}, {"_path": "doc/Cupti/structCUpti__PCSamplingData.html", "path_type": "hardlink", "sha256": "f58ede7e8c4b7e93fb216bf9cd3687583a633832d498aac3ecc1fe761ad09047", "sha256_in_prefix": "f58ede7e8c4b7e93fb216bf9cd3687583a633832d498aac3ecc1fe761ad09047", "size_in_bytes": 11245}, {"_path": "doc/Cupti/structCUpti__PCSamplingDisableParams.html", "path_type": "hardlink", "sha256": "015820f1193e1e7ca78cc7141a2dea001ef21fe80e33e71c4df52c1bc0666ff0", "sha256_in_prefix": "015820f1193e1e7ca78cc7141a2dea001ef21fe80e33e71c4df52c1bc0666ff0", "size_in_bytes": 4182}, {"_path": "doc/Cupti/structCUpti__PCSamplingEnableParams.html", "path_type": "hardlink", "sha256": "3b438e3a5f1b136a2e48a0d189e254db5bb1ef86cd9243e34b52a902164f3dd1", "sha256_in_prefix": "3b438e3a5f1b136a2e48a0d189e254db5bb1ef86cd9243e34b52a902164f3dd1", "size_in_bytes": 4165}, {"_path": "doc/Cupti/structCUpti__PCSamplingGetDataParams.html", "path_type": "hardlink", "sha256": "c1c8f8841a25424bdb9932ea01b5ac526d8861a611d296ad66184b80e4653457", "sha256_in_prefix": "c1c8f8841a25424bdb9932ea01b5ac526d8861a611d296ad66184b80e4653457", "size_in_bytes": 5415}, {"_path": "doc/Cupti/structCUpti__PCSamplingGetNumStallReasonsParams.html", "path_type": "hardlink", "sha256": "38175b9caea51332d534f14c9bb3381a108fd54d5dfb91dc664c3ca552777aee", "sha256_in_prefix": "38175b9caea51332d534f14c9bb3381a108fd54d5dfb91dc664c3ca552777aee", "size_in_bytes": 5241}, {"_path": "doc/Cupti/structCUpti__PCSamplingGetStallReasonsParams.html", "path_type": "hardlink", "sha256": "6fbbf259431928ebf37695c7f5f5d870404d1caecfc637d0dd33686c9aa25250", "sha256_in_prefix": "6fbbf259431928ebf37695c7f5f5d870404d1caecfc637d0dd33686c9aa25250", "size_in_bytes": 6881}, {"_path": "doc/Cupti/structCUpti__PCSamplingPCData.html", "path_type": "hardlink", "sha256": "a79021a1c77961969520a1b2d5268947c821ae3a919204020c9d86de8af72e77", "sha256_in_prefix": "a79021a1c77961969520a1b2d5268947c821ae3a919204020c9d86de8af72e77", "size_in_bytes": 8465}, {"_path": "doc/Cupti/structCUpti__PCSamplingStallReason.html", "path_type": "hardlink", "sha256": "6d03597c9f1266d6f90a1c7a4951b7562f5c4f3ce4a072da2d3115421bf2b58a", "sha256_in_prefix": "6d03597c9f1266d6f90a1c7a4951b7562f5c4f3ce4a072da2d3115421bf2b58a", "size_in_bytes": 3287}, {"_path": "doc/Cupti/structCUpti__PCSamplingStartParams.html", "path_type": "hardlink", "sha256": "98a827a1531c624b3fec7075cb1c1fd0f710116cb64bfb2b024c2c8837ff2299", "sha256_in_prefix": "98a827a1531c624b3fec7075cb1c1fd0f710116cb64bfb2b024c2c8837ff2299", "size_in_bytes": 4148}, {"_path": "doc/Cupti/structCUpti__PCSamplingStopParams.html", "path_type": "hardlink", "sha256": "922787915ffb176b8eca1141b0027d5aeb57f6f28df7833954388580b0d4a5d1", "sha256_in_prefix": "922787915ffb176b8eca1141b0027d5aeb57f6f28df7833954388580b0d4a5d1", "size_in_bytes": 4131}, {"_path": "doc/Cupti/structCUpti__Profiler__BeginPass__Params.html", "path_type": "hardlink", "sha256": "8f5e2662ebeef766acabfb2b04c5ddd0d59d756667d3e6c74622c76fce0943f1", "sha256_in_prefix": "8f5e2662ebeef766acabfb2b04c5ddd0d59d756667d3e6c74622c76fce0943f1", "size_in_bytes": 3176}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__BeginSession__Params.html", "path_type": "hardlink", "sha256": "caea321976f18e428a933cb90594cac771753538cb7429a141773ec7051d9b63", "sha256_in_prefix": "caea321976f18e428a933cb90594cac771753538cb7429a141773ec7051d9b63", "size_in_bytes": 9164}, {"_path": "doc/Cupti/structCUpti__Profiler__CounterDataImageOptions.html", "path_type": "hardlink", "sha256": "ce57858977b3d086feb0b36ffa608a1b211955494e7a2e737f2be281d2f526fe", "sha256_in_prefix": "ce57858977b3d086feb0b36ffa608a1b211955494e7a2e737f2be281d2f526fe", "size_in_bytes": 6145}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__CalculateScratchBufferSize__Params.html", "path_type": "hardlink", "sha256": "c7781b8553bc915b041e6a11256aa73fa8d03488289960042d3d1ee2f53cf8c5", "sha256_in_prefix": "c7781b8553bc915b041e6a11256aa73fa8d03488289960042d3d1ee2f53cf8c5", "size_in_bytes": 4812}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__CalculateSize__Params.html", "path_type": "hardlink", "sha256": "a313c21859dd39d7fadfb766e1cad18013d20c30fce22e56edaa311de0a15a88", "sha256_in_prefix": "a313c21859dd39d7fadfb766e1cad18013d20c30fce22e56edaa311de0a15a88", "size_in_bytes": 4763}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__InitializeScratchBuffer__Params.html", "path_type": "hardlink", "sha256": "d768a573815185ce63bef06e3e2912b71757b6914a65ee339dad1ed8f14be81c", "sha256_in_prefix": "d768a573815185ce63bef06e3e2912b71757b6914a65ee339dad1ed8f14be81c", "size_in_bytes": 5484}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__CounterDataImage__Initialize__Params.html", "path_type": "hardlink", "sha256": "e4a7d108bf83f1038b1ddb443d5978291c1f501a87c8499fe32740d37b3bbd19", "sha256_in_prefix": "e4a7d108bf83f1038b1ddb443d5978291c1f501a87c8499fe32740d37b3bbd19", "size_in_bytes": 5371}, {"_path": "doc/<PERSON>ti/structCUpti__Profiler__DeInitialize__Params.html", "path_type": "hardlink", "sha256": "f8488f0863aa8126dd3c36803c54d920f71144f66539423bc174d655a0b7910f", "sha256_in_prefix": "f8488f0863aa8126dd3c36803c54d920f71144f66539423bc174d655a0b7910f", "size_in_bytes": 2680}, {"_path": "doc/Cupti/structCUpti__Profiler__DeviceSupported__Params.html", "path_type": "hardlink", "sha256": "3de06ad07db6921715e4242d7e76e02326df40d65d41f425ad3596c73d388902", "sha256_in_prefix": "3de06ad07db6921715e4242d7e76e02326df40d65d41f425ad3596c73d388902", "size_in_bytes": 8374}, {"_path": "doc/Cupti/structCUpti__Profiler__DisableProfiling__Params.html", "path_type": "hardlink", "sha256": "7bd5a720c1cdc091b53e5937d85c5545df2d09547024dbcafbe6c60cbbfa29c0", "sha256_in_prefix": "7bd5a720c1cdc091b53e5937d85c5545df2d09547024dbcafbe6c60cbbfa29c0", "size_in_bytes": 3253}, {"_path": "doc/Cupti/structCUpti__Profiler__EnableProfiling__Params.html", "path_type": "hardlink", "sha256": "acf62ba9220b70e65934ad24e28644c10ed9af0b3c543559a33ab4d8bbc75a95", "sha256_in_prefix": "acf62ba9220b70e65934ad24e28644c10ed9af0b3c543559a33ab4d8bbc75a95", "size_in_bytes": 3242}, {"_path": "doc/Cupti/structCUpti__Profiler__EndPass__Params.html", "path_type": "hardlink", "sha256": "6d47cf3096819ff34412b5a3d02966833dd04eedf58c304c53032f5ff4fbba34", "sha256_in_prefix": "6d47cf3096819ff34412b5a3d02966833dd04eedf58c304c53032f5ff4fbba34", "size_in_bytes": 4817}, {"_path": "doc/Cupti/structCUpti__Profiler__EndSession__Params.html", "path_type": "hardlink", "sha256": "cb17d1c17fd3950814eabbb64f9d3362b6e2215edebae6f8bee341768561946b", "sha256_in_prefix": "cb17d1c17fd3950814eabbb64f9d3362b6e2215edebae6f8bee341768561946b", "size_in_bytes": 3187}, {"_path": "doc/Cupti/structCUpti__Profiler__FlushCounterData__Params.html", "path_type": "hardlink", "sha256": "14ba344e59fe448944ffb6141b60e78c0fd6ea0d58472114b159d190ee422e0c", "sha256_in_prefix": "14ba344e59fe448944ffb6141b60e78c0fd6ea0d58472114b159d190ee422e0c", "size_in_bytes": 4453}, {"_path": "doc/Cupti/structCUpti__Profiler__GetCounterAvailability__Params.html", "path_type": "hardlink", "sha256": "5b14378cc13baefa780a700d85ea04e05ca30e9297226b126375cf51840b4ce7", "sha256_in_prefix": "5b14378cc13baefa780a700d85ea04e05ca30e9297226b126375cf51840b4ce7", "size_in_bytes": 5165}, {"_path": "doc/Cupti/structCUpti__Profiler__Initialize__Params.html", "path_type": "hardlink", "sha256": "ecfe5c875444a9e613002632e8dbeaba3a730d93bc06223b817fb1fa1cba6a39", "sha256_in_prefix": "ecfe5c875444a9e613002632e8dbeaba3a730d93bc06223b817fb1fa1cba6a39", "size_in_bytes": 2662}, {"_path": "doc/Cupti/structCUpti__Profiler__IsPassCollected__Params.html", "path_type": "hardlink", "sha256": "bb183eb6fd6cd3efeec4ee6976cd22d8015f24f9d2a5162346e317303721fcac", "sha256_in_prefix": "bb183eb6fd6cd3efeec4ee6976cd22d8015f24f9d2a5162346e317303721fcac", "size_in_bytes": 5593}, {"_path": "doc/Cupti/structCUpti__Profiler__SetConfig__Params.html", "path_type": "hardlink", "sha256": "51aed97c4a99cf93bccf64eabbd4e53e4adf407c0af7b3815ca58fc0738e94ee", "sha256_in_prefix": "51aed97c4a99cf93bccf64eabbd4e53e4adf407c0af7b3815ca58fc0738e94ee", "size_in_bytes": 6656}, {"_path": "doc/Cupti/structCUpti__Profiler__UnsetConfig__Params.html", "path_type": "hardlink", "sha256": "4d221d0c81bdee9b99366f6283b1e6e65333225a7c18f672c0edec0d947414ab", "sha256_in_prefix": "4d221d0c81bdee9b99366f6283b1e6e65333225a7c18f672c0edec0d947414ab", "size_in_bytes": 3198}, {"_path": "doc/Cupti/structCUpti__ResourceData.html", "path_type": "hardlink", "sha256": "d808b396c6a393c24bb8be50a726930a37fa8a14f7cffda2c7ba7c4ddf2b5506", "sha256_in_prefix": "d808b396c6a393c24bb8be50a726930a37fa8a14f7cffda2c7ba7c4ddf2b5506", "size_in_bytes": 4834}, {"_path": "doc/<PERSON>ti/structCUpti__SynchronizeData.html", "path_type": "hardlink", "sha256": "44f96848e3342223ae238e34f948cd719fda9c7987a6e3cd827be1505bd1d0f1", "sha256_in_prefix": "44f96848e3342223ae238e34f948cd719fda9c7987a6e3cd827be1505bd1d0f1", "size_in_bytes": 3760}, {"_path": "doc/Cupti/structHeader.html", "path_type": "hardlink", "sha256": "f2ee2af90a9f2c1fc338c35a91bee873ca4d50e32e7ceb94dd2f1bdfb56f6ec0", "sha256_in_prefix": "f2ee2af90a9f2c1fc338c35a91bee873ca4d50e32e7ceb94dd2f1bdfb56f6ec0", "size_in_bytes": 3093}, {"_path": "doc/Cupti/structNV_1_1Cupti_1_1Checkpoint_1_1CUpti__Checkpoint.html", "path_type": "hardlink", "sha256": "484ebf2311f890c70fd35094e7baf6f153f6b3e50d3c6641ee59fc8a78325ca9", "sha256_in_prefix": "484ebf2311f890c70fd35094e7baf6f153f6b3e50d3c6641ee59fc8a78325ca9", "size_in_bytes": 6887}, {"_path": "doc/Cupti/structPcSamplingStallReasons.html", "path_type": "hardlink", "sha256": "3590d2c98835f65d2e3612d865285cb00807d911018ca15c3be2167f24a1b65b", "sha256_in_prefix": "3590d2c98835f65d2e3612d865285cb00807d911018ca15c3be2167f24a1b65b", "size_in_bytes": 4088}, {"_path": "doc/<PERSON>ti/tab_b.gif", "path_type": "hardlink", "sha256": "50eb5bf4441ea3073ec1ca36dd63bc2b1eb736f514c6de2bc648e98f0fe9cecd", "sha256_in_prefix": "50eb5bf4441ea3073ec1ca36dd63bc2b1eb736f514c6de2bc648e98f0fe9cecd", "size_in_bytes": 35}, {"_path": "doc/<PERSON>ti/tab_l.gif", "path_type": "hardlink", "sha256": "d5ecca2b2495aa6b20a860061133a58c00942a408b7828369af4d4b7e08a54d5", "sha256_in_prefix": "d5ecca2b2495aa6b20a860061133a58c00942a408b7828369af4d4b7e08a54d5", "size_in_bytes": 706}, {"_path": "doc/<PERSON>ti/tab_r.gif", "path_type": "hardlink", "sha256": "1876b505a59e66fdc10880a43b6aee462f3ca4dfefcb963b9de9c2d82539a85b", "sha256_in_prefix": "1876b505a59e66fdc10880a43b6aee462f3ca4dfefcb963b9de9c2d82539a85b", "size_in_bytes": 2585}, {"_path": "doc/Cupti/tabs.css", "path_type": "hardlink", "sha256": "6b3373db3135a9f7344a699afc786eae372787ed73885e21f470999ce58bac88", "sha256_in_prefix": "6b3373db3135a9f7344a699afc786eae372787ed73885e21f470999ce58bac88", "size_in_bytes": 1838}, {"_path": "doc/Cupti/unionCUpti__ActivityObjectKindId.html", "path_type": "hardlink", "sha256": "f82870a44720b4dfb3ebd52f246ff8ce27c9c37761d76ae9fe50f8c1d37cdb03", "sha256_in_prefix": "f82870a44720b4dfb3ebd52f246ff8ce27c9c37761d76ae9fe50f8c1d37cdb03", "size_in_bytes": 3862}, {"_path": "doc/Cupti/unionCUpti__MetricValue.html", "path_type": "hardlink", "sha256": "234628e131cdadad0cb1dc662b40c2ce6b214bb665e1bc674cf8a99d9de87e8f", "sha256_in_prefix": "234628e131cdadad0cb1dc662b40c2ce6b214bb665e1bc674cf8a99d9de87e8f", "size_in_bytes": 1974}, {"_path": "doc/common/formatting/bg-head.png", "path_type": "hardlink", "sha256": "d5ea8b20b42df3ca5dccf404fdda5b67b5ee7405e90f1ef288042b412cb4f6f4", "sha256_in_prefix": "d5ea8b20b42df3ca5dccf404fdda5b67b5ee7405e90f1ef288042b412cb4f6f4", "size_in_bytes": 230}, {"_path": "doc/common/formatting/bg-horiz.png", "path_type": "hardlink", "sha256": "7ed990e671720b1ef4c35d5d4c4e050229ab7e612dbe6f61bfe307d7a46dae4b", "sha256_in_prefix": "7ed990e671720b1ef4c35d5d4c4e050229ab7e612dbe6f61bfe307d7a46dae4b", "size_in_bytes": 331}, {"_path": "doc/common/formatting/bg-left.png", "path_type": "hardlink", "sha256": "7e8015b70261db70e396106855b71ebd4cec2570c4f364bf24dc1d236f6f7304", "sha256_in_prefix": "7e8015b70261db70e396106855b71ebd4cec2570c4f364bf24dc1d236f6f7304", "size_in_bytes": 132}, {"_path": "doc/common/formatting/bg-right.png", "path_type": "hardlink", "sha256": "749b5434bd89c09915a98166c2e115263cf4d2641918bfd191407a01a424c47c", "sha256_in_prefix": "749b5434bd89c09915a98166c2e115263cf4d2641918bfd191407a01a424c47c", "size_in_bytes": 131}, {"_path": "doc/common/formatting/bg-sidehead-glow.png", "path_type": "hardlink", "sha256": "8323ca5e47fa251cb5c3a7d946f8db2f39dd77faee66cac9b0522cbac28337c7", "sha256_in_prefix": "8323ca5e47fa251cb5c3a7d946f8db2f39dd77faee66cac9b0522cbac28337c7", "size_in_bytes": 153}, {"_path": "doc/common/formatting/bg-sidehead.png", "path_type": "hardlink", "sha256": "3991888eec29c5e9117e6aa1f37bfbc0ccd8d91db0b9f08944498acf1b25c814", "sha256_in_prefix": "3991888eec29c5e9117e6aa1f37bfbc0ccd8d91db0b9f08944498acf1b25c814", "size_in_bytes": 2827}, {"_path": "doc/common/formatting/bg-vert.png", "path_type": "hardlink", "sha256": "8c63966094e18042f6018f70fd1dd16e5dff159f5d9581175d08c95566b780a5", "sha256_in_prefix": "8c63966094e18042f6018f70fd1dd16e5dff159f5d9581175d08c95566b780a5", "size_in_bytes": 152}, {"_path": "doc/common/formatting/common.min.js", "path_type": "hardlink", "sha256": "4d9455011e8b9cc20b2e1251e83008d430aeaf9bc41e863a6dede0cc70ffca5e", "sha256_in_prefix": "4d9455011e8b9cc20b2e1251e83008d430aeaf9bc41e863a6dede0cc70ffca5e", "size_in_bytes": 10628}, {"_path": "doc/common/formatting/commonltr.css", "path_type": "hardlink", "sha256": "7ce842c6f02e99f86211c774d1952c82346dd93241234f2508e3bfc8fe18843b", "sha256_in_prefix": "7ce842c6f02e99f86211c774d1952c82346dd93241234f2508e3bfc8fe18843b", "size_in_bytes": 6097}, {"_path": "doc/common/formatting/cppapiref.css", "path_type": "hardlink", "sha256": "493c77adf31d93385ea75f6fb397f0559bbe0a2c2c25a6f851c316719f7f3d43", "sha256_in_prefix": "493c77adf31d93385ea75f6fb397f0559bbe0a2c2c25a6f851c316719f7f3d43", "size_in_bytes": 8927}, {"_path": "doc/common/formatting/cuda-toolkit-documentation.png", "path_type": "hardlink", "sha256": "82d8bc92cc62f02fbe75fbf41f6315ed061f3c6e3ee26f49dafe077e6d6f1c8c", "sha256_in_prefix": "82d8bc92cc62f02fbe75fbf41f6315ed061f3c6e3ee26f49dafe077e6d6f1c8c", "size_in_bytes": 9129}, {"_path": "doc/common/formatting/devtools-documentation.png", "path_type": "hardlink", "sha256": "cc7ec5dff58454e4737bc444cd94bad996750a0161ebe178b589a7fc8d7c201f", "sha256_in_prefix": "cc7ec5dff58454e4737bc444cd94bad996750a0161ebe178b589a7fc8d7c201f", "size_in_bytes": 4359}, {"_path": "doc/common/formatting/devzone.png", "path_type": "hardlink", "sha256": "fabd58f2f65a43196f795a2e722f706610b5a03edeefe2967f58ca4de5048830", "sha256_in_prefix": "fabd58f2f65a43196f795a2e722f706610b5a03edeefe2967f58ca4de5048830", "size_in_bytes": 10349}, {"_path": "doc/common/formatting/dita.style.css", "path_type": "hardlink", "sha256": "981642d4c79d4f0dae02dd76e3b1dfddc4e6f53ef9866646e5b12eabe777d854", "sha256_in_prefix": "981642d4c79d4f0dae02dd76e3b1dfddc4e6f53ef9866646e5b12eabe777d854", "size_in_bytes": 34852}, {"_path": "doc/common/formatting/html5shiv-printshiv.min.js", "path_type": "hardlink", "sha256": "d32f2b6f288cbe3c58a98de7ac8c881d737e3b0f1eee8668463ad58d81cb4406", "sha256_in_prefix": "d32f2b6f288cbe3c58a98de7ac8c881d737e3b0f1eee8668463ad58d81cb4406", "size_in_bytes": 3989}, {"_path": "doc/common/formatting/jquery.ba-hashchange.min.js", "path_type": "hardlink", "sha256": "4282e21b6be8696f0175368a9db457df1e29a3559d0c9a653920587556c1f99b", "sha256_in_prefix": "4282e21b6be8696f0175368a9db457df1e29a3559d0c9a653920587556c1f99b", "size_in_bytes": 1604}, {"_path": "doc/common/formatting/jquery.min.js", "path_type": "hardlink", "sha256": "20638e363fcc5152155f24b281303e17da62da62d24ef5dcf863b184d9a25734", "sha256_in_prefix": "20638e363fcc5152155f24b281303e17da62da62d24ef5dcf863b184d9a25734", "size_in_bytes": 92633}, {"_path": "doc/common/formatting/jquery.scrollintoview.min.js", "path_type": "hardlink", "sha256": "4da5b5d1e11984f23faf44f40368b521dc090360d51dd66a6f0b35b52c657309", "sha256_in_prefix": "4da5b5d1e11984f23faf44f40368b521dc090360d51dd66a6f0b35b52c657309", "size_in_bytes": 3501}, {"_path": "doc/common/formatting/magnify-dropdown.png", "path_type": "hardlink", "sha256": "c707d514fb80ece368e68f5a6c70c7ac6b357464be981262cc0911fc76a012df", "sha256_in_prefix": "c707d514fb80ece368e68f5a6c70c7ac6b357464be981262cc0911fc76a012df", "size_in_bytes": 1139}, {"_path": "doc/common/formatting/magnify.png", "path_type": "hardlink", "sha256": "396fcab4919640809426f31de2cba8c226f48f46beab7929b0001db96f519b53", "sha256_in_prefix": "396fcab4919640809426f31de2cba8c226f48f46beab7929b0001db96f519b53", "size_in_bytes": 1100}, {"_path": "doc/common/formatting/nvidia.png", "path_type": "hardlink", "sha256": "7f0030add67b951f07d168464059245cb9dcd01354fb5c6191e6638c1e026cbb", "sha256_in_prefix": "7f0030add67b951f07d168464059245cb9dcd01354fb5c6191e6638c1e026cbb", "size_in_bytes": 4442}, {"_path": "doc/common/formatting/prettify/lang-Splus.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "doc/common/formatting/prettify/lang-aea.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "doc/common/formatting/prettify/lang-agc.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "doc/common/formatting/prettify/lang-apollo.js", "path_type": "hardlink", "sha256": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "sha256_in_prefix": "c00f354bed56dfa7e0727c37a9dc49484b85d570b50b7ecb24ed91f6f69085be", "size_in_bytes": 1612}, {"_path": "doc/common/formatting/prettify/lang-basic.js", "path_type": "hardlink", "sha256": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "sha256_in_prefix": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "size_in_bytes": 1124}, {"_path": "doc/common/formatting/prettify/lang-cbm.js", "path_type": "hardlink", "sha256": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "sha256_in_prefix": "ee03d3d4aeac8c7e4743ec5e3825c9859589b616624a02b9802745a284b1bf46", "size_in_bytes": 1124}, {"_path": "doc/common/formatting/prettify/lang-cl.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-clj.js", "path_type": "hardlink", "sha256": "ed3516d94150cff8e6d138891650d2e5ed888fdf9a2ba21b80066136fa9c4596", "sha256_in_prefix": "ed3516d94150cff8e6d138891650d2e5ed888fdf9a2ba21b80066136fa9c4596", "size_in_bytes": 1484}, {"_path": "doc/common/formatting/prettify/lang-css.js", "path_type": "hardlink", "sha256": "75425acf4f641d7f875abc0c7e05641ce1cf27f5651e784e7bc98dd66db1d94c", "sha256_in_prefix": "75425acf4f641d7f875abc0c7e05641ce1cf27f5651e784e7bc98dd66db1d94c", "size_in_bytes": 1525}, {"_path": "doc/common/formatting/prettify/lang-dart.js", "path_type": "hardlink", "sha256": "49159396e691ef19d5305e63e04a5495fae560a66f1934da248a3556b392e534", "sha256_in_prefix": "49159396e691ef19d5305e63e04a5495fae560a66f1934da248a3556b392e534", "size_in_bytes": 1626}, {"_path": "doc/common/formatting/prettify/lang-el.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-erl.js", "path_type": "hardlink", "sha256": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "sha256_in_prefix": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "size_in_bytes": 1208}, {"_path": "doc/common/formatting/prettify/lang-erlang.js", "path_type": "hardlink", "sha256": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "sha256_in_prefix": "bcfdc2cbf090e3f60a741fe8675af943c3b5c93f63b6d308235bf8a11581540f", "size_in_bytes": 1208}, {"_path": "doc/common/formatting/prettify/lang-fs.js", "path_type": "hardlink", "sha256": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "sha256_in_prefix": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "size_in_bytes": 1711}, {"_path": "doc/common/formatting/prettify/lang-go.js", "path_type": "hardlink", "sha256": "b5adc88320698d36a12fc38e4deba9b6d322842eea43a656c914c03ddb4ddc78", "sha256_in_prefix": "b5adc88320698d36a12fc38e4deba9b6d322842eea43a656c914c03ddb4ddc78", "size_in_bytes": 884}, {"_path": "doc/common/formatting/prettify/lang-hs.js", "path_type": "hardlink", "sha256": "ef49ed0fc4d7ed45b2326efe60522ab042adc19c86b6900a8e69211c8e3b4413", "sha256_in_prefix": "ef49ed0fc4d7ed45b2326efe60522ab042adc19c86b6900a8e69211c8e3b4413", "size_in_bytes": 1217}, {"_path": "doc/common/formatting/prettify/lang-lasso.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "doc/common/formatting/prettify/lang-lassoscript.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "doc/common/formatting/prettify/lang-latex.js", "path_type": "hardlink", "sha256": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "sha256_in_prefix": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "size_in_bytes": 875}, {"_path": "doc/common/formatting/prettify/lang-lgt.js", "path_type": "hardlink", "sha256": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "sha256_in_prefix": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "size_in_bytes": 1428}, {"_path": "doc/common/formatting/prettify/lang-lisp.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-ll.js", "path_type": "hardlink", "sha256": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "sha256_in_prefix": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "size_in_bytes": 971}, {"_path": "doc/common/formatting/prettify/lang-llvm.js", "path_type": "hardlink", "sha256": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "sha256_in_prefix": "6062b885a32525740e86fd47d38b1a32c3a688cea9af11f16cf836658d6bca64", "size_in_bytes": 971}, {"_path": "doc/common/formatting/prettify/lang-logtalk.js", "path_type": "hardlink", "sha256": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "sha256_in_prefix": "dbbedcec7a8872a1637c7e4b92f0aed39c307293ca7824b90e1450854d735eb5", "size_in_bytes": 1428}, {"_path": "doc/common/formatting/prettify/lang-ls.js", "path_type": "hardlink", "sha256": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "sha256_in_prefix": "66d81190fead0d9a927d136f42e4b5afcf972f1e270af23cfa676a53c7406848", "size_in_bytes": 2742}, {"_path": "doc/common/formatting/prettify/lang-lsp.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-lua.js", "path_type": "hardlink", "sha256": "6cdb5f35177e81de9d84995b2bb5b4bddae18ee53f8eeb4e5acf53d166bcf990", "sha256_in_prefix": "6cdb5f35177e81de9d84995b2bb5b4bddae18ee53f8eeb4e5acf53d166bcf990", "size_in_bytes": 1162}, {"_path": "doc/common/formatting/prettify/lang-matlab.js", "path_type": "hardlink", "sha256": "ad8391672b553272b0a639f949f80a4457dad4777c6d9f41fec54d5536afeb9c", "sha256_in_prefix": "ad8391672b553272b0a639f949f80a4457dad4777c6d9f41fec54d5536afeb9c", "size_in_bytes": 21092}, {"_path": "doc/common/formatting/prettify/lang-ml.js", "path_type": "hardlink", "sha256": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "sha256_in_prefix": "e0ba91f5f5283ac855200290d4790b96a11cdfdffeb24a8c66431d4c8c592c65", "size_in_bytes": 1711}, {"_path": "doc/common/formatting/prettify/lang-mumps.js", "path_type": "hardlink", "sha256": "a0a16488395d2be1815e241c9e4521595d2917194a5a50ec98649dc58bef199d", "sha256_in_prefix": "a0a16488395d2be1815e241c9e4521595d2917194a5a50ec98649dc58bef199d", "size_in_bytes": 1500}, {"_path": "doc/common/formatting/prettify/lang-n.js", "path_type": "hardlink", "sha256": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "sha256_in_prefix": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "size_in_bytes": 2069}, {"_path": "doc/common/formatting/prettify/lang-nemerle.js", "path_type": "hardlink", "sha256": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "sha256_in_prefix": "06b1b4d8d4c93b2d536d2d85405a99c48bfc722a22354b6fa1da374df1bc01d7", "size_in_bytes": 2069}, {"_path": "doc/common/formatting/prettify/lang-pascal.js", "path_type": "hardlink", "sha256": "6eed79bfdd3179ef0ea800304c089736d1e45048785d1f771862f73f2ef4b7ec", "sha256_in_prefix": "6eed79bfdd3179ef0ea800304c089736d1e45048785d1f771862f73f2ef4b7ec", "size_in_bytes": 1332}, {"_path": "doc/common/formatting/prettify/lang-proto.js", "path_type": "hardlink", "sha256": "571dc03af497e5d30ccbe4a01b315e01eda9380d8b06139ea2e219745d887e12", "sha256_in_prefix": "571dc03af497e5d30ccbe4a01b315e01eda9380d8b06139ea2e219745d887e12", "size_in_bytes": 891}, {"_path": "doc/common/formatting/prettify/lang-r.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "doc/common/formatting/prettify/lang-rd.js", "path_type": "hardlink", "sha256": "50fdd7c7de82fc381b3147847eeec0980064f714e9ff45411f35cd5b1aeae7b0", "sha256_in_prefix": "50fdd7c7de82fc381b3147847eeec0980064f714e9ff45411f35cd5b1aeae7b0", "size_in_bytes": 862}, {"_path": "doc/common/formatting/prettify/lang-rkt.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-rust.js", "path_type": "hardlink", "sha256": "a0dd431a0fd90648cdd47a61c2d1443059aa99f00c65c826f845a0b4eed0b7be", "sha256_in_prefix": "a0dd431a0fd90648cdd47a61c2d1443059aa99f00c65c826f845a0b4eed0b7be", "size_in_bytes": 2254}, {"_path": "doc/common/formatting/prettify/lang-s.js", "path_type": "hardlink", "sha256": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "sha256_in_prefix": "306494ec6095cf8fda9c7e44ebcf2ed74608095e06d7f948d123c449635f9570", "size_in_bytes": 1369}, {"_path": "doc/common/formatting/prettify/lang-scala.js", "path_type": "hardlink", "sha256": "99cee57320279566c37f295a7e8a6a865936e935aba3c26a7905fb86f8de44df", "sha256_in_prefix": "99cee57320279566c37f295a7e8a6a865936e935aba3c26a7905fb86f8de44df", "size_in_bytes": 1554}, {"_path": "doc/common/formatting/prettify/lang-scm.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-sql.js", "path_type": "hardlink", "sha256": "8699e27b169ce06ccb9de38687b07ce364e6fa270b9a489a117e88ba143f528d", "sha256_in_prefix": "8699e27b169ce06ccb9de38687b07ce364e6fa270b9a489a117e88ba143f528d", "size_in_bytes": 2404}, {"_path": "doc/common/formatting/prettify/lang-ss.js", "path_type": "hardlink", "sha256": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "sha256_in_prefix": "ab2c6bfac4b9a59853bf95344edb9a530af56ccba69e5af84212d0078dbab582", "size_in_bytes": 1400}, {"_path": "doc/common/formatting/prettify/lang-swift.js", "path_type": "hardlink", "sha256": "6cfa8bd06ed6a1789fc4dcc0a14c6b3ce0d1ad9c0e0af620e36475db7c7aaf5e", "sha256_in_prefix": "6cfa8bd06ed6a1789fc4dcc0a14c6b3ce0d1ad9c0e0af620e36475db7c7aaf5e", "size_in_bytes": 2050}, {"_path": "doc/common/formatting/prettify/lang-tcl.js", "path_type": "hardlink", "sha256": "5b55d847b376ba4e08009dde17982c5423ad46f0d86ab5ff64d02cb1123f4d22", "sha256_in_prefix": "5b55d847b376ba4e08009dde17982c5423ad46f0d86ab5ff64d02cb1123f4d22", "size_in_bytes": 1261}, {"_path": "doc/common/formatting/prettify/lang-tex.js", "path_type": "hardlink", "sha256": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "sha256_in_prefix": "75bcea78e9d34891ab7d17b611e0dbdcb65ec412fd1e041c74276c61a9a02da2", "size_in_bytes": 875}, {"_path": "doc/common/formatting/prettify/lang-vb.js", "path_type": "hardlink", "sha256": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "sha256_in_prefix": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "size_in_bytes": 2423}, {"_path": "doc/common/formatting/prettify/lang-vbs.js", "path_type": "hardlink", "sha256": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "sha256_in_prefix": "99333f0a0c15540af9fbbc87c8d3afca5876b77759b1c9b2ad634a53bf4917a6", "size_in_bytes": 2423}, {"_path": "doc/common/formatting/prettify/lang-vhd.js", "path_type": "hardlink", "sha256": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "sha256_in_prefix": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "size_in_bytes": 2054}, {"_path": "doc/common/formatting/prettify/lang-vhdl.js", "path_type": "hardlink", "sha256": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "sha256_in_prefix": "3cc5b831500770fe70871d57f86e44d8b79b22704054198422cba0779b271ca6", "size_in_bytes": 2054}, {"_path": "doc/common/formatting/prettify/lang-wiki.js", "path_type": "hardlink", "sha256": "d1b3fef0de5de519f9a28b68949c7b5fc7a82cf77d6bfa3ff798471541de97b5", "sha256_in_prefix": "d1b3fef0de5de519f9a28b68949c7b5fc7a82cf77d6bfa3ff798471541de97b5", "size_in_bytes": 1157}, {"_path": "doc/common/formatting/prettify/lang-xq.js", "path_type": "hardlink", "sha256": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "sha256_in_prefix": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "size_in_bytes": 23870}, {"_path": "doc/common/formatting/prettify/lang-xquery.js", "path_type": "hardlink", "sha256": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "sha256_in_prefix": "929a1bc60c51777dd037a0b930fc5ef8826691f747d471d1b93ca57a85d6bcd7", "size_in_bytes": 23870}, {"_path": "doc/common/formatting/prettify/lang-yaml.js", "path_type": "hardlink", "sha256": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "sha256_in_prefix": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "size_in_bytes": 1033}, {"_path": "doc/common/formatting/prettify/lang-yml.js", "path_type": "hardlink", "sha256": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "sha256_in_prefix": "292ebdeb7dbeae7d8d2ecaee62c714ac10b4620950ea19f02da214bda68b1c1d", "size_in_bytes": 1033}, {"_path": "doc/common/formatting/prettify/onLoad.png", "path_type": "hardlink", "sha256": "859e0d54ce7aae5de46f9ac67a24313fed8bd042baa8cd3135a1395db5aef5c6", "sha256_in_prefix": "859e0d54ce7aae5de46f9ac67a24313fed8bd042baa8cd3135a1395db5aef5c6", "size_in_bytes": 110}, {"_path": "doc/common/formatting/prettify/prettify.css", "path_type": "hardlink", "sha256": "1d2d628605d9eaa7d4712e414de849855f3511947e23943acf1e7219d12a6e9d", "sha256_in_prefix": "1d2d628605d9eaa7d4712e414de849855f3511947e23943acf1e7219d12a6e9d", "size_in_bytes": 675}, {"_path": "doc/common/formatting/prettify/prettify.js", "path_type": "hardlink", "sha256": "cba675a9c942285a5e742197a33b45b1ccc7b4d333ced035abbd97edc60b2302", "sha256_in_prefix": "cba675a9c942285a5e742197a33b45b1ccc7b4d333ced035abbd97edc60b2302", "size_in_bytes": 15307}, {"_path": "doc/common/formatting/prettify/run_prettify.js", "path_type": "hardlink", "sha256": "ed4b1bfd5321585d97a1f84ece8ae71c969a71791d1ec52d8dff47ede58ea91d", "sha256_in_prefix": "ed4b1bfd5321585d97a1f84ece8ae71c969a71791d1ec52d8dff47ede58ea91d", "size_in_bytes": 18100}, {"_path": "doc/common/formatting/qwcode.highlight.css", "path_type": "hardlink", "sha256": "5c59aff177b504d0b79a73c7b00ccda905c03c445489e06e12de6f7d2ec5bb41", "sha256_in_prefix": "5c59aff177b504d0b79a73c7b00ccda905c03c445489e06e12de6f7d2ec5bb41", "size_in_bytes": 908}, {"_path": "doc/common/formatting/search-clear.png", "path_type": "hardlink", "sha256": "3b9e4abc9fa8aaa24a459d69b63169f4a63a63468493e1d94a9857ae3a5a1c6c", "sha256_in_prefix": "3b9e4abc9fa8aaa24a459d69b63169f4a63a63468493e1d94a9857ae3a5a1c6c", "size_in_bytes": 3638}, {"_path": "doc/common/formatting/site.css", "path_type": "hardlink", "sha256": "458308b26bdc1f943a15431e1393361c99c627957da87a0c5236bb3a4374b73e", "sha256_in_prefix": "458308b26bdc1f943a15431e1393361c99c627957da87a0c5236bb3a4374b73e", "size_in_bytes": 12447}, {"_path": "doc/common/scripts/google-analytics/google-analytics-tracker.js", "path_type": "hardlink", "sha256": "4b2a034fa4648a9624dc4ea51bb110259ff680993243598485f7c331405b108c", "sha256_in_prefix": "4b2a034fa4648a9624dc4ea51bb110259ff680993243598485f7c331405b108c", "size_in_bytes": 117}, {"_path": "doc/common/scripts/google-analytics/google-analytics-write.js", "path_type": "hardlink", "sha256": "c144a7cdd903ddc1941166e39f23045a6bb6b9425999d2f50e5e8950757ab532", "sha256_in_prefix": "c144a7cdd903ddc1941166e39f23045a6bb6b9425999d2f50e5e8950757ab532", "size_in_bytes": 221}, {"_path": "doc/common/scripts/tynt/tynt.js", "path_type": "hardlink", "sha256": "a3ac843bf09609f24c48cec49dbd832e294437a8559a7beaf688e532b43d5e95", "sha256_in_prefix": "a3ac843bf09609f24c48cec49dbd832e294437a8559a7beaf688e532b43d5e95", "size_in_bytes": 316}, {"_path": "doc/index.html", "path_type": "hardlink", "sha256": "b933f52f6e2152f14b827c0aace2480f6eb56283b9cfdec42b18f83775fa9191", "sha256_in_prefix": "b933f52f6e2152f14b827c0aace2480f6eb56283b9cfdec42b18f83775fa9191", "size_in_bytes": 3896}, {"_path": "doc/pdf/Cupti.pdf", "path_type": "hardlink", "sha256": "6e31eb927e5a543966b90e82b230a369eafa92450e0a9b7249b97c46f8126197", "sha256_in_prefix": "6e31eb927e5a543966b90e82b230a369eafa92450e0a9b7249b97c46f8126197", "size_in_bytes": 2028614}, {"_path": "doc/search/check.html", "path_type": "hardlink", "sha256": "cd37d034cab367b7b6fbb2718759c369ae00e855022053d9dffa70331dea0762", "sha256_in_prefix": "cd37d034cab367b7b6fbb2718759c369ae00e855022053d9dffa70331dea0762", "size_in_bytes": 1252}, {"_path": "doc/search/files.js", "path_type": "hardlink", "sha256": "30f5b2cef9216532cb7b86f3e9f35223c92908ae0221425c25ad3a63a1dd2fbe", "sha256_in_prefix": "30f5b2cef9216532cb7b86f3e9f35223c92908ae0221425c25ad3a63a1dd2fbe", "size_in_bytes": 99}, {"_path": "doc/search/htmlFileInfoList.js", "path_type": "hardlink", "sha256": "402c28cd9e05e474e2667ac97cbd7ca9ba46da05d531aac4beda50bb7704de7b", "sha256_in_prefix": "402c28cd9e05e474e2667ac97cbd7ca9ba46da05d531aac4beda50bb7704de7b", "size_in_bytes": 14495}, {"_path": "doc/search/htmlFileList.js", "path_type": "hardlink", "sha256": "2a2973fc1d4a9af88e18af441e1f66db7f9e1c59c7aa67a03628466583e36b21", "sha256_in_prefix": "2a2973fc1d4a9af88e18af441e1f66db7f9e1c59c7aa67a03628466583e36b21", "size_in_bytes": 11310}, {"_path": "doc/search/index-1.js", "path_type": "hardlink", "sha256": "9d3d618010c88c7ec6ed1f802e6a51d6e74aabab074f0e4b7a1013fd454b6041", "sha256_in_prefix": "9d3d618010c88c7ec6ed1f802e6a51d6e74aabab074f0e4b7a1013fd454b6041", "size_in_bytes": 67514}, {"_path": "doc/search/index-2.js", "path_type": "hardlink", "sha256": "ae73d04cd77c543c1f0fa2a2a754d9f68ab3906650db5ef76714f4ae69846e04", "sha256_in_prefix": "ae73d04cd77c543c1f0fa2a2a754d9f68ab3906650db5ef76714f4ae69846e04", "size_in_bytes": 67946}, {"_path": "doc/search/index-3.js", "path_type": "hardlink", "sha256": "e9972b155ea90419994d2f7a73c97f0ebc159687a2ed8b63c5d3ecfabf2a6635", "sha256_in_prefix": "e9972b155ea90419994d2f7a73c97f0ebc159687a2ed8b63c5d3ecfabf2a6635", "size_in_bytes": 58731}, {"_path": "doc/search/nwSearchFnt.min.js", "path_type": "hardlink", "sha256": "aa150eb8635cb8df0e268e67a405cbb84a01033c5c5f4172fe947179899a4a86", "sha256_in_prefix": "aa150eb8635cb8df0e268e67a405cbb84a01033c5c5f4172fe947179899a4a86", "size_in_bytes": 12073}, {"_path": "doc/search/stemmers/en_stemmer.min.js", "path_type": "hardlink", "sha256": "ce0241b6853bb62e6baefaaee419baa458b5ec05181a7107636855411f1fb263", "sha256_in_prefix": "ce0241b6853bb62e6baefaaee419baa458b5ec05181a7107636855411f1fb263", "size_in_bytes": 3531}, {"_path": "include/cuda_stdint.h", "path_type": "hardlink", "sha256": "15cf80a04f152499300fc8a830e19abc1872eec803735692a91282aa07d08751", "sha256_in_prefix": "15cf80a04f152499300fc8a830e19abc1872eec803735692a91282aa07d08751", "size_in_bytes": 4205}, {"_path": "include/cupti.h", "path_type": "hardlink", "sha256": "536e83ba6afddd41cb5f07beb390dd2732e2bb2fdc7cf90da68574c406d09dbc", "sha256_in_prefix": "536e83ba6afddd41cb5f07beb390dd2732e2bb2fdc7cf90da68574c406d09dbc", "size_in_bytes": 4820}, {"_path": "include/cupti_activity.h", "path_type": "hardlink", "sha256": "e35da08b237d5a7cc34547c1c88f587b2dfcc243ce5914407012f32d3ce1168a", "sha256_in_prefix": "e35da08b237d5a7cc34547c1c88f587b2dfcc243ce5914407012f32d3ce1168a", "size_in_bytes": 323470}, {"_path": "include/cupti_callbacks.h", "path_type": "hardlink", "sha256": "8facb1dd6867dcdf5a2ce4ed612780ebac1b7e95f60c97b4cab9e71e3c7e5916", "sha256_in_prefix": "8facb1dd6867dcdf5a2ce4ed612780ebac1b7e95f60c97b4cab9e71e3c7e5916", "size_in_bytes": 27349}, {"_path": "include/cupti_checkpoint.h", "path_type": "hardlink", "sha256": "1c7f8b9a3ce702039bd344c5c3e58208d46526a611fff246e03ea90e36a968e9", "sha256_in_prefix": "1c7f8b9a3ce702039bd344c5c3e58208d46526a611fff246e03ea90e36a968e9", "size_in_bytes": 5391}, {"_path": "include/cupti_driver_cbid.h", "path_type": "hardlink", "sha256": "3cf2687174e04d0d9afe54546d90aad4b9e069afeefd6e37b220c78a773ecf97", "sha256_in_prefix": "3cf2687174e04d0d9afe54546d90aad4b9e069afeefd6e37b220c78a773ecf97", "size_in_bytes": 71071}, {"_path": "include/cupti_events.h", "path_type": "hardlink", "sha256": "6c4a8e40c689bc9065db3b182b57e0ddfb2375c67b49a761ff5f609c49efead5", "sha256_in_prefix": "6c4a8e40c689bc9065db3b182b57e0ddfb2375c67b49a761ff5f609c49efead5", "size_in_bytes": 54010}, {"_path": "include/cupti_metrics.h", "path_type": "hardlink", "sha256": "d6ef3eb2c32728abdd7da530604a611e154d5777279fac7af37b3cdf2ef72ad4", "sha256_in_prefix": "d6ef3eb2c32728abdd7da530604a611e154d5777279fac7af37b3cdf2ef72ad4", "size_in_bytes": 32973}, {"_path": "include/cupti_nvtx_cbid.h", "path_type": "hardlink", "sha256": "bfa701f28a09cb9f91e0134ecdf328400a6c2db284ce16c691de952504af4374", "sha256_in_prefix": "bfa701f28a09cb9f91e0134ecdf328400a6c2db284ce16c691de952504af4374", "size_in_bytes": 6023}, {"_path": "include/cupti_pcsampling.h", "path_type": "hardlink", "sha256": "20df334a5790becb5b1f95e560890a5be01ebf9ddccf73be049506cf838c4f93", "sha256_in_prefix": "20df334a5790becb5b1f95e560890a5be01ebf9ddccf73be049506cf838c4f93", "size_in_bytes": 33345}, {"_path": "include/cupti_pcsampling_util.h", "path_type": "hardlink", "sha256": "d012ca51196ac11643045bc16a95dd9426ee3f8afae9770141baab478fe63105", "sha256_in_prefix": "d012ca51196ac11643045bc16a95dd9426ee3f8afae9770141baab478fe63105", "size_in_bytes": 13479}, {"_path": "include/cupti_profiler_target.h", "path_type": "hardlink", "sha256": "19b02d3bcad1e37f11e6627e322719aea3af7676d4b251b8175fa811ad6b972c", "sha256_in_prefix": "19b02d3bcad1e37f11e6627e322719aea3af7676d4b251b8175fa811ad6b972c", "size_in_bytes": 32185}, {"_path": "include/cupti_result.h", "path_type": "hardlink", "sha256": "161deb6bc8a6eae2e7f39c85d2dbd08e92ea00d6d2dff6362696e7b6069eaa08", "sha256_in_prefix": "161deb6bc8a6eae2e7f39c85d2dbd08e92ea00d6d2dff6362696e7b6069eaa08", "size_in_bytes": 12354}, {"_path": "include/cupti_runtime_cbid.h", "path_type": "hardlink", "sha256": "53a94d342b18aae4bf653eeacf1e8c1506929cbbad250408b6cac051f92a0f69", "sha256_in_prefix": "53a94d342b18aae4bf653eeacf1e8c1506929cbbad250408b6cac051f92a0f69", "size_in_bytes": 44640}, {"_path": "include/cupti_target.h", "path_type": "hardlink", "sha256": "9f1c28a369cf043de7dc441880292685ceb6d3cc60489b2e151954f517523229", "sha256_in_prefix": "9f1c28a369cf043de7dc441880292685ceb6d3cc60489b2e151954f517523229", "size_in_bytes": 1306}, {"_path": "include/cupti_version.h", "path_type": "hardlink", "sha256": "8787bc05fd4eb33767890977bc256054d4b923cda547e18b361d31df49c12927", "sha256_in_prefix": "8787bc05fd4eb33767890977bc256054d4b923cda547e18b361d31df49c12927", "size_in_bytes": 4475}, {"_path": "include/generated_cudaD3D10_meta.h", "path_type": "hardlink", "sha256": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "sha256_in_prefix": "3d21cf541fc64a3e8d111477fc94dc9ebbfed929aea5609eb69602f32745db8a", "size_in_bytes": 4439}, {"_path": "include/generated_cudaD3D11_meta.h", "path_type": "hardlink", "sha256": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "sha256_in_prefix": "fa87ddc95dab6f95e25d319aea7c64020f673e42e6b1ab963a3710bbc461d16d", "size_in_bytes": 1823}, {"_path": "include/generated_cudaD3D9_meta.h", "path_type": "hardlink", "sha256": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "sha256_in_prefix": "89a25b6eee7cbc560ecaee18b97d9007122d1f4f62a096dae0296f30655fddd0", "size_in_bytes": 5379}, {"_path": "include/generated_cudaGL_meta.h", "path_type": "hardlink", "sha256": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "sha256_in_prefix": "67e4b157a27f114e8cdd3c6cffac917ac38672a6ce0931f47c640eb99b371117", "size_in_bytes": 3344}, {"_path": "include/generated_cuda_d3d10_interop_meta.h", "path_type": "hardlink", "sha256": "76c86ea44b708215f9290a048fc4a0504e8b7c9af6b14c0deff203622548a13f", "sha256_in_prefix": "76c86ea44b708215f9290a048fc4a0504e8b7c9af6b14c0deff203622548a13f", "size_in_bytes": 3364}, {"_path": "include/generated_cuda_d3d11_interop_meta.h", "path_type": "hardlink", "sha256": "9d29f4ec811d51a1625b1d7cb5643d160853e64fd77c0cbd6af988329eec1e6e", "sha256_in_prefix": "9d29f4ec811d51a1625b1d7cb5643d160853e64fd77c0cbd6af988329eec1e6e", "size_in_bytes": 1500}, {"_path": "include/generated_cuda_d3d9_interop_meta.h", "path_type": "hardlink", "sha256": "928816f80222c48cabc8976f3c998288778cd55d1f465cd4d32373a7f7084c41", "sha256_in_prefix": "928816f80222c48cabc8976f3c998288778cd55d1f465cd4d32373a7f7084c41", "size_in_bytes": 4178}, {"_path": "include/generated_cuda_gl_interop_meta.h", "path_type": "hardlink", "sha256": "a312356f67f7bfb4fca94967306235e4488adb5e05f2bd15edc9c1588a003d8b", "sha256_in_prefix": "a312356f67f7bfb4fca94967306235e4488adb5e05f2bd15edc9c1588a003d8b", "size_in_bytes": 2492}, {"_path": "include/generated_cuda_meta.h", "path_type": "hardlink", "sha256": "1d6fb286031aaaa9d6d91237f8bbcecb85dcc965dfe7df701c22804977d15c25", "sha256_in_prefix": "1d6fb286031aaaa9d6d91237f8bbcecb85dcc965dfe7df701c22804977d15c25", "size_in_bytes": 90445}, {"_path": "include/generated_cuda_runtime_api_meta.h", "path_type": "hardlink", "sha256": "b4eb67577dfc2e9f3fab772415182cef2f255afb0a5fac46860904e46e17bf37", "sha256_in_prefix": "b4eb67577dfc2e9f3fab772415182cef2f255afb0a5fac46860904e46e17bf37", "size_in_bytes": 66458}, {"_path": "include/generated_cudart_removed_meta.h", "path_type": "hardlink", "sha256": "59ae12d892a86e92e22b7ccd2994f71552eeb8a9f546f8cf2710b25e524bf51c", "sha256_in_prefix": "59ae12d892a86e92e22b7ccd2994f71552eeb8a9f546f8cf2710b25e524bf51c", "size_in_bytes": 5334}, {"_path": "include/generated_nvtx_meta.h", "path_type": "hardlink", "sha256": "5d657bc5eb8c75c36402ac0b0f93b5e26a54e42b440ed4a04531758d2b95285c", "sha256_in_prefix": "5d657bc5eb8c75c36402ac0b0f93b5e26a54e42b440ed4a04531758d2b95285c", "size_in_bytes": 7760}, {"_path": "include/nvperf_common.h", "path_type": "hardlink", "sha256": "4a420fbd9c699b77db4a6e760f8dddaf350161fef63930094eb81a14df831c65", "sha256_in_prefix": "4a420fbd9c699b77db4a6e760f8dddaf350161fef63930094eb81a14df831c65", "size_in_bytes": 10695}, {"_path": "include/nvperf_cuda_host.h", "path_type": "hardlink", "sha256": "312d7e2ebfa9d061668f8be8094f83f7b8f402c0b3ef143599dfb8fb13c4e153", "sha256_in_prefix": "312d7e2ebfa9d061668f8be8094f83f7b8f402c0b3ef143599dfb8fb13c4e153", "size_in_bytes": 8496}, {"_path": "include/nvperf_host.h", "path_type": "hardlink", "sha256": "7dad2a64f877cf950997ec6f853a514e949b2a3cc7200044c4cd19ad3fa3b3f7", "sha256_in_prefix": "7dad2a64f877cf950997ec6f853a514e949b2a3cc7200044c4cd19ad3fa3b3f7", "size_in_bytes": 67817}, {"_path": "include/nvperf_target.h", "path_type": "hardlink", "sha256": "4d9de238d9796dcdedc200003a22c3245f7a96d0160a70f91597eb371884bfee", "sha256_in_prefix": "4d9de238d9796dcdedc200003a22c3245f7a96d0160a70f91597eb371884bfee", "size_in_bytes": 22048}, {"_path": "lib/checkpoint.dll", "path_type": "hardlink", "sha256": "3fe911b9506cf1976e1deeb40f093ac0b6a4bc7b85d23211f3633cc9e2b4798d", "sha256_in_prefix": "3fe911b9506cf1976e1deeb40f093ac0b6a4bc7b85d23211f3633cc9e2b4798d", "size_in_bytes": 379992}, {"_path": "lib/checkpoint.lib", "path_type": "hardlink", "sha256": "8307c5d5ce39f17522310bc5fbf7d101efb67c072a9e1d702152e52ca6407b4b", "sha256_in_prefix": "8307c5d5ce39f17522310bc5fbf7d101efb67c072a9e1d702152e52ca6407b4b", "size_in_bytes": 2256}, {"_path": "lib/cupti.lib", "path_type": "hardlink", "sha256": "682603a2b6aeb87a514225471026705d0b48879dd875da043c788a1790c04b60", "sha256_in_prefix": "682603a2b6aeb87a514225471026705d0b48879dd875da043c788a1790c04b60", "size_in_bytes": 37166}, {"_path": "lib/cupti64_2023.1.1.dll", "path_type": "hardlink", "sha256": "26fcbaa723970aaa75a0a48b1f4707af405c2558015ea29009ee96bd54661e2f", "sha256_in_prefix": "26fcbaa723970aaa75a0a48b1f4707af405c2558015ea29009ee96bd54661e2f", "size_in_bytes": 4327424}, {"_path": "lib/nvperf_host.dll", "path_type": "hardlink", "sha256": "35313332afcd5568ae87fceec758e21de9bd49526051614d3d1d1643307c2e9f", "sha256_in_prefix": "35313332afcd5568ae87fceec758e21de9bd49526051614d3d1d1643307c2e9f", "size_in_bytes": 22792776}, {"_path": "lib/nvperf_host.lib", "path_type": "hardlink", "sha256": "1f0b6283e791990802f405c68edba34a27e627bd2b29c7d31009452e6d5433a5", "sha256_in_prefix": "1f0b6283e791990802f405c68edba34a27e627bd2b29c7d31009452e6d5433a5", "size_in_bytes": 131054}, {"_path": "lib/nvperf_target.dll", "path_type": "hardlink", "sha256": "a54c013caf6a05a12d78f00be175a42bb110052986dad1b370c8b1e2a33751b7", "sha256_in_prefix": "a54c013caf6a05a12d78f00be175a42bb110052986dad1b370c8b1e2a33751b7", "size_in_bytes": 1996888}, {"_path": "lib/nvperf_target.lib", "path_type": "hardlink", "sha256": "6a2ca15147832d6eef4e27a90e325d8975ce35dd7cbb46046d52ec390fde7cc0", "sha256_in_prefix": "6a2ca15147832d6eef4e27a90e325d8975ce35dd7cbb46046d52ec390fde7cc0", "size_in_bytes": 101514}, {"_path": "lib/pcsamplingutil.dll", "path_type": "hardlink", "sha256": "7e98790aa6816db7b8a28765a1b3958337a26293ce33de16961111e08d2ae1b1", "sha256_in_prefix": "7e98790aa6816db7b8a28765a1b3958337a26293ce33de16961111e08d2ae1b1", "size_in_bytes": 68184}, {"_path": "lib/pcsamplingutil.lib", "path_type": "hardlink", "sha256": "01d56482cffb1fa296176ea35cf392df74825c00f44262da7a067d266c8fa6ac", "sha256_in_prefix": "01d56482cffb1fa296176ea35cf392df74825c00f44262da7a067d266c8fa6ac", "size_in_bytes": 2914}, {"_path": "samples/activity_trace_async/Makefile", "path_type": "hardlink", "sha256": "12c440088f8ac3d8c1dac8d5366307985f0bc7daf3451e2b60b1efa6a3cfccf2", "sha256_in_prefix": "12c440088f8ac3d8c1dac8d5366307985f0bc7daf3451e2b60b1efa6a3cfccf2", "size_in_bytes": 3029}, {"_path": "samples/activity_trace_async/activity_trace_async.cu", "path_type": "hardlink", "sha256": "7eb91bf42580c39ed23639076d11c53ae8752cafc5ca6aced01a4c02cc92955b", "sha256_in_prefix": "7eb91bf42580c39ed23639076d11c53ae8752cafc5ca6aced01a4c02cc92955b", "size_in_bytes": 5178}, {"_path": "samples/autorange_profiling/Makefile", "path_type": "hardlink", "sha256": "1bfc7b51062af3f74e83c9074616f9d6b845047842d1ab90f7ee8f53c9f6f238", "sha256_in_prefix": "1bfc7b51062af3f74e83c9074616f9d6b845047842d1ab90f7ee8f53c9f6f238", "size_in_bytes": 3776}, {"_path": "samples/autorange_profiling/auto_range_profiling.cu", "path_type": "hardlink", "sha256": "387baef8d8e6d2b0e9dc57ce526029b181d75262d876a79a0f18dfc18dabfebd", "sha256_in_prefix": "387baef8d8e6d2b0e9dc57ce526029b181d75262d876a79a0f18dfc18dabfebd", "size_in_bytes": 17871}, {"_path": "samples/callback_event/Makefile", "path_type": "hardlink", "sha256": "b10f8aa791c12c41d9a3664f1d21abc4e6229d7186313a584c4c22ede528bbbc", "sha256_in_prefix": "b10f8aa791c12c41d9a3664f1d21abc4e6229d7186313a584c4c22ede528bbbc", "size_in_bytes": 2990}, {"_path": "samples/callback_event/callback_event.cu", "path_type": "hardlink", "sha256": "8b685caca887e7010cf08588cd7c816b0e9f79b3cdf4e31fb07146d04e64b960", "sha256_in_prefix": "8b685caca887e7010cf08588cd7c816b0e9f79b3cdf4e31fb07146d04e64b960", "size_in_bytes": 9091}, {"_path": "samples/callback_metric/Makefile", "path_type": "hardlink", "sha256": "ef6d89c245317d980662d60d1ca5113b5aff9e31ddac7f50469eef5ecd1f190f", "sha256_in_prefix": "ef6d89c245317d980662d60d1ca5113b5aff9e31ddac7f50469eef5ecd1f190f", "size_in_bytes": 2998}, {"_path": "samples/callback_metric/callback_metric.cu", "path_type": "hardlink", "sha256": "39ad3498bb0fa4245ba07969092d1021072aa9d34b8a810e53c565af25a0829b", "sha256_in_prefix": "39ad3498bb0fa4245ba07969092d1021072aa9d34b8a810e53c565af25a0829b", "size_in_bytes": 16500}, {"_path": "samples/callback_profiling/Makefile", "path_type": "hardlink", "sha256": "5839cefbdaf5a656c2a217c7b3e49562d57a6d5c8c99e80afa54e9c240b8fdc0", "sha256_in_prefix": "5839cefbdaf5a656c2a217c7b3e49562d57a6d5c8c99e80afa54e9c240b8fdc0", "size_in_bytes": 3752}, {"_path": "samples/callback_profiling/callback_profiling.cu", "path_type": "hardlink", "sha256": "698a79a0bed533cb8641c160a112d94bfc2404ee1e60760354d98f6f193bff74", "sha256_in_prefix": "698a79a0bed533cb8641c160a112d94bfc2404ee1e60760354d98f6f193bff74", "size_in_bytes": 20445}, {"_path": "samples/callback_timestamp/Makefile", "path_type": "hardlink", "sha256": "7b6da305974a60829ff59d2344cfde5944f5849afebd784b8c207d9a2f1a11c6", "sha256_in_prefix": "7b6da305974a60829ff59d2344cfde5944f5849afebd784b8c207d9a2f1a11c6", "size_in_bytes": 3041}, {"_path": "samples/callback_timestamp/callback_timestamp.cu", "path_type": "hardlink", "sha256": "1e14f58e9af760b12786cb1521837a21251ffc33cb1c98b06d5edf1bad9e496c", "sha256_in_prefix": "1e14f58e9af760b12786cb1521837a21251ffc33cb1c98b06d5edf1bad9e496c", "size_in_bytes": 10119}, {"_path": "samples/checkpoint_kernels/Makefile", "path_type": "hardlink", "sha256": "2801743712133f99231c2eba16beb347868c7880c2a4a15df78a51b8ddbe8536", "sha256_in_prefix": "2801743712133f99231c2eba16beb347868c7880c2a4a15df78a51b8ddbe8536", "size_in_bytes": 3206}, {"_path": "samples/checkpoint_kernels/checkpoint_kernels.cu", "path_type": "hardlink", "sha256": "f29c4537031b35b05f43b64888f2f8bfdfa2257ef712d4f191b1c288e8cd3ba3", "sha256_in_prefix": "f29c4537031b35b05f43b64888f2f8bfdfa2257ef712d4f191b1c288e8cd3ba3", "size_in_bytes": 5114}, {"_path": "samples/common/helper_cupti.h", "path_type": "hardlink", "sha256": "27cb44df3934bba0f1ab6af4a6718ac654e55fbdbeb142a63bf7a04823819450", "sha256_in_prefix": "27cb44df3934bba0f1ab6af4a6718ac654e55fbdbeb142a63bf7a04823819450", "size_in_bytes": 7898}, {"_path": "samples/common/helper_cupti_activity.h", "path_type": "hardlink", "sha256": "a0fc50c7721749f5101acd66022e15f0f684634f1f69ce80a330ef25b826f5c8", "sha256_in_prefix": "a0fc50c7721749f5101acd66022e15f0f684634f1f69ce80a330ef25b826f5c8", "size_in_bytes": 97115}, {"_path": "samples/concurrent_profiling/Makefile", "path_type": "hardlink", "sha256": "f5028b8bc6cbd30fffb42dfc07d1420fa7107e9b2e39ee8670e6c336f18e0ed8", "sha256_in_prefix": "f5028b8bc6cbd30fffb42dfc07d1420fa7107e9b2e39ee8670e6c336f18e0ed8", "size_in_bytes": 3769}, {"_path": "samples/concurrent_profiling/concurrent_profiling.cu", "path_type": "hardlink", "sha256": "4d9414c98de80ba5d6eeb5ccef9e0018e2642bce76848163d592c996e3edd4bd", "sha256_in_prefix": "4d9414c98de80ba5d6eeb5ccef9e0018e2642bce76848163d592c996e3edd4bd", "size_in_bytes": 33130}, {"_path": "samples/cuda_graphs_trace/Makefile", "path_type": "hardlink", "sha256": "0d200da9d9b1ee63ab6e13b0c590a5498e5442376a5e923521f7a25d1cf94d39", "sha256_in_prefix": "0d200da9d9b1ee63ab6e13b0c590a5498e5442376a5e923521f7a25d1cf94d39", "size_in_bytes": 3010}, {"_path": "samples/cuda_graphs_trace/cuda_graphs_trace.cu", "path_type": "hardlink", "sha256": "44effc9c72067a945acc1e847a921de312955e82f1476ce906881207513124e8", "sha256_in_prefix": "44effc9c72067a945acc1e847a921de312955e82f1476ce906881207513124e8", "size_in_bytes": 10549}, {"_path": "samples/cuda_memory_trace/Makefile", "path_type": "hardlink", "sha256": "d6aed892b6d03008c7d001e471acbf6fa67017559ce4f8e2edcdbd111668aef7", "sha256_in_prefix": "d6aed892b6d03008c7d001e471acbf6fa67017559ce4f8e2edcdbd111668aef7", "size_in_bytes": 2983}, {"_path": "samples/cuda_memory_trace/memory_trace.cu", "path_type": "hardlink", "sha256": "43a96dd38b49d0fef1a69fb3fc685e0d825bbe3914f16b070032fb1fdac56239", "sha256_in_prefix": "43a96dd38b49d0fef1a69fb3fc685e0d825bbe3914f16b070032fb1fdac56239", "size_in_bytes": 5349}, {"_path": "samples/cupti_correlation/Makefile", "path_type": "hardlink", "sha256": "e2a99c7ae4cc9ddcc5ede5e880c05a596751ac7ce6ef7a31e460d20bc3519db6", "sha256_in_prefix": "e2a99c7ae4cc9ddcc5ede5e880c05a596751ac7ce6ef7a31e460d20bc3519db6", "size_in_bytes": 3009}, {"_path": "samples/cupti_correlation/cupti_correlation.cu", "path_type": "hardlink", "sha256": "92d29a4e09aa2716a6c42d9d710b8c14794674e9719f64b42847c4ff39e895d7", "sha256_in_prefix": "92d29a4e09aa2716a6c42d9d710b8c14794674e9719f64b42847c4ff39e895d7", "size_in_bytes": 10975}, {"_path": "samples/cupti_external_correlation/Makefile", "path_type": "hardlink", "sha256": "20de617504e87c3f95531030964366b3193e85a85ea89d0724e6c3c3b104d92d", "sha256_in_prefix": "20de617504e87c3f95531030964366b3193e85a85ea89d0724e6c3c3b104d92d", "size_in_bytes": 3064}, {"_path": "samples/cupti_external_correlation/cupti_external_correlation.cu", "path_type": "hardlink", "sha256": "c25f3f900f6235f25203c79b86026ba76f412d572402867b57e9d35d588f6d8a", "sha256_in_prefix": "c25f3f900f6235f25203c79b86026ba76f412d572402867b57e9d35d588f6d8a", "size_in_bytes": 8630}, {"_path": "samples/cupti_metric_properties/Makefile", "path_type": "hardlink", "sha256": "ced3f9cc14bc22e933d2ccdef032acf15176fbb912a44ced238f354f4346e692", "sha256_in_prefix": "ced3f9cc14bc22e933d2ccdef032acf15176fbb912a44ced238f354f4346e692", "size_in_bytes": 3813}, {"_path": "samples/cupti_metric_properties/cupti_metric_properties.cpp", "path_type": "hardlink", "sha256": "987c859ece78fb5088297663e03475872d928b2eab2c8c5441d621d8e75d5365", "sha256_in_prefix": "987c859ece78fb5088297663e03475872d928b2eab2c8c5441d621d8e75d5365", "size_in_bytes": 19589}, {"_path": "samples/cupti_nvtx/Makefile", "path_type": "hardlink", "sha256": "3ec2ef14424bfb8a960b03782ed6a305f1d797017197f89f4d77968f1efcb8ad", "sha256_in_prefix": "3ec2ef14424bfb8a960b03782ed6a305f1d797017197f89f4d77968f1efcb8ad", "size_in_bytes": 3146}, {"_path": "samples/cupti_nvtx/cupti_nvtx.cu", "path_type": "hardlink", "sha256": "0030d60f0c61f1df8348c747d4c268891091e95fd095469988d8e6bdc4a53434", "sha256_in_prefix": "0030d60f0c61f1df8348c747d4c268891091e95fd095469988d8e6bdc4a53434", "size_in_bytes": 8798}, {"_path": "samples/cupti_query/Makefile", "path_type": "hardlink", "sha256": "51bb03968b14b48bef496a5f925d5bb38c4599b0caa0c8d628a9a5eb06b0b07b", "sha256_in_prefix": "51bb03968b14b48bef496a5f925d5bb38c4599b0caa0c8d628a9a5eb06b0b07b", "size_in_bytes": 2742}, {"_path": "samples/cupti_query/cupti_query.cpp", "path_type": "hardlink", "sha256": "4e87fbcf950823390de9cf0594a616d407c8e4c1a3cec94d9becbaf012ef9c5c", "sha256_in_prefix": "4e87fbcf950823390de9cf0594a616d407c8e4c1a3cec94d9becbaf012ef9c5c", "size_in_bytes": 16218}, {"_path": "samples/cupti_trace_injection/Makefile", "path_type": "hardlink", "sha256": "3850195b8e1800cbcf1ef3acca2444239b7df458078e8bdea07a8304c17d68e4", "sha256_in_prefix": "3850195b8e1800cbcf1ef3acca2444239b7df458078e8bdea07a8304c17d68e4", "size_in_bytes": 2044}, {"_path": "samples/cupti_trace_injection/README.txt", "path_type": "hardlink", "sha256": "3ce08542b8a24fc2406ef8ddb504c12a1bfa417792b99db39582b42ca39e8ab6", "sha256_in_prefix": "3ce08542b8a24fc2406ef8ddb504c12a1bfa417792b99db39582b42ca39e8ab6", "size_in_bytes": 1416}, {"_path": "samples/cupti_trace_injection/cupti_trace_injection.cpp", "path_type": "hardlink", "sha256": "b5b91c1c628ccc9a15707b6b6f2dfec680e4a53016cdd0efaf58ae39b8b2a635", "sha256_in_prefix": "b5b91c1c628ccc9a15707b6b6f2dfec680e4a53016cdd0efaf58ae39b8b2a635", "size_in_bytes": 12401}, {"_path": "samples/event_multi_gpu/Makefile", "path_type": "hardlink", "sha256": "b98195d417a2c99a8ca5ad73e4ef3996a38efdc3877ff8a9f2539cfd4f67d094", "sha256_in_prefix": "b98195d417a2c99a8ca5ad73e4ef3996a38efdc3877ff8a9f2539cfd4f67d094", "size_in_bytes": 2775}, {"_path": "samples/event_multi_gpu/event_multi_gpu.cu", "path_type": "hardlink", "sha256": "8570dcf01ef5d1aafb22bc9ad1e7fd229d4b2683ecc9ac88531e6db2850c337d", "sha256_in_prefix": "8570dcf01ef5d1aafb22bc9ad1e7fd229d4b2683ecc9ac88531e6db2850c337d", "size_in_bytes": 5299}, {"_path": "samples/event_sampling/Makefile", "path_type": "hardlink", "sha256": "f1926d831b3ce10ce029015d8810072794d39a8cc1cf2508d06c04e40c1ff1aa", "sha256_in_prefix": "f1926d831b3ce10ce029015d8810072794d39a8cc1cf2508d06c04e40c1ff1aa", "size_in_bytes": 2989}, {"_path": "samples/event_sampling/event_sampling.cu", "path_type": "hardlink", "sha256": "4b472a2f775a467f18aba86ec295a06458b98cec08c4d74855e7a29cba0a84f3", "sha256_in_prefix": "4b472a2f775a467f18aba86ec295a06458b98cec08c4d74855e7a29cba0a84f3", "size_in_bytes": 9439}, {"_path": "samples/extensions/include/c_util/FileOp.h", "path_type": "hardlink", "sha256": "d52c91b3d9f7eae23cbe966a63ddcb4635bc762b8fbfc0b66686e3a995be7cd1", "sha256_in_prefix": "d52c91b3d9f7eae23cbe966a63ddcb4635bc762b8fbfc0b66686e3a995be7cd1", "size_in_bytes": 1217}, {"_path": "samples/extensions/include/c_util/ScopeExit.h", "path_type": "hardlink", "sha256": "fa8225ef9c179a5748c2eacdb39a85a149c634aa1fe69dcf9ba08161f2d77975", "sha256_in_prefix": "fa8225ef9c179a5748c2eacdb39a85a149c634aa1fe69dcf9ba08161f2d77975", "size_in_bytes": 504}, {"_path": "samples/extensions/include/profilerhost_util/Eval.h", "path_type": "hardlink", "sha256": "3021012d2d10f82a92eb3e9a2c896b6f25771efc89d944a7ed31cf1fb9dff075", "sha256_in_prefix": "3021012d2d10f82a92eb3e9a2c896b6f25771efc89d944a7ed31cf1fb9dff075", "size_in_bytes": 2142}, {"_path": "samples/extensions/include/profilerhost_util/List.h", "path_type": "hardlink", "sha256": "f60e350724b25fb30c1ab96753b4661186092c2ff2801a3549e9aa1451f56f96", "sha256_in_prefix": "f60e350724b25fb30c1ab96753b4661186092c2ff2801a3549e9aa1451f56f96", "size_in_bytes": 1685}, {"_path": "samples/extensions/include/profilerhost_util/Metric.h", "path_type": "hardlink", "sha256": "1932044dfd049882a1579c90445abda7e8fd391e34e720ceb38505722c56b07c", "sha256_in_prefix": "1932044dfd049882a1579c90445abda7e8fd391e34e720ceb38505722c56b07c", "size_in_bytes": 1862}, {"_path": "samples/extensions/include/profilerhost_util/Parser.h", "path_type": "hardlink", "sha256": "475388560277a08dedb6b9cf70b5a91e69e49cbff3a560cbfe9fc925538b0923", "sha256_in_prefix": "475388560277a08dedb6b9cf70b5a91e69e49cbff3a560cbfe9fc925538b0923", "size_in_bytes": 1957}, {"_path": "samples/extensions/include/profilerhost_util/Utils.h", "path_type": "hardlink", "sha256": "fc134329860310fb70c6eefc84fcca83553e1e7384181b37db45b0954ca8ea8f", "sha256_in_prefix": "fc134329860310fb70c6eefc84fcca83553e1e7384181b37db45b0954ca8ea8f", "size_in_bytes": 4604}, {"_path": "samples/extensions/src/profilerhost_util/Eval.cpp", "path_type": "hardlink", "sha256": "15b469bbc19fb55a8da3db66749bc6d5803df73cc5b2608c953a91e7c6924ae8", "sha256_in_prefix": "15b469bbc19fb55a8da3db66749bc6d5803df73cc5b2608c953a91e7c6924ae8", "size_in_bytes": 15510}, {"_path": "samples/extensions/src/profilerhost_util/List.cpp", "path_type": "hardlink", "sha256": "fd81c7cbc4b5f25089b5678c6dbc884a8abe70c9be80074d349de65f9dd68ab8", "sha256_in_prefix": "fd81c7cbc4b5f25089b5678c6dbc884a8abe70c9be80074d349de65f9dd68ab8", "size_in_bytes": 9847}, {"_path": "samples/extensions/src/profilerhost_util/Makefile", "path_type": "hardlink", "sha256": "b0f2557e8e312d5b8d892223ac1550fc4bbb566d3c3171864e6e49e570e61f98", "sha256_in_prefix": "b0f2557e8e312d5b8d892223ac1550fc4bbb566d3c3171864e6e49e570e61f98", "size_in_bytes": 3232}, {"_path": "samples/extensions/src/profilerhost_util/Metric.cpp", "path_type": "hardlink", "sha256": "ae4ddf8ae11d5f9ceb3de2603721ec9e1d1e5aa7501a27238efce65006ada325", "sha256_in_prefix": "ae4ddf8ae11d5f9ceb3de2603721ec9e1d1e5aa7501a27238efce65006ada325", "size_in_bytes": 12413}, {"_path": "samples/nested_range_profiling/Makefile", "path_type": "hardlink", "sha256": "ca3b4669a6fe02d1a09c5ef39d32861dab3577875ca781c1f53972efecb079ce", "sha256_in_prefix": "ca3b4669a6fe02d1a09c5ef39d32861dab3577875ca781c1f53972efecb079ce", "size_in_bytes": 3793}, {"_path": "samples/nested_range_profiling/nested_range_profiling.cu", "path_type": "hardlink", "sha256": "367c76c1c79cad0defb8cb3c0a56c961946af5f6bea30d5c044cb17806880b02", "sha256_in_prefix": "367c76c1c79cad0defb8cb3c0a56c961946af5f6bea30d5c044cb17806880b02", "size_in_bytes": 19970}, {"_path": "samples/nvlink_bandwidth/Makefile", "path_type": "hardlink", "sha256": "4d81902e297dd7303fa0a22cf6ef948661cb79a59575f1ce586ecd2f541ef31b", "sha256_in_prefix": "4d81902e297dd7303fa0a22cf6ef948661cb79a59575f1ce586ecd2f541ef31b", "size_in_bytes": 3003}, {"_path": "samples/nvlink_bandwidth/nvlink_bandwidth.cu", "path_type": "hardlink", "sha256": "1adec9669c2cbd2cb2923c590fa958889108fcd98bdf4b8345bdfdc39c5c76d3", "sha256_in_prefix": "1adec9669c2cbd2cb2923c590fa958889108fcd98bdf4b8345bdfdc39c5c76d3", "size_in_bytes": 17318}, {"_path": "samples/pc_sampling/Makefile", "path_type": "hardlink", "sha256": "d0b314aa518c1f23ccff4d15fcb30bfd829913159f14eb1397ade652eb8b4738", "sha256_in_prefix": "d0b314aa518c1f23ccff4d15fcb30bfd829913159f14eb1397ade652eb8b4738", "size_in_bytes": 2187}, {"_path": "samples/pc_sampling/pc_sampling.cu", "path_type": "hardlink", "sha256": "b33c5f30d7452667a8e41f59ebe21cd2905e12e731597f5378ef9aaaa0bb6c7a", "sha256_in_prefix": "b33c5f30d7452667a8e41f59ebe21cd2905e12e731597f5378ef9aaaa0bb6c7a", "size_in_bytes": 4349}, {"_path": "samples/pc_sampling_continuous/Makefile", "path_type": "hardlink", "sha256": "154278c3d1e60939ccd806137eca646d824e1f88cff9d1b1a8663aba7fad76e9", "sha256_in_prefix": "154278c3d1e60939ccd806137eca646d824e1f88cff9d1b1a8663aba7fad76e9", "size_in_bytes": 2033}, {"_path": "samples/pc_sampling_continuous/README.txt", "path_type": "hardlink", "sha256": "6a8a3422ab3929ef6eec59b5d99776b3dc11b63875fab4974535823d059e5d43", "sha256_in_prefix": "6a8a3422ab3929ef6eec59b5d99776b3dc11b63875fab4974535823d059e5d43", "size_in_bytes": 1566}, {"_path": "samples/pc_sampling_continuous/libpc_sampling_continuous.pl", "path_type": "hardlink", "sha256": "2440454a9b9128caba745046503c5ddc04d37758aa23dffd1c159ccf78374806", "sha256_in_prefix": "2440454a9b9128caba745046503c5ddc04d37758aa23dffd1c159ccf78374806", "size_in_bytes": 9035}, {"_path": "samples/pc_sampling_continuous/pc_sampling_continuous.cpp", "path_type": "hardlink", "sha256": "8c8f3476a802eba0fb6d7da551e2a3a94be3af331685b74cd1ece7e4ce78f596", "sha256_in_prefix": "8c8f3476a802eba0fb6d7da551e2a3a94be3af331685b74cd1ece7e4ce78f596", "size_in_bytes": 45621}, {"_path": "samples/pc_sampling_start_stop/Makefile", "path_type": "hardlink", "sha256": "15ed56f403ad6b45e84772355f0ba9226946e86d5cc88d52ac00675052e890ec", "sha256_in_prefix": "15ed56f403ad6b45e84772355f0ba9226946e86d5cc88d52ac00675052e890ec", "size_in_bytes": 2249}, {"_path": "samples/pc_sampling_start_stop/pc_sampling_start_stop.cu", "path_type": "hardlink", "sha256": "0fffde6c5c006cc8398f74da97bcc77f97719910fb98eadd4a181572273bb475", "sha256_in_prefix": "0fffde6c5c006cc8398f74da97bcc77f97719910fb98eadd4a181572273bb475", "size_in_bytes": 17702}, {"_path": "samples/pc_sampling_utility/Makefile", "path_type": "hardlink", "sha256": "edfc989e40e52b6fad65d962b0dc7e7c490f9527f727633cbb81685a478eca80", "sha256_in_prefix": "edfc989e40e52b6fad65d962b0dc7e7c490f9527f727633cbb81685a478eca80", "size_in_bytes": 1901}, {"_path": "samples/pc_sampling_utility/README.txt", "path_type": "hardlink", "sha256": "aca22ce313f3abcb84f676ae0afe088ddb12f01a9f88f38f4b2f637c1dbed5ca", "sha256_in_prefix": "aca22ce313f3abcb84f676ae0afe088ddb12f01a9f88f38f4b2f637c1dbed5ca", "size_in_bytes": 992}, {"_path": "samples/pc_sampling_utility/pc_sampling_utility.cpp", "path_type": "hardlink", "sha256": "5c1b34d26cd4f98ca1228a2dfaf9c169a2ec5ca7975b85ae139288c5f38d027f", "sha256_in_prefix": "5c1b34d26cd4f98ca1228a2dfaf9c169a2ec5ca7975b85ae139288c5f38d027f", "size_in_bytes": 1228}, {"_path": "samples/pc_sampling_utility/pc_sampling_utility_helper.h", "path_type": "hardlink", "sha256": "ead5db7a6e744717828a788ff1c9f0437fae2d9f044e2824731d50fef366d417", "sha256_in_prefix": "ead5db7a6e744717828a788ff1c9f0437fae2d9f044e2824731d50fef366d417", "size_in_bytes": 26799}, {"_path": "samples/sass_source_map/Makefile", "path_type": "hardlink", "sha256": "896dea0e6dfb628713293c69fa4118109405d113b2469ebf314cb7c3764c8153", "sha256_in_prefix": "896dea0e6dfb628713293c69fa4118109405d113b2469ebf314cb7c3764c8153", "size_in_bytes": 2985}, {"_path": "samples/sass_source_map/sass_source_map.cu", "path_type": "hardlink", "sha256": "e6ba10142b6851b27e73ef2980afcd3954bbd6c6452ec0f6bebba01c87527c88", "sha256_in_prefix": "e6ba10142b6851b27e73ef2980afcd3954bbd6c6452ec0f6bebba01c87527c88", "size_in_bytes": 5272}, {"_path": "samples/unified_memory/Makefile", "path_type": "hardlink", "sha256": "47aab797850106768a3472042d165e461b6915ab79d4f3482d550e95762c7090", "sha256_in_prefix": "47aab797850106768a3472042d165e461b6915ab79d4f3482d550e95762c7090", "size_in_bytes": 3100}, {"_path": "samples/unified_memory/unified_memory.cu", "path_type": "hardlink", "sha256": "b425245d280bc9a775e115979b67bc7e214c3bd7c9b1f20fa95ee1bde5d6d464", "sha256_in_prefix": "b425245d280bc9a775e115979b67bc7e214c3bd7c9b1f20fa95ee1bde5d6d464", "size_in_bytes": 4329}, {"_path": "samples/userrange_profiling/Makefile", "path_type": "hardlink", "sha256": "f06b8b99f52271789dc876b70be34612dd1e92ebf50a08abf3ebd0ab51d8650e", "sha256_in_prefix": "f06b8b99f52271789dc876b70be34612dd1e92ebf50a08abf3ebd0ab51d8650e", "size_in_bytes": 3774}, {"_path": "samples/userrange_profiling/user_range_profiling.cu", "path_type": "hardlink", "sha256": "080b3ab6022884c5553a72d0c1788e532aee023f4e92da6a133b60e38c413576", "sha256_in_prefix": "080b3ab6022884c5553a72d0c1788e532aee023f4e92da6a133b60e38c413576", "size_in_bytes": 17755}], "paths_version": 1}, "requested_spec": "None", "sha256": "ac7d2e0470a6534d81c6ae23d216912e4ccaa750d4158eed742383f0eb1442f5", "size": 12208761, "subdir": "win-64", "timestamp": 1680572686000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-cupti-12.1.105-0.tar.bz2", "version": "12.1.105"}