{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-cudart >=12.1.55", "cuda-nvrtc >=12.1.55", "cuda-opencl >=12.1.56", "libcublas >=12.1.0.26", "libcufft >=11.0.2.4", "libcurand >=10.3.2.56", "libcusolver >=11.4.4.55", "libcusparse >=12.0.2.55", "libnpp >=12.0.2.50", "libnvjitlink >=12.1.55", "libnvjpeg >=12.1.0.39"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-libraries-12.1.0-0", "files": [], "fn": "cuda-libraries-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-libraries-12.1.0-0", "type": 1}, "md5": "c05405ab40b0b61b9c016031ce93ccc4", "name": "cuda-libraries", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-libraries-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "08c9334a777061a0eec598c0cfc7c2fede2310df1a31c3d49cd98247e17e4f27", "size": 1477, "subdir": "win-64", "timestamp": 1677129968000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-libraries-12.1.0-0.tar.bz2", "version": "12.1.0"}