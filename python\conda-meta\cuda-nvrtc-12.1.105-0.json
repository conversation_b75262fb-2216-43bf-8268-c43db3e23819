{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": [], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-nvrtc-12.1.105-0", "files": ["lib/x64/nvrtc-builtins_static.lib", "lib/x64/nvrtc.lib", "lib/x64/nvrtc_static.lib"], "fn": "cuda-nvrtc-12.1.105-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-nvrtc-12.1.105-0", "type": 1}, "md5": "5572930c5c92f96cb5a9f48a8ff40efb", "name": "cuda-nvrtc", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-nvrtc-12.1.105-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/nvrtc-builtins_static.lib", "path_type": "hardlink", "sha256": "f6be9a351e51941dce78f5b171da06567335392004b64d5a0d24de87cf3354ec", "sha256_in_prefix": "f6be9a351e51941dce78f5b171da06567335392004b64d5a0d24de87cf3354ec", "size_in_bytes": 6968514}, {"_path": "lib/x64/nvrtc.lib", "path_type": "hardlink", "sha256": "bac8224f8f4f12a8730a13ee6bce23b45e137bff0a901a62f61ded77a1897a63", "sha256_in_prefix": "bac8224f8f4f12a8730a13ee6bce23b45e137bff0a901a62f61ded77a1897a63", "size_in_bytes": 6440}, {"_path": "lib/x64/nvrtc_static.lib", "path_type": "hardlink", "sha256": "c33d2c46fc840352c507548a3fa82103e41064be99fd6801a68e193a03a35d21", "sha256_in_prefix": "c33d2c46fc840352c507548a3fa82103e41064be99fd6801a68e193a03a35d21", "size_in_bytes": 321534344}], "paths_version": 1}, "requested_spec": "None", "sha256": "30c9a6303c80b72c7a16d8c35b7f63378cff6b17453ac5a690361f6310ba69a3", "size": 76729751, "subdir": "win-64", "timestamp": 1680573172000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-nvrtc-12.1.105-0.tar.bz2", "version": "12.1.105"}