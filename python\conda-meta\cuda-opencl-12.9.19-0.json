{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-version >=12.9,<12.10.0a0", "khronos-opencl-icd-loader >=2024.5.8", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-opencl-12.9.19-0", "files": ["Library/etc/OpenCL/vendors/cuda.icd"], "fn": "cuda-opencl-12.9.19-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-opencl-12.9.19-0", "type": 1}, "md5": "c6e1e79501924d9846b981e0c6edabe1", "name": "cuda-opencl", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-opencl-12.9.19-0.conda", "paths_data": {"paths": [{"_path": "Library/etc/OpenCL/vendors/cuda.icd", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}], "paths_version": 1}, "requested_spec": "None", "sha256": "b8046570b968a5338437b579419b2926c28316d6269b5354dac2a61930ea9471", "size": 17093, "subdir": "win-64", "timestamp": 1741064772000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-opencl-12.9.19-0.conda", "version": "12.9.19"}