{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-cudart-dev", "cuda-version >=12.9,<12.10.0a0"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-profiler-api-12.9.79-0", "files": ["Library/include/cudaProfiler.h", "Library/include/cuda_profiler_api.h"], "fn": "cuda-profiler-api-12.9.79-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-profiler-api-12.9.79-0", "type": 1}, "md5": "32922a7ecfaef3485c9a474ef14473c9", "name": "cuda-profiler-api", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-profiler-api-12.9.79-0.conda", "paths_data": {"paths": [{"_path": "Library/include/cudaProfiler.h", "path_type": "hardlink", "sha256": "b9c9fa71341b693f9f0b0ea8071e12a0869ac99afe669345a02145163867560d", "sha256_in_prefix": "b9c9fa71341b693f9f0b0ea8071e12a0869ac99afe669345a02145163867560d", "size_in_bytes": 7019}, {"_path": "Library/include/cuda_profiler_api.h", "path_type": "hardlink", "sha256": "c8973678f0c821e7a3938e0ce37fd6af49c3b4995b215b204c1959002f558322", "sha256_in_prefix": "c8973678f0c821e7a3938e0ce37fd6af49c3b4995b215b204c1959002f558322", "size_in_bytes": 4700}], "paths_version": 1}, "requested_spec": "None", "sha256": "32d532cae001da850f9593fda1dceead00be8278ee7ea7b900ac2cf526fe6ce3", "size": 19817, "subdir": "win-64", "timestamp": 1747088817000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-profiler-api-12.9.79-0.conda", "version": "12.9.79"}