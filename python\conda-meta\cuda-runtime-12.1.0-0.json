{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-libraries >=12.1.0"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-runtime-12.1.0-0", "files": [], "fn": "cuda-runtime-12.1.0-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-runtime-12.1.0-0", "type": 1}, "md5": "fa8278d90eeeb45ef08cc359bb76028c", "name": "cuda-runtime", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-runtime-12.1.0-0.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "4cf8924c9717f364cf25a903abd7f281fe84146c2d4f342647614878aa3cac4e", "size": 1392, "subdir": "win-64", "timestamp": 1677130036000, "url": "https://conda.anaconda.org/nvidia/win-64/cuda-runtime-12.1.0-0.tar.bz2", "version": "12.1.0"}