{"build": "3", "build_number": 3, "channel": "https://conda.anaconda.org/nvidia/noarch", "constrains": ["__cuda >=12", "cudatoolkit 12.9|12.9.*"], "depends": [], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-version-12.9-3", "files": [], "fn": "cuda-version-12.9-3.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-version-12.9-3", "type": 1}, "md5": "40969c18662ba1d35292e70e8545ce90", "name": "cuda-version", "noarch": "generic", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\cuda-version-12.9-3.conda", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "d05c0d4e2d1fbf32275db798275b9a8b57e97c0748f4c073ba6c532fe839bb06", "size": 17161, "subdir": "noarch", "timestamp": 1748727273000, "url": "https://conda.anaconda.org/nvidia/noarch/cuda-version-12.9-3.conda", "version": "12.9"}