{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\filelock-3.19.1-pyhd8ed1ab_0", "files": ["Lib/site-packages/filelock-3.19.1.dist-info/INSTALLER", "Lib/site-packages/filelock-3.19.1.dist-info/METADATA", "Lib/site-packages/filelock-3.19.1.dist-info/RECORD", "Lib/site-packages/filelock-3.19.1.dist-info/REQUESTED", "Lib/site-packages/filelock-3.19.1.dist-info/WHEEL", "Lib/site-packages/filelock-3.19.1.dist-info/direct_url.json", "Lib/site-packages/filelock-3.19.1.dist-info/licenses/LICENSE", "Lib/site-packages/filelock/__init__.py", "Lib/site-packages/filelock/_api.py", "Lib/site-packages/filelock/_error.py", "Lib/site-packages/filelock/_soft.py", "Lib/site-packages/filelock/_unix.py", "Lib/site-packages/filelock/_util.py", "Lib/site-packages/filelock/_windows.py", "Lib/site-packages/filelock/asyncio.py", "Lib/site-packages/filelock/py.typed", "Lib/site-packages/filelock/version.py", "Lib/site-packages/filelock/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_api.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_error.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_soft.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_unix.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_util.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/_windows.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/asyncio.cpython-310.pyc", "Lib/site-packages/filelock/__pycache__/version.cpython-310.pyc"], "fn": "filelock-3.19.1-pyhd8ed1ab_0.conda", "license": "Unlicense", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\filelock-3.19.1-pyhd8ed1ab_0", "type": 1}, "md5": "9c418d067409452b2e87e0016257da68", "name": "filelock", "noarch": "python", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\filelock-3.19.1-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/filelock-3.19.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/filelock-3.19.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "822e98d63d6669cd35e35b0907f41ad8c4ef870c92260d84a868dd7e1040e0e8", "sha256_in_prefix": "822e98d63d6669cd35e35b0907f41ad8c4ef870c92260d84a868dd7e1040e0e8", "size_in_bytes": 2108}, {"_path": "site-packages/filelock-3.19.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "25078bc7562f3505c77c960f0cb0bdea367f2c6dfe2637e1fe7ab61c71d84289", "sha256_in_prefix": "25078bc7562f3505c77c960f0cb0bdea367f2c6dfe2637e1fe7ab61c71d84289", "size_in_bytes": 1765}, {"_path": "site-packages/filelock-3.19.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/filelock-3.19.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/filelock-3.19.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "61019a3dbdae76b88bff7dfd666e3e5af45c290454adc2d260a91089008d18b0", "sha256_in_prefix": "61019a3dbdae76b88bff7dfd666e3e5af45c290454adc2d260a91089008d18b0", "size_in_bytes": 104}, {"_path": "site-packages/filelock-3.19.1.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "88d9b4eb60579c191ec391ca04c16130572d7eedc4a86daa58bf28c6e14c9bcd", "sha256_in_prefix": "88d9b4eb60579c191ec391ca04c16130572d7eedc4a86daa58bf28c6e14c9bcd", "size_in_bytes": 1210}, {"_path": "site-packages/filelock/__init__.py", "path_type": "hardlink", "sha256": "fedffe380197a3fab23daf65350d589f355812f496dc8d289cfab3a689ac5558", "sha256_in_prefix": "fedffe380197a3fab23daf65350d589f355812f496dc8d289cfab3a689ac5558", "size_in_bytes": 1769}, {"_path": "site-packages/filelock/_api.py", "path_type": "hardlink", "sha256": "d9a01305e277fa3b4c8f93929bb104e77f6235a4c1b1fd77297b5c04ca22f283", "sha256_in_prefix": "d9a01305e277fa3b4c8f93929bb104e77f6235a4c1b1fd77297b5c04ca22f283", "size_in_bytes": 14545}, {"_path": "site-packages/filelock/_error.py", "path_type": "hardlink", "sha256": "fb98cc7234eeeb4600bc03b551ba830f5188123564c2bf31085c0306d31e6038", "sha256_in_prefix": "fb98cc7234eeeb4600bc03b551ba830f5188123564c2bf31085c0306d31e6038", "size_in_bytes": 787}, {"_path": "site-packages/filelock/_soft.py", "path_type": "hardlink", "sha256": "85aaad73f4c1fca25b62fd9af22b8401c94ab8ce1f306d6f4dca76f2c2bdd7d7", "sha256_in_prefix": "85aaad73f4c1fca25b62fd9af22b8401c94ab8ce1f306d6f4dca76f2c2bdd7d7", "size_in_bytes": 1711}, {"_path": "site-packages/filelock/_unix.py", "path_type": "hardlink", "sha256": "7863ace200e067ee5f1a7254cfe3a424379990033382f61c0fc8550fa5c7edee", "sha256_in_prefix": "7863ace200e067ee5f1a7254cfe3a424379990033382f61c0fc8550fa5c7edee", "size_in_bytes": 2351}, {"_path": "site-packages/filelock/_util.py", "path_type": "hardlink", "sha256": "4070683452187db013861a2d1f743c13669c15cf38c291b8f7e4febaed35ed91", "sha256_in_prefix": "4070683452187db013861a2d1f743c13669c15cf38c291b8f7e4febaed35ed91", "size_in_bytes": 1715}, {"_path": "site-packages/filelock/_windows.py", "path_type": "hardlink", "sha256": "f24e1720197fcd955f182da0cf4904afc0d906fa4d6bcc1d53da9e33562b05bf", "sha256_in_prefix": "f24e1720197fcd955f182da0cf4904afc0d906fa4d6bcc1d53da9e33562b05bf", "size_in_bytes": 2179}, {"_path": "site-packages/filelock/asyncio.py", "path_type": "hardlink", "sha256": "2c3f7292c0b6e055749a1fe1ce0cd58b89a63a316055509bed92db2ea25caace", "sha256_in_prefix": "2c3f7292c0b6e055749a1fe1ce0cd58b89a63a316055509bed92db2ea25caace", "size_in_bytes": 12483}, {"_path": "site-packages/filelock/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/filelock/version.py", "path_type": "hardlink", "sha256": "87af286c2dc7c2b47efc6bdcdc193a59158f623b9101cb2637f54b7d018f638a", "sha256_in_prefix": "87af286c2dc7c2b47efc6bdcdc193a59158f623b9101cb2637f54b7d018f638a", "size_in_bytes": 706}, {"_path": "Lib/site-packages/filelock/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_api.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_error.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_soft.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_unix.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_util.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/_windows.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/asyncio.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/filelock/__pycache__/version.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "7a2497c775cc7da43b5e32fc5cf9f4e8301ca723f0eb7f808bbe01c6094a3693", "size": 18003, "subdir": "noarch", "timestamp": 1755216353000, "url": "https://conda.anaconda.org/conda-forge/noarch/filelock-3.19.1-pyhd8ed1ab_0.conda", "version": "3.19.1"}