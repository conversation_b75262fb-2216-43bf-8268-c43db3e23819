==> 2025-08-31 00:52:12 <==
# cmd: C:\ProgramData\anaconda3\Scripts\conda-script.py create -p .\python python=3.10 -y
# conda version: 23.11.0
+conda-forge/noarch::ca-certificates-2025.8.3-h4c7d964_0
+conda-forge/noarch::pip-25.2-pyh8b19718_0
+conda-forge/noarch::setuptools-80.9.0-pyhff2d567_0
+conda-forge/noarch::tzdata-2025b-h78e105d_0
+conda-forge/noarch::wheel-0.45.1-pyhd8ed1ab_1
+conda-forge/win-64::bzip2-1.0.8-h2466b09_7
+conda-forge/win-64::libexpat-2.7.1-hac47afa_0
+conda-forge/win-64::libffi-3.4.6-h537db12_1
+conda-forge/win-64::liblzma-5.8.1-h2466b09_2
+conda-forge/win-64::libsqlite-3.50.4-hf5d6505_0
+conda-forge/win-64::libzlib-1.3.1-h2466b09_2
+conda-forge/win-64::openssl-3.5.2-h725018a_0
+conda-forge/win-64::python-3.10.18-h8c5b53a_0_cpython
+conda-forge/win-64::tk-8.6.13-h2c6b04d_2
+conda-forge/win-64::ucrt-10.0.26100.0-h57928b3_0
+conda-forge/win-64::vc-14.3-h41ae7f8_31
+conda-forge/win-64::vc14_runtime-14.44.35208-h818238b_31
+conda-forge/win-64::vcomp14-14.44.35208-h818238b_31
# update specs: ['python=3.10']
==> 2025-08-31 00:59:10 <==
# cmd: C:\ProgramData\anaconda3\Scripts\conda-script.py install -c conda-forge pynini
# conda version: 23.11.0
+conda-forge/noarch::font-ttf-dejavu-sans-mono-2.37-hab24e00_0
+conda-forge/noarch::font-ttf-inconsolata-3.000-h77eed37_0
+conda-forge/noarch::font-ttf-source-code-pro-2.038-h77eed37_0
+conda-forge/noarch::font-ttf-ubuntu-0.83-h77eed37_3
+conda-forge/noarch::fonts-conda-ecosystem-1-0
+conda-forge/noarch::fonts-conda-forge-1-0
+conda-forge/noarch::python_abi-3.10-8_cp310
+conda-forge/win-64::_openmp_mutex-4.5-2_gnu
+conda-forge/win-64::cairo-1.18.4-h5782bbf_0
+conda-forge/win-64::dlfcn-win32-1.4.1-h63175ca_0
+conda-forge/win-64::fontconfig-2.15.0-h765892d_1
+conda-forge/win-64::freetype-2.13.3-h57928b3_1
+conda-forge/win-64::fribidi-1.0.10-h8d14728_0
+conda-forge/win-64::getopt-win32-0.1-h6a83c73_3
+conda-forge/win-64::graphite2-1.3.14-hac47afa_2
+conda-forge/win-64::graphviz-13.1.2-ha5e8f4b_0
+conda-forge/win-64::gts-0.7.6-h6b5321d_4
+conda-forge/win-64::harfbuzz-11.4.4-h5f2951f_0
+conda-forge/win-64::icu-75.1-he0c23c2_0
+conda-forge/win-64::lerc-4.0.0-h6470a55_1
+conda-forge/win-64::libdeflate-1.24-h76ddb4d_0
+conda-forge/win-64::libfreetype-2.13.3-h57928b3_1
+conda-forge/win-64::libfreetype6-2.13.3-h0b5ce68_1
+conda-forge/win-64::libgcc-15.1.0-h1383e82_4
+conda-forge/win-64::libgd-2.3.3-h7208af6_11
+conda-forge/win-64::libglib-2.84.3-h1c1036b_0
+conda-forge/win-64::libgomp-15.1.0-h1383e82_4
+conda-forge/win-64::libiconv-1.18-hc1393d2_2
+conda-forge/win-64::libintl-0.22.5-h5728263_3
+conda-forge/win-64::libjpeg-turbo-3.1.0-h2466b09_0
+conda-forge/win-64::libpng-1.6.50-h7351971_1
+conda-forge/win-64::libtiff-4.7.0-h550210a_6
+conda-forge/win-64::libwebp-base-1.6.0-h4d5522a_0
+conda-forge/win-64::libwinpthread-12.0.0.r4.gg4f2fc60ca-h57928b3_9
+conda-forge/win-64::libxcb-1.17.0-h0e4246c_0
+conda-forge/win-64::openfst-1.8.4-hc790b64_1
+conda-forge/win-64::pango-1.56.4-h03d888a_0
+conda-forge/win-64::pcre2-10.45-h99c9b8b_0
+conda-forge/win-64::pixman-0.46.4-h5112557_1
+conda-forge/win-64::pthread-stubs-0.4-h0e40799_1002
+conda-forge/win-64::pynini-2.1.6.post1-py310he9f1925_4
+conda-forge/win-64::vs2015_runtime-14.44.35208-h38c0c73_31
+conda-forge/win-64::xorg-libice-1.1.2-h0e40799_0
+conda-forge/win-64::xorg-libsm-1.2.6-h0e40799_0
+conda-forge/win-64::xorg-libx11-1.8.12-hf48077a_0
+conda-forge/win-64::xorg-libxau-1.0.12-h0e40799_0
+conda-forge/win-64::xorg-libxdmcp-1.1.5-h0e40799_0
+conda-forge/win-64::xorg-libxext-1.3.6-h0e40799_0
+conda-forge/win-64::xorg-libxpm-3.5.17-h0e40799_1
+conda-forge/win-64::xorg-libxt-1.3.1-h0e40799_0
+conda-forge/win-64::zstd-1.5.7-hbeecb71_2
# update specs: ['pynini']
==> 2025-08-31 01:07:57 <==
# cmd: C:\ProgramData\anaconda3\Scripts\conda-script.py install pytorch==2.3.1 torchvision==0.18.1 torchaudio==2.3.1 pytorch-cuda=12.1 -c pytorch -c nvidia
# conda version: 23.11.0
+conda-forge/noarch::certifi-2025.8.3-pyhd8ed1ab_0
+conda-forge/noarch::charset-normalizer-3.4.3-pyhd8ed1ab_0
+conda-forge/noarch::filelock-3.19.1-pyhd8ed1ab_0
+conda-forge/noarch::h2-4.3.0-pyhcf101f3_0
+conda-forge/noarch::hpack-4.1.0-pyhd8ed1ab_0
+conda-forge/noarch::hyperframe-6.1.0-pyhd8ed1ab_0
+conda-forge/noarch::idna-3.10-pyhd8ed1ab_1
+conda-forge/noarch::jinja2-3.1.6-pyhd8ed1ab_0
+conda-forge/noarch::mpmath-1.3.0-pyhd8ed1ab_1
+conda-forge/noarch::networkx-3.4.2-pyh267e887_2
+conda-forge/noarch::pycparser-2.22-pyh29332c3_1
+conda-forge/noarch::pysocks-1.7.1-pyh09c184e_7
+conda-forge/noarch::requests-2.32.5-pyhd8ed1ab_0
+conda-forge/noarch::sympy-1.14.0-pyh04b8f61_5
+conda-forge/noarch::typing_extensions-4.15.0-pyhcf101f3_0
+conda-forge/noarch::urllib3-2.5.0-pyhd8ed1ab_0
+conda-forge/noarch::win_inet_pton-1.1.0-pyh7428d3b_8
+conda-forge/win-64::blas-1.0-mkl
+conda-forge/win-64::brotli-python-1.1.0-py310h9e98ed7_3
+conda-forge/win-64::cffi-1.17.1-py310ha8f682b_0
+conda-forge/win-64::intel-openmp-2025.2.0-h57928b3_757
+conda-forge/win-64::khronos-opencl-icd-loader-2024.10.24-h2466b09_1
+conda-forge/win-64::lcms2-2.17-hbcf6048_0
+conda-forge/win-64::libblas-3.9.0-12_win64_mkl
+conda-forge/win-64::libcblas-3.9.0-12_win64_mkl
+conda-forge/win-64::libhwloc-2.12.1-default_h88281d1_1000
+conda-forge/win-64::liblapack-3.9.0-12_win64_mkl
+conda-forge/win-64::libuv-1.51.0-hfd05255_1
+conda-forge/win-64::libxml2-2.13.8-h741aa76_1
+conda-forge/win-64::markupsafe-3.0.2-py310h38315fa_1
+conda-forge/win-64::mkl-2021.4.0-h0e2418a_729
+conda-forge/win-64::numpy-2.2.6-py310h4987827_0
+conda-forge/win-64::opencl-headers-2025.06.13-he0c23c2_0
+conda-forge/win-64::openjpeg-2.5.3-h24db6dd_1
+conda-forge/win-64::pillow-11.3.0-py310h6d647b9_0
+conda-forge/win-64::pyyaml-6.0.2-py310h38315fa_2
+conda-forge/win-64::tbb-2021.13.0-h18a62a1_3
+conda-forge/win-64::yaml-0.2.5-h6a83c73_3
+conda-forge/win-64::zstandard-0.23.0-py310h29418f3_3
+nvidia/noarch::cuda-version-12.9-3
+nvidia/win-64::cuda-cccl-12.9.27-0
+nvidia/win-64::cuda-cccl_win-64-12.9.27-0
+nvidia/win-64::cuda-cudart-12.1.105-0
+nvidia/win-64::cuda-cudart-dev-12.1.105-0
+nvidia/win-64::cuda-cupti-12.1.105-0
+nvidia/win-64::cuda-libraries-12.1.0-0
+nvidia/win-64::cuda-libraries-dev-12.1.0-0
+nvidia/win-64::cuda-nvrtc-12.1.105-0
+nvidia/win-64::cuda-nvrtc-dev-12.1.105-0
+nvidia/win-64::cuda-nvtx-12.1.105-0
+nvidia/win-64::cuda-opencl-12.9.19-0
+nvidia/win-64::cuda-opencl-dev-12.9.19-0
+nvidia/win-64::cuda-profiler-api-12.9.79-0
+nvidia/win-64::cuda-runtime-12.1.0-0
+nvidia/win-64::libcublas-12.1.0.26-0
+nvidia/win-64::libcublas-dev-12.1.0.26-0
+nvidia/win-64::libcufft-11.0.2.4-0
+nvidia/win-64::libcufft-dev-11.0.2.4-0
+nvidia/win-64::libcurand-10.3.10.19-0
+nvidia/win-64::libcurand-dev-10.3.10.19-0
+nvidia/win-64::libcusolver-11.4.4.55-0
+nvidia/win-64::libcusolver-dev-11.4.4.55-0
+nvidia/win-64::libcusparse-12.0.2.55-0
+nvidia/win-64::libcusparse-dev-12.0.2.55-0
+nvidia/win-64::libnpp-12.0.2.50-0
+nvidia/win-64::libnpp-dev-12.0.2.50-0
+nvidia/win-64::libnvjitlink-12.1.105-0
+nvidia/win-64::libnvjitlink-dev-12.1.105-0
+nvidia/win-64::libnvjpeg-12.1.1.14-0
+nvidia/win-64::libnvjpeg-dev-12.1.1.14-0
+pytorch/noarch::pytorch-mutex-1.0-cuda
+pytorch/win-64::pytorch-2.3.1-py3.10_cuda12.1_cudnn8_0
+pytorch/win-64::pytorch-cuda-12.1-hde6ce7c_6
+pytorch/win-64::torchaudio-2.3.1-py310_cu121
+pytorch/win-64::torchvision-0.18.1-py310_cu121
# update specs: ['pytorch-cuda=12.1', 'pytorch==2.3.1', 'torchaudio==2.3.1', 'torchvision==0.18.1']
