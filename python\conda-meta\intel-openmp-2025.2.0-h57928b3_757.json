{"build": "h57928b3_757", "build_number": 757, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": [], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\intel-openmp-2025.2.0-h57928b3_757", "files": ["Library/bin/libiomp5md.dll", "Library/bin/libiomp5md.pdb", "Library/bin/libiomp5md_db.dll", "Library/bin/libiompstubs5md.dll", "Library/bin/libomp-fallback-cassert.spv", "Library/bin/libomp-fallback-cmath-fp64.spv", "Library/bin/libomp-fallback-cmath.spv", "Library/bin/libomp-fallback-complex-fp64.spv", "Library/bin/libomp-fallback-complex.spv", "Library/bin/libomp-fallback-cstring.spv", "Library/bin/libomp-fallback-imf-fp64.spv", "Library/bin/libomp-fallback-imf.spv", "Library/bin/omptarget.dll", "Library/bin/omptarget.rtl.level0.dll", "Library/bin/omptarget.rtl.opencl.dll", "Library/bin/omptarget.rtl.unified_runtime.dll", "Library/bin/omptarget.sycl.wrap.dll", "Library/lib/libiomp5md.lib", "Library/lib/libiompstubs5md.lib", "Library/lib/omptarget.lib", "Library/lib/pkgconfig/openmp.pc", "opt/compiler/Library/lib/libomp-cmath-fp64.obj", "opt/compiler/Library/lib/libomp-cmath.obj", "opt/compiler/Library/lib/libomp-complex-fp64.obj", "opt/compiler/Library/lib/libomp-complex.obj", "opt/compiler/Library/lib/libomp-device-svml.obj", "opt/compiler/Library/lib/libomp-fallback-cassert.obj", "opt/compiler/Library/lib/libomp-fallback-cmath-fp64.obj", "opt/compiler/Library/lib/libomp-fallback-cmath.obj", "opt/compiler/Library/lib/libomp-fallback-complex-fp64.obj", "opt/compiler/Library/lib/libomp-fallback-complex.obj", "opt/compiler/Library/lib/libomp-fallback-cstring.obj", "opt/compiler/Library/lib/libomp-fallback-imf-fp64.obj", "opt/compiler/Library/lib/libomp-fallback-imf.obj", "opt/compiler/Library/lib/libomp-imf-fp64.obj", "opt/compiler/Library/lib/libomp-imf.obj", "opt/compiler/Library/lib/libomp-itt-compiler-wrappers.obj", "opt/compiler/Library/lib/libomp-itt-stubs.obj", "opt/compiler/Library/lib/libomp-itt-user-wrappers.obj", "opt/compiler/Library/lib/libomp-msvc-math.obj", "opt/compiler/Library/lib/libomp-msvc.obj", "opt/compiler/Library/lib/libomp-nextgen-spirvdevicertl-fp64.obj", "opt/compiler/Library/lib/libomp-nextgen-spirvdevicertl.obj", "opt/compiler/Library/lib/libomp-spirvdevicertl-optional.obj", "opt/compiler/Library/lib/libomp-spirvdevicertl-required.obj", "opt/compiler/Library/lib/libomp-spirvdevicertl.obj", "opt/compiler/Library/lib/libomptarget-nextgen.bc", "opt/compiler/Library/lib/libomptarget-nextgen64.bc", "opt/compiler/Library/lib/libomptarget-opencl-optional.bc", "opt/compiler/Library/lib/libomptarget-opencl-required.bc", "opt/compiler/Library/lib/libomptarget-opencl.bc", "opt/compiler/include/omp-tools.h", "opt/compiler/include/omp.h", "opt/compiler/include/omp_lib.h", "share/doc/compiler/licensing/openmp/third-party-programs.txt"], "fn": "intel-openmp-2025.2.0-h57928b3_757.conda", "license": "LicenseRef-IntelSimplifiedSoftwareOct2022", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\intel-openmp-2025.2.0-h57928b3_757", "type": 1}, "md5": "664dae0c154c42ca84c8ca0a337cc9a4", "name": "intel-openmp", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\intel-openmp-2025.2.0-h57928b3_757.conda", "paths_data": {"paths": [{"_path": "Library/bin/libiomp5md.dll", "path_type": "hardlink", "sha256": "d9d66ed25f1a0ea725fa3a41b22cfd5d182c19dbe4771d9c90ca02ad7466f6a1", "sha256_in_prefix": "d9d66ed25f1a0ea725fa3a41b22cfd5d182c19dbe4771d9c90ca02ad7466f6a1", "size_in_bytes": 1613680}, {"_path": "Library/bin/libiomp5md.pdb", "path_type": "hardlink", "sha256": "6fc9f4750acb173c0402aee32c75d793dcc0f80d230943f305655f0850a33470", "sha256_in_prefix": "6fc9f4750acb173c0402aee32c75d793dcc0f80d230943f305655f0850a33470", "size_in_bytes": 5115904}, {"_path": "Library/bin/libiomp5md_db.dll", "path_type": "hardlink", "sha256": "8fa0eb611875b7d2361291e4565b2c1c3da102ee1a07418e1a90efa40db28057", "sha256_in_prefix": "8fa0eb611875b7d2361291e4565b2c1c3da102ee1a07418e1a90efa40db28057", "size_in_bytes": 126832}, {"_path": "Library/bin/libiompstubs5md.dll", "path_type": "hardlink", "sha256": "daeb39e4ced3bfce4a622b63b8b0281613eb8180266f8dd1dc678346df1c7697", "sha256_in_prefix": "daeb39e4ced3bfce4a622b63b8b0281613eb8180266f8dd1dc678346df1c7697", "size_in_bytes": 43888}, {"_path": "Library/bin/libomp-fallback-cassert.spv", "path_type": "hardlink", "sha256": "83bacdfa8e9803911e9c71d5cb2e77df9ff372403123500d675285680450c141", "sha256_in_prefix": "83bacdfa8e9803911e9c71d5cb2e77df9ff372403123500d675285680450c141", "size_in_bytes": 1504}, {"_path": "Library/bin/libomp-fallback-cmath-fp64.spv", "path_type": "hardlink", "sha256": "d6d8b8925cd5b88f0c68ffbc236f66c6d581bca5821e760cb24a7522517c320a", "sha256_in_prefix": "d6d8b8925cd5b88f0c68ffbc236f66c6d581bca5821e760cb24a7522517c320a", "size_in_bytes": 8928}, {"_path": "Library/bin/libomp-fallback-cmath.spv", "path_type": "hardlink", "sha256": "f14dd8fd378f35e56763292beb8191cefdc9c6f2cdeed6e3a4a4edf8d715fd34", "sha256_in_prefix": "f14dd8fd378f35e56763292beb8191cefdc9c6f2cdeed6e3a4a4edf8d715fd34", "size_in_bytes": 10768}, {"_path": "Library/bin/libomp-fallback-complex-fp64.spv", "path_type": "hardlink", "sha256": "6f0dd004e4f0ff11b7bcdbc6516eeea47cda159079f1f3714adf51805f1e331d", "sha256_in_prefix": "6f0dd004e4f0ff11b7bcdbc6516eeea47cda159079f1f3714adf51805f1e331d", "size_in_bytes": 54488}, {"_path": "Library/bin/libomp-fallback-complex.spv", "path_type": "hardlink", "sha256": "ab684431de1bb04bf9ba4e8bf8eb7fde068f4dba3e0cf1497311b711b9eb05b7", "sha256_in_prefix": "ab684431de1bb04bf9ba4e8bf8eb7fde068f4dba3e0cf1497311b711b9eb05b7", "size_in_bytes": 53532}, {"_path": "Library/bin/libomp-fallback-cstring.spv", "path_type": "hardlink", "sha256": "8919c2e5dd2473a1fc0750b2dbc8102246092e1ab323c15e2a91992c9853a1c4", "sha256_in_prefix": "8919c2e5dd2473a1fc0750b2dbc8102246092e1ab323c15e2a91992c9853a1c4", "size_in_bytes": 14252}, {"_path": "Library/bin/libomp-fallback-imf-fp64.spv", "path_type": "hardlink", "sha256": "078a2d7cea88defd38ab9355107baca4d427f7f883d5f2a6e931d45b3e9eac82", "sha256_in_prefix": "078a2d7cea88defd38ab9355107baca4d427f7f883d5f2a6e931d45b3e9eac82", "size_in_bytes": 4813236}, {"_path": "Library/bin/libomp-fallback-imf.spv", "path_type": "hardlink", "sha256": "6069a2c6e37ee6f830942bb2392cd1ae3de1cdd338c78aa519c813149f11b5eb", "sha256_in_prefix": "6069a2c6e37ee6f830942bb2392cd1ae3de1cdd338c78aa519c813149f11b5eb", "size_in_bytes": 2578152}, {"_path": "Library/bin/omptarget.dll", "path_type": "hardlink", "sha256": "98eba4355dfbbe0ad63c685b446e4c841f12542a432d0a0f8b93e818123d4363", "sha256_in_prefix": "98eba4355dfbbe0ad63c685b446e4c841f12542a432d0a0f8b93e818123d4363", "size_in_bytes": 57461616}, {"_path": "Library/bin/omptarget.rtl.level0.dll", "path_type": "hardlink", "sha256": "e4f4458b96ef4c56a2acc039dfe161376811087b0f38d97328620b2088f54ad5", "sha256_in_prefix": "e4f4458b96ef4c56a2acc039dfe161376811087b0f38d97328620b2088f54ad5", "size_in_bytes": 1687920}, {"_path": "Library/bin/omptarget.rtl.opencl.dll", "path_type": "hardlink", "sha256": "4710b57012aa1a0bdfd2840e7255defdb94388a0b87003b32f637653fe0c1ee4", "sha256_in_prefix": "4710b57012aa1a0bdfd2840e7255defdb94388a0b87003b32f637653fe0c1ee4", "size_in_bytes": 1529712}, {"_path": "Library/bin/omptarget.rtl.unified_runtime.dll", "path_type": "hardlink", "sha256": "24a99a1c34d9a4ea30c022115dae32aaea759ea43790869eeb11503c70c102f2", "sha256_in_prefix": "24a99a1c34d9a4ea30c022115dae32aaea759ea43790869eeb11503c70c102f2", "size_in_bytes": 1399152}, {"_path": "Library/bin/omptarget.sycl.wrap.dll", "path_type": "hardlink", "sha256": "c53cc8fe1575d08149c08915af73cef5f6a107caf00f3c66648f9f9299ceb05b", "sha256_in_prefix": "c53cc8fe1575d08149c08915af73cef5f6a107caf00f3c66648f9f9299ceb05b", "size_in_bytes": 41328}, {"_path": "Library/lib/libiomp5md.lib", "path_type": "hardlink", "sha256": "6b9b9a328034531476d6dd30fc9ada55faed2749cf19649234a1f02a7f41be8b", "sha256_in_prefix": "6b9b9a328034531476d6dd30fc9ada55faed2749cf19649234a1f02a7f41be8b", "size_in_bytes": 269442}, {"_path": "Library/lib/libiompstubs5md.lib", "path_type": "hardlink", "sha256": "fabee749ad5853a5a8a6790ae1050d8701972a3a5f6da9f9df906f195d07800b", "sha256_in_prefix": "fabee749ad5853a5a8a6790ae1050d8701972a3a5f6da9f9df906f195d07800b", "size_in_bytes": 74654}, {"_path": "Library/lib/omptarget.lib", "path_type": "hardlink", "sha256": "f34f1eb5c12ea04ce46955e2f460ff934130b021ba6459e9833ea012650344b1", "sha256_in_prefix": "f34f1eb5c12ea04ce46955e2f460ff934130b021ba6459e9833ea012650344b1", "size_in_bytes": 50372}, {"_path": "Library/lib/pkgconfig/openmp.pc", "path_type": "hardlink", "sha256": "5efac642c079f7944e72e228348ea58634c60e373c59e103080cb26e880a514f", "sha256_in_prefix": "5efac642c079f7944e72e228348ea58634c60e373c59e103080cb26e880a514f", "size_in_bytes": 895}, {"_path": "opt/compiler/Library/lib/libomp-cmath-fp64.obj", "path_type": "hardlink", "sha256": "f13787d9d94f64d9fdeb8def93a59ceee116f86d48035f49708cdd90f82ded2e", "sha256_in_prefix": "f13787d9d94f64d9fdeb8def93a59ceee116f86d48035f49708cdd90f82ded2e", "size_in_bytes": 88872}, {"_path": "opt/compiler/Library/lib/libomp-cmath.obj", "path_type": "hardlink", "sha256": "e316de28d27bd414c3efb8cc0965a654d02d048ac61bebf229027ba32fbd78df", "sha256_in_prefix": "e316de28d27bd414c3efb8cc0965a654d02d048ac61bebf229027ba32fbd78df", "size_in_bytes": 39512}, {"_path": "opt/compiler/Library/lib/libomp-complex-fp64.obj", "path_type": "hardlink", "sha256": "6be8882aa1c66e9b557ab8b4f17e8b481feb1ce7e785183f38839876c3f91cd1", "sha256_in_prefix": "6be8882aa1c66e9b557ab8b4f17e8b481feb1ce7e785183f38839876c3f91cd1", "size_in_bytes": 39486}, {"_path": "opt/compiler/Library/lib/libomp-complex.obj", "path_type": "hardlink", "sha256": "18c8bafd7eddcf221c2677a29f20594f86c314dbd83f5a2a5c03791abaf2d1ec", "sha256_in_prefix": "18c8bafd7eddcf221c2677a29f20594f86c314dbd83f5a2a5c03791abaf2d1ec", "size_in_bytes": 39678}, {"_path": "opt/compiler/Library/lib/libomp-device-svml.obj", "path_type": "hardlink", "sha256": "fa337c889350f3e8117914790529a9220918b2d05cf8a6cd1d9eb8c863846460", "sha256_in_prefix": "fa337c889350f3e8117914790529a9220918b2d05cf8a6cd1d9eb8c863846460", "size_in_bytes": 1264061}, {"_path": "opt/compiler/Library/lib/libomp-fallback-cassert.obj", "path_type": "hardlink", "sha256": "4791239aeb311c632c3321ddff00e6d1022f4b5781c35292fdc8dc742c9b74f2", "sha256_in_prefix": "4791239aeb311c632c3321ddff00e6d1022f4b5781c35292fdc8dc742c9b74f2", "size_in_bytes": 10765}, {"_path": "opt/compiler/Library/lib/libomp-fallback-cmath-fp64.obj", "path_type": "hardlink", "sha256": "f0e77e23e3b463c9fe7e967114a0e4eca522e1ccabb2ce658255b59d62ef5a99", "sha256_in_prefix": "f0e77e23e3b463c9fe7e967114a0e4eca522e1ccabb2ce658255b59d62ef5a99", "size_in_bytes": 40185}, {"_path": "opt/compiler/Library/lib/libomp-fallback-cmath.obj", "path_type": "hardlink", "sha256": "49ad2894448412e6a81c6038c3163650e5d5e4bc2783f2b9b5d262e859d84c5e", "sha256_in_prefix": "49ad2894448412e6a81c6038c3163650e5d5e4bc2783f2b9b5d262e859d84c5e", "size_in_bytes": 44872}, {"_path": "opt/compiler/Library/lib/libomp-fallback-complex-fp64.obj", "path_type": "hardlink", "sha256": "36711cb0fb7728f62b1ccfaafd840d1ed226827486d22e7980e211420cffb486", "sha256_in_prefix": "36711cb0fb7728f62b1ccfaafd840d1ed226827486d22e7980e211420cffb486", "size_in_bytes": 74461}, {"_path": "opt/compiler/Library/lib/libomp-fallback-complex.obj", "path_type": "hardlink", "sha256": "ef704ba2776e0d08f08a2930496cc3de00fb2759080c9fb9f7bb843e2b91c5fe", "sha256_in_prefix": "ef704ba2776e0d08f08a2930496cc3de00fb2759080c9fb9f7bb843e2b91c5fe", "size_in_bytes": 72316}, {"_path": "opt/compiler/Library/lib/libomp-fallback-cstring.obj", "path_type": "hardlink", "sha256": "fb2d7dab6a0852a47a984f8484c99a85e7be01a9eeb2a104cfaaf1a8e26b7c10", "sha256_in_prefix": "fb2d7dab6a0852a47a984f8484c99a85e7be01a9eeb2a104cfaaf1a8e26b7c10", "size_in_bytes": 19934}, {"_path": "opt/compiler/Library/lib/libomp-fallback-imf-fp64.obj", "path_type": "hardlink", "sha256": "efd32b66c783bbd1541d486685311c7fd7065430df3916a3dee19d9e1ca62866", "sha256_in_prefix": "efd32b66c783bbd1541d486685311c7fd7065430df3916a3dee19d9e1ca62866", "size_in_bytes": 8377705}, {"_path": "opt/compiler/Library/lib/libomp-fallback-imf.obj", "path_type": "hardlink", "sha256": "c7796612771a61c7d3e8ac4853bebb3c0a45ac227570d291ab84f8428c1faf2b", "sha256_in_prefix": "c7796612771a61c7d3e8ac4853bebb3c0a45ac227570d291ab84f8428c1faf2b", "size_in_bytes": 4465311}, {"_path": "opt/compiler/Library/lib/libomp-imf-fp64.obj", "path_type": "hardlink", "sha256": "541d4818999725172e06f6a537379e823223ddab9172f7049b94472ab2bd9d49", "sha256_in_prefix": "541d4818999725172e06f6a537379e823223ddab9172f7049b94472ab2bd9d49", "size_in_bytes": 505787}, {"_path": "opt/compiler/Library/lib/libomp-imf.obj", "path_type": "hardlink", "sha256": "3440eea172ef933b1db31786c249769ddbac016e7cb31fcc2fc0456034de2062", "sha256_in_prefix": "3440eea172ef933b1db31786c249769ddbac016e7cb31fcc2fc0456034de2062", "size_in_bytes": 523860}, {"_path": "opt/compiler/Library/lib/libomp-itt-compiler-wrappers.obj", "path_type": "hardlink", "sha256": "2dff26ec6d404bb7df79f4cb35bd35572955fd8b36965b42c35e03438d5bf8e8", "sha256_in_prefix": "2dff26ec6d404bb7df79f4cb35bd35572955fd8b36965b42c35e03438d5bf8e8", "size_in_bytes": 15032}, {"_path": "opt/compiler/Library/lib/libomp-itt-stubs.obj", "path_type": "hardlink", "sha256": "cf6791b86235964a081fe5fe1b1aeb3d9064a2ddd36afddeb4dfd8c5206a2064", "sha256_in_prefix": "cf6791b86235964a081fe5fe1b1aeb3d9064a2ddd36afddeb4dfd8c5206a2064", "size_in_bytes": 14341}, {"_path": "opt/compiler/Library/lib/libomp-itt-user-wrappers.obj", "path_type": "hardlink", "sha256": "973c888085c46bc94c17f51ad98ac2814b979edbd71274428b6d0a38d7bc3417", "sha256_in_prefix": "973c888085c46bc94c17f51ad98ac2814b979edbd71274428b6d0a38d7bc3417", "size_in_bytes": 16802}, {"_path": "opt/compiler/Library/lib/libomp-msvc-math.obj", "path_type": "hardlink", "sha256": "6953c6f34d7e28ba04c9035e85d4bff9e424c30c20b109b25623397506fc665b", "sha256_in_prefix": "6953c6f34d7e28ba04c9035e85d4bff9e424c30c20b109b25623397506fc665b", "size_in_bytes": 44908}, {"_path": "opt/compiler/Library/lib/libomp-msvc.obj", "path_type": "hardlink", "sha256": "0ceb4545fedce8a79500e345d19447315cece9663b8569bff0d0b86f012ee418", "sha256_in_prefix": "0ceb4545fedce8a79500e345d19447315cece9663b8569bff0d0b86f012ee418", "size_in_bytes": 25277}, {"_path": "opt/compiler/Library/lib/libomp-nextgen-spirvdevicertl-fp64.obj", "path_type": "hardlink", "sha256": "7626d98c8ff809d94921f6220e74a1b966670445f3a44f141ffe89984efb11f5", "sha256_in_prefix": "7626d98c8ff809d94921f6220e74a1b966670445f3a44f141ffe89984efb11f5", "size_in_bytes": 27261}, {"_path": "opt/compiler/Library/lib/libomp-nextgen-spirvdevicertl.obj", "path_type": "hardlink", "sha256": "5eb557526be1cad5073f32370869b55cb0cb4b4ca88400ee0710fac4936ec089", "sha256_in_prefix": "5eb557526be1cad5073f32370869b55cb0cb4b4ca88400ee0710fac4936ec089", "size_in_bytes": 696169}, {"_path": "opt/compiler/Library/lib/libomp-spirvdevicertl-optional.obj", "path_type": "hardlink", "sha256": "d218e68f9fe9004ab5fd510d81ae4a1fe3ab379c9d379c95d7ed00414ba85be6", "sha256_in_prefix": "d218e68f9fe9004ab5fd510d81ae4a1fe3ab379c9d379c95d7ed00414ba85be6", "size_in_bytes": 396011}, {"_path": "opt/compiler/Library/lib/libomp-spirvdevicertl-required.obj", "path_type": "hardlink", "sha256": "531b560d93dc36b602c4bafefcbf8f6e1dc0cb879e9db53dc411be3e708fd1cb", "sha256_in_prefix": "531b560d93dc36b602c4bafefcbf8f6e1dc0cb879e9db53dc411be3e708fd1cb", "size_in_bytes": 13473}, {"_path": "opt/compiler/Library/lib/libomp-spirvdevicertl.obj", "path_type": "hardlink", "sha256": "8635b795a43da5d39d7751399f56caf06320c8839b6040b35c97a32e8cdc353c", "sha256_in_prefix": "8635b795a43da5d39d7751399f56caf06320c8839b6040b35c97a32e8cdc353c", "size_in_bytes": 401066}, {"_path": "opt/compiler/Library/lib/libomptarget-nextgen.bc", "path_type": "hardlink", "sha256": "70c8b058d7a8833f4bcb2070262b31b65e03b945277a5c8b295bc86fdca9e877", "sha256_in_prefix": "70c8b058d7a8833f4bcb2070262b31b65e03b945277a5c8b295bc86fdca9e877", "size_in_bytes": 215048}, {"_path": "opt/compiler/Library/lib/libomptarget-nextgen64.bc", "path_type": "hardlink", "sha256": "c700c4663f28b7fd2bdcd912ce81eb492bf728d100720734d5ff1a7ec88bc7d8", "sha256_in_prefix": "c700c4663f28b7fd2bdcd912ce81eb492bf728d100720734d5ff1a7ec88bc7d8", "size_in_bytes": 7936}, {"_path": "opt/compiler/Library/lib/libomptarget-opencl-optional.bc", "path_type": "hardlink", "sha256": "dcfc83b1a5e1591309ccff63ba457634638729de5be13e10a5d4f98f09303d89", "sha256_in_prefix": "dcfc83b1a5e1591309ccff63ba457634638729de5be13e10a5d4f98f09303d89", "size_in_bytes": 122032}, {"_path": "opt/compiler/Library/lib/libomptarget-opencl-required.bc", "path_type": "hardlink", "sha256": "b9bdcec76e3dbbbf3337018fce1eaf653491f0ea0e1de3323f27af01c24f73b3", "sha256_in_prefix": "b9bdcec76e3dbbbf3337018fce1eaf653491f0ea0e1de3323f27af01c24f73b3", "size_in_bytes": 3968}, {"_path": "opt/compiler/Library/lib/libomptarget-opencl.bc", "path_type": "hardlink", "sha256": "3dea196991821cd351dd9f4321598c08c2154a2182faec32f415d13ab51443d1", "sha256_in_prefix": "3dea196991821cd351dd9f4321598c08c2154a2182faec32f415d13ab51443d1", "size_in_bytes": 123440}, {"_path": "opt/compiler/include/omp-tools.h", "path_type": "hardlink", "sha256": "36722115c2d481002a265a9964cfb8057a572893a7b057686ffb6b0dc0c8576d", "sha256_in_prefix": "36722115c2d481002a265a9964cfb8057a572893a7b057686ffb6b0dc0c8576d", "size_in_bytes": 54448}, {"_path": "opt/compiler/include/omp.h", "path_type": "hardlink", "sha256": "650cc94e55a649f23abd00506caec062e2fbf43f702e51fc60031706f41e3acd", "sha256_in_prefix": "650cc94e55a649f23abd00506caec062e2fbf43f702e51fc60031706f41e3acd", "size_in_bytes": 32419}, {"_path": "opt/compiler/include/omp_lib.h", "path_type": "hardlink", "sha256": "fb6c42010faaf20738c608bade61f8998e6fd9b43ceab875c5de59b53ed985c7", "sha256_in_prefix": "fb6c42010faaf20738c608bade61f8998e6fd9b43ceab875c5de59b53ed985c7", "size_in_bytes": 64530}, {"_path": "share/doc/compiler/licensing/openmp/third-party-programs.txt", "path_type": "hardlink", "sha256": "f8ce918fe7311ce279e68380a2e233f8a42b1cd3dda8f4c48d6de97a0255c1d7", "sha256_in_prefix": "f8ce918fe7311ce279e68380a2e233f8a42b1cd3dda8f4c48d6de97a0255c1d7", "size_in_bytes": 31578}], "paths_version": 1}, "requested_spec": "None", "sha256": "0857277f8e854a43b070112b7eb280f5c8be15384c76994c7f24d81ffa6df301", "size": 22438820, "subdir": "win-64", "timestamp": 1753886268000, "url": "https://conda.anaconda.org/conda-forge/win-64/intel-openmp-2025.2.0-h57928b3_757.conda", "version": "2025.2.0"}