{"build": "12_win64_mkl", "build_number": 12, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": ["liblapack 3.9.0 12_win64_mkl", "liblapacke 3.9.0 12_win64_mkl", "blas * mkl", "mkl <2025"], "depends": ["libblas 3.9.0 12_win64_mkl"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libcblas-3.9.0-12_win64_mkl", "files": ["Library/bin/libcblas.dll"], "fn": "libcblas-3.9.0-12_win64_mkl.tar.bz2", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libcblas-3.9.0-12_win64_mkl", "type": 1}, "md5": "5103ff857d4086ae01f2df8757f31d0b", "name": "libc<PERSON>s", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libcblas-3.9.0-12_win64_mkl.tar.bz2", "paths_data": {"paths": [{"_path": "Library/bin/libcblas.dll", "path_type": "hardlink", "sha256": "0013424b25e0151a6d067179f40f6d064385bb024e26cb2ece8a86909fab9173", "sha256_in_prefix": "0013424b25e0151a6d067179f40f6d064385bb024e26cb2ece8a86909fab9173", "size_in_bytes": 20475408}], "paths_version": 1}, "requested_spec": "None", "sha256": "8dfa3148412e33bbe52bd011957df5a0d27c9c84899ea2a63870d60b559ac7fd", "size": 4704018, "subdir": "win-64", "timestamp": 1634286933000, "url": "https://conda.anaconda.org/conda-forge/win-64/libcblas-3.9.0-12_win64_mkl.tar.bz2", "version": "3.9.0"}