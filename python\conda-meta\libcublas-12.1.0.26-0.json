{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": [], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libcublas-12.1.0.26-0", "files": ["lib/x64/cublas.lib", "lib/x64/cublasLt.lib", "lib/x64/nvblas.lib"], "fn": "libcublas-12.1.0.26-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libcublas-12.1.0.26-0", "type": 1}, "md5": "99cc8d3d5d8e841c1c362f9b45e6e5a1", "name": "libcublas", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libcublas-12.1.0.26-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/cublas.lib", "path_type": "hardlink", "sha256": "cea77f343f835bfba2e58b3019ea5b2944b94347006d56da0249fd42a3957c7a", "sha256_in_prefix": "cea77f343f835bfba2e58b3019ea5b2944b94347006d56da0249fd42a3957c7a", "size_in_bytes": 153306}, {"_path": "lib/x64/cublasLt.lib", "path_type": "hardlink", "sha256": "4728a2924151eb308c48b3b6ed8e5104b0fd2680cbe81191bf3fc32c41331bf7", "sha256_in_prefix": "4728a2924151eb308c48b3b6ed8e5104b0fd2680cbe81191bf3fc32c41331bf7", "size_in_bytes": 197310}, {"_path": "lib/x64/nvblas.lib", "path_type": "hardlink", "sha256": "efaa3300ca6589006be8651d6362acb75dab73a8de6587761e85eab00bb8cc18", "sha256_in_prefix": "efaa3300ca6589006be8651d6362acb75dab73a8de6587761e85eab00bb8cc18", "size_in_bytes": 11250}], "paths_version": 1}, "requested_spec": "None", "sha256": "79dda4a6227bcd2c50cee447aa39b5be8d6451be6607bc20e265c7d271464f92", "size": 39775, "subdir": "win-64", "timestamp": 1675931395000, "url": "https://conda.anaconda.org/nvidia/win-64/libcublas-12.1.0.26-0.tar.bz2", "version": "12.1.0.26"}