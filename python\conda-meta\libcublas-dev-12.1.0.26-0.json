{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["libcublas >=12.1.0.26"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libcublas-dev-12.1.0.26-0", "files": ["bin/cublas64_12.dll", "bin/cublasLt64_12.dll", "bin/nvblas64_12.dll", "include/cublas.h", "include/cublasLt.h", "include/cublasXt.h", "include/cublas_api.h", "include/cublas_v2.h", "include/nvblas.h", "src/fortran.c", "src/fortran.h", "src/fortran_common.h", "src/fortran_thunking.c", "src/fortran_thunking.h"], "fn": "libcublas-dev-12.1.0.26-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libcublas-dev-12.1.0.26-0", "type": 1}, "md5": "fe5a9284f9120854215f322e0c7c45d6", "name": "libcublas-dev", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libcublas-dev-12.1.0.26-0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/cublas64_12.dll", "path_type": "hardlink", "sha256": "a6c51390a5d6689e916fde4d45ec32c6c2a8ba675c94df9e8b93c92b4354ed31", "sha256_in_prefix": "a6c51390a5d6689e916fde4d45ec32c6c2a8ba675c94df9e8b93c92b4354ed31", "size_in_bytes": 98052096}, {"_path": "bin/cublasLt64_12.dll", "path_type": "hardlink", "sha256": "7fddd4cf83d97e697c0fb1b6faf2cb59b009c093ab1ec8a528782a246f834494", "sha256_in_prefix": "7fddd4cf83d97e697c0fb1b6faf2cb59b009c093ab1ec8a528782a246f834494", "size_in_bytes": 489646592}, {"_path": "bin/nvblas64_12.dll", "path_type": "hardlink", "sha256": "132d581587b438d000f703edc66a34b9724fdbcc8ba3fca9a27955e8b1e74b6b", "sha256_in_prefix": "132d581587b438d000f703edc66a34b9724fdbcc8ba3fca9a27955e8b1e74b6b", "size_in_bytes": 328704}, {"_path": "include/cublas.h", "path_type": "hardlink", "sha256": "917979843ed4b042674a0df27b86c06fe1e28b2643c8e24e6709c1e0d85c8b41", "sha256_in_prefix": "917979843ed4b042674a0df27b86c06fe1e28b2643c8e24e6709c1e0d85c8b41", "size_in_bytes": 42137}, {"_path": "include/cublasLt.h", "path_type": "hardlink", "sha256": "952cff1ff9de291b2a28c348dc7cc0b8444801c69920a80ca0f66b235e9f4335", "sha256_in_prefix": "952cff1ff9de291b2a28c348dc7cc0b8444801c69920a80ca0f66b235e9f4335", "size_in_bytes": 77871}, {"_path": "include/cublasXt.h", "path_type": "hardlink", "sha256": "a51299616004afcc0ed44d3394508d4878c66d6e5fd241ba027bc17e4dcc4fb5", "sha256_in_prefix": "a51299616004afcc0ed44d3394508d4878c66d6e5fd241ba027bc17e4dcc4fb5", "size_in_bytes": 38073}, {"_path": "include/cublas_api.h", "path_type": "hardlink", "sha256": "049efa5a2cb613462e5475ff36efdbd9d7cde125e569fba75198fc4c75227ebd", "sha256_in_prefix": "049efa5a2cb613462e5475ff36efdbd9d7cde125e569fba75198fc4c75227ebd", "size_in_bytes": 370475}, {"_path": "include/cublas_v2.h", "path_type": "hardlink", "sha256": "c00d426fbc7aa24c10702492c0df2530fcf45786fc3e78832a1dccb3fba2c4ee", "sha256_in_prefix": "c00d426fbc7aa24c10702492c0df2530fcf45786fc3e78832a1dccb3fba2c4ee", "size_in_bytes": 15938}, {"_path": "include/nvblas.h", "path_type": "hardlink", "sha256": "0bebae64184148e78c709fd283616ec831f0802ca915f1951412a9185a627a0c", "sha256_in_prefix": "0bebae64184148e78c709fd283616ec831f0802ca915f1951412a9185a627a0c", "size_in_bytes": 24165}, {"_path": "src/fortran.c", "path_type": "hardlink", "sha256": "81c6bf8c73aa77e8c0289f06bc101ef22c4aab585f7d73738d37cb95987cfbc2", "sha256_in_prefix": "81c6bf8c73aa77e8c0289f06bc101ef22c4aab585f7d73738d37cb95987cfbc2", "size_in_bytes": 74264}, {"_path": "src/fortran.h", "path_type": "hardlink", "sha256": "44fd46023e1a847ae31eb0fa448cc7df3230e09801418d33e4f9cd51ae3274f3", "sha256_in_prefix": "44fd46023e1a847ae31eb0fa448cc7df3230e09801418d33e4f9cd51ae3274f3", "size_in_bytes": 48541}, {"_path": "src/fortran_common.h", "path_type": "hardlink", "sha256": "2b1ee254da2f1f2e6f775d12569129a5b17f4eaa4d97e6734282d4a7b585cfbe", "sha256_in_prefix": "2b1ee254da2f1f2e6f775d12569129a5b17f4eaa4d97e6734282d4a7b585cfbe", "size_in_bytes": 14864}, {"_path": "src/fortran_thunking.c", "path_type": "hardlink", "sha256": "3ccac40f706fa58bbf22084bf90bbb6f7c4adf839e184014520d2291b4bb42d8", "sha256_in_prefix": "3ccac40f706fa58bbf22084bf90bbb6f7c4adf839e184014520d2291b4bb42d8", "size_in_bytes": 271839}, {"_path": "src/fortran_thunking.h", "path_type": "hardlink", "sha256": "f5552098e5a16c19bf3da4bc0634deaf25a38ce2a9fba67d0e6c8f14d4d6d9ec", "sha256_in_prefix": "f5552098e5a16c19bf3da4bc0634deaf25a38ce2a9fba67d0e6c8f14d4d6d9ec", "size_in_bytes": 43062}], "paths_version": 1}, "requested_spec": "None", "sha256": "a18e37c2bd16474e9b0806aa5552db25536a8f56d911ca738e1bb7b014dc968d", "size": 365167141, "subdir": "win-64", "timestamp": 1675931579000, "url": "https://conda.anaconda.org/nvidia/win-64/libcublas-dev-12.1.0.26-0.tar.bz2", "version": "12.1.0.26"}