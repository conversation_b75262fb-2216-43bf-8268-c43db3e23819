{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": [], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libcufft-11.0.2.4-0", "files": ["lib/x64/cufft.lib", "lib/x64/cufftw.lib"], "fn": "libcufft-11.0.2.4-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libcufft-11.0.2.4-0", "type": 1}, "md5": "485535f3e422a1815a116c0b7439c233", "name": "libcu<PERSON>t", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libcufft-11.0.2.4-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/cufft.lib", "path_type": "hardlink", "sha256": "5b09ca521007d9d68f3710722385b7b2f3dd8ef544e3d570d3ab469a2f014487", "sha256_in_prefix": "5b09ca521007d9d68f3710722385b7b2f3dd8ef544e3d570d3ab469a2f014487", "size_in_bytes": 13902}, {"_path": "lib/x64/cufftw.lib", "path_type": "hardlink", "sha256": "4c18ce4f5483dca56dc2fe249e9724f98b46be0eff4da2b0e678f7ef01a47e55", "sha256_in_prefix": "4c18ce4f5483dca56dc2fe249e9724f98b46be0eff4da2b0e678f7ef01a47e55", "size_in_bytes": 17310}], "paths_version": 1}, "requested_spec": "None", "sha256": "38164059ede3560f6491a424665bed9c6f3f446c9db4b0036ef28069ef48dcb0", "size": 5944, "subdir": "win-64", "timestamp": 1674629485000, "url": "https://conda.anaconda.org/nvidia/win-64/libcufft-11.0.2.4-0.tar.bz2", "version": "11.0.2.4"}