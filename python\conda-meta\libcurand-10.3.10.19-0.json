{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["cuda-version >=12.9,<12.10.0a0", "vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libcurand-10.3.10.19-0", "files": ["Library/bin/curand64_10.dll"], "fn": "libcurand-10.3.10.19-0.conda", "license": "LicenseRef-NVIDIA-End-User-License-Agreement", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libcurand-10.3.10.19-0", "type": 1}, "md5": "3c03e7b533d3f0d974bbb51752f4d306", "name": "lib<PERSON><PERSON>", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libcurand-10.3.10.19-0.conda", "paths_data": {"paths": [{"_path": "Library/bin/curand64_10.dll", "path_type": "hardlink", "sha256": "4d6d16c9941c4f7a7e49d5c43df25d408a2a8503d8cc1eb9808e0e0c2cde36d4", "sha256_in_prefix": "4d6d16c9941c4f7a7e49d5c43df25d408a2a8503d8cc1eb9808e0e0c2cde36d4", "size_in_bytes": 79197696}], "paths_version": 1}, "requested_spec": "None", "sha256": "b05d80bab2bb5ea70c58ca7ec05e9e1d4c5f2ce115e2b19439332c429ee0ac77", "size": 48933489, "subdir": "win-64", "timestamp": 1741065478000, "url": "https://conda.anaconda.org/nvidia/win-64/libcurand-10.3.10.19-0.conda", "version": "10.3.10.19"}