{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["libcusparse >=12.0.2.55"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libcusparse-dev-12.0.2.55-0", "files": ["bin/cusparse64_12.dll", "include/cusparse.h", "include/cusparse_v2.h", "src/cusparse_fortran.c", "src/cusparse_fortran.h", "src/cusparse_fortran_common.h"], "fn": "libcusparse-dev-12.0.2.55-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libcusparse-dev-12.0.2.55-0", "type": 1}, "md5": "d1cb25ad739a0b9493f437f8f5b6ae54", "name": "libcusparse-dev", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libcusparse-dev-12.0.2.55-0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/cusparse64_12.dll", "path_type": "hardlink", "sha256": "d9961f8f75e3eaea8153af40ee2851f55798fb7ba570e7a19f8e4326c9fbb9b2", "sha256_in_prefix": "d9961f8f75e3eaea8153af40ee2851f55798fb7ba570e7a19f8e4326c9fbb9b2", "size_in_bytes": 252765184}, {"_path": "include/cusparse.h", "path_type": "hardlink", "sha256": "4f74bd787049cdfdce90c7407beb3a6c88d79de717f77c292dbb91df5a75e0f1", "sha256_in_prefix": "4f74bd787049cdfdce90c7407beb3a6c88d79de717f77c292dbb91df5a75e0f1", "size_in_bytes": 288761}, {"_path": "include/cusparse_v2.h", "path_type": "hardlink", "sha256": "a7a7078e7a404315e0b68d0834edb87094b91129ea3e683dbb8009dbfc29697f", "sha256_in_prefix": "a7a7078e7a404315e0b68d0834edb87094b91129ea3e683dbb8009dbfc29697f", "size_in_bytes": 2641}, {"_path": "src/cusparse_fortran.c", "path_type": "hardlink", "sha256": "cc1b02716cd313c6779ba4b146cacfd0b599c926d69459324c21bd3c7a5dbbfd", "sha256_in_prefix": "cc1b02716cd313c6779ba4b146cacfd0b599c926d69459324c21bd3c7a5dbbfd", "size_in_bytes": 57724}, {"_path": "src/cusparse_fortran.h", "path_type": "hardlink", "sha256": "f174ffa030a2619234571b5e4cef4ee543ad890ac4100c692f7fe4645af0473d", "sha256_in_prefix": "f174ffa030a2619234571b5e4cef4ee543ad890ac4100c692f7fe4645af0473d", "size_in_bytes": 36267}, {"_path": "src/cusparse_fortran_common.h", "path_type": "hardlink", "sha256": "5686e4f19165600a69642c51c64617830f4a4d565ec13b71c6db170295eb2150", "sha256_in_prefix": "5686e4f19165600a69642c51c64617830f4a4d565ec13b71c6db170295eb2150", "size_in_bytes": 15755}], "paths_version": 1}, "requested_spec": "None", "sha256": "2bda05db5ced35dd2877bbb55e90ea186ed2886d8cfdbc3d1f7542278211b255", "size": 170432700, "subdir": "win-64", "timestamp": 1674625429000, "url": "https://conda.anaconda.org/nvidia/win-64/libcusparse-dev-12.0.2.55-0.tar.bz2", "version": "12.0.2.55"}