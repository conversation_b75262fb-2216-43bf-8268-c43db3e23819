{"build": "12_win64_mkl", "build_number": 12, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": ["liblapacke 3.9.0 12_win64_mkl", "libcblas 3.9.0 12_win64_mkl", "blas * mkl", "mkl <2025"], "depends": ["libblas 3.9.0 12_win64_mkl"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\liblapack-3.9.0-12_win64_mkl", "files": ["Library/bin/liblapack.dll"], "fn": "liblapack-3.9.0-12_win64_mkl.tar.bz2", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\liblapack-3.9.0-12_win64_mkl", "type": 1}, "md5": "e2971c6292cb0daf16bced80b9aa767a", "name": "liblapack", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\liblapack-3.9.0-12_win64_mkl.tar.bz2", "paths_data": {"paths": [{"_path": "Library/bin/liblapack.dll", "path_type": "hardlink", "sha256": "0013424b25e0151a6d067179f40f6d064385bb024e26cb2ece8a86909fab9173", "sha256_in_prefix": "0013424b25e0151a6d067179f40f6d064385bb024e26cb2ece8a86909fab9173", "size_in_bytes": 20475408}], "paths_version": 1}, "requested_spec": "None", "sha256": "5a33e7b174c86a7650cdf4d7ebc2bfda63c79960979d04a891bc5c6a26722cd0", "size": 4704763, "subdir": "win-64", "timestamp": 1634286972000, "url": "https://conda.anaconda.org/conda-forge/win-64/liblapack-3.9.0-12_win64_mkl.tar.bz2", "version": "3.9.0"}