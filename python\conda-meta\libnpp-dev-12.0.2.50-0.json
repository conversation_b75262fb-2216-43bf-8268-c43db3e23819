{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["libnpp >=12.0.2.50"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libnpp-dev-12.0.2.50-0", "files": ["bin/nppc64_12.dll", "bin/nppial64_12.dll", "bin/nppicc64_12.dll", "bin/nppidei64_12.dll", "bin/nppif64_12.dll", "bin/nppig64_12.dll", "bin/nppim64_12.dll", "bin/nppist64_12.dll", "bin/nppisu64_12.dll", "bin/nppitc64_12.dll", "bin/npps64_12.dll", "include/npp.h", "include/nppcore.h", "include/nppdefs.h", "include/nppi.h", "include/nppi_arithmetic_and_logical_operations.h", "include/nppi_color_conversion.h", "include/nppi_data_exchange_and_initialization.h", "include/nppi_filtering_functions.h", "include/nppi_geometry_transforms.h", "include/nppi_linear_transforms.h", "include/nppi_morphological_operations.h", "include/nppi_statistics_functions.h", "include/nppi_support_functions.h", "include/nppi_threshold_and_compare_operations.h", "include/npps.h", "include/npps_arithmetic_and_logical_operations.h", "include/npps_conversion_functions.h", "include/npps_filtering_functions.h", "include/npps_initialization.h", "include/npps_statistics_functions.h", "include/npps_support_functions.h"], "fn": "libnpp-dev-12.0.2.50-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libnpp-dev-12.0.2.50-0", "type": 1}, "md5": "1ad0362bb5522bbbf5d9f6b7b95806ce", "name": "libnpp-dev", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libnpp-dev-12.0.2.50-0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/nppc64_12.dll", "path_type": "hardlink", "sha256": "953fd584809882ae7f08c9392ced514208688faca70f0416b9a8987dc12b2531", "sha256_in_prefix": "953fd584809882ae7f08c9392ced514208688faca70f0416b9a8987dc12b2531", "size_in_bytes": 288256}, {"_path": "bin/nppial64_12.dll", "path_type": "hardlink", "sha256": "6119e0ac25dfb796098d9eaa0e23eaac7402b0a12fc84674c301c796cca4eaab", "sha256_in_prefix": "6119e0ac25dfb796098d9eaa0e23eaac7402b0a12fc84674c301c796cca4eaab", "size_in_bytes": 14873600}, {"_path": "bin/nppicc64_12.dll", "path_type": "hardlink", "sha256": "c34a9f9d7d9d86daebb63f88e0559c5b83ba91f7689d4fd681d7240c95109e2b", "sha256_in_prefix": "c34a9f9d7d9d86daebb63f88e0559c5b83ba91f7689d4fd681d7240c95109e2b", "size_in_bytes": 5421056}, {"_path": "bin/nppidei64_12.dll", "path_type": "hardlink", "sha256": "3e35b1cafde4870ce5f679fde6f2708b22f6148b69efabe4297f67b1e113e998", "sha256_in_prefix": "3e35b1cafde4870ce5f679fde6f2708b22f6148b69efabe4297f67b1e113e998", "size_in_bytes": 9459712}, {"_path": "bin/nppif64_12.dll", "path_type": "hardlink", "sha256": "6913c9fb7256258bab9e9125cde03cd8b235725020f397114b1406118a2ee738", "sha256_in_prefix": "6913c9fb7256258bab9e9125cde03cd8b235725020f397114b1406118a2ee738", "size_in_bytes": 94409728}, {"_path": "bin/nppig64_12.dll", "path_type": "hardlink", "sha256": "3f555241287da26a06dbbf3d3cb39708ecb0f3e56f5e50e420f4d3548ac224ba", "sha256_in_prefix": "3f555241287da26a06dbbf3d3cb39708ecb0f3e56f5e50e420f4d3548ac224ba", "size_in_bytes": 37711360}, {"_path": "bin/nppim64_12.dll", "path_type": "hardlink", "sha256": "8b82f87eac7c9ff5b6104883bba2e52b4f7bb08f16c0e1c4d8cc891c68889cc0", "sha256_in_prefix": "8b82f87eac7c9ff5b6104883bba2e52b4f7bb08f16c0e1c4d8cc891c68889cc0", "size_in_bytes": 7943680}, {"_path": "bin/nppist64_12.dll", "path_type": "hardlink", "sha256": "ee4503f41b169e04d875f22bec31a19148d51504e08449a692bce421b0cc2eb4", "sha256_in_prefix": "ee4503f41b169e04d875f22bec31a19148d51504e08449a692bce421b0cc2eb4", "size_in_bytes": 37652480}, {"_path": "bin/nppisu64_12.dll", "path_type": "hardlink", "sha256": "a9811aad3eeafab9756d23ba4713bc531f1f862b89ffcba1ee517f3e38247480", "sha256_in_prefix": "a9811aad3eeafab9756d23ba4713bc531f1f862b89ffcba1ee517f3e38247480", "size_in_bytes": 257536}, {"_path": "bin/nppitc64_12.dll", "path_type": "hardlink", "sha256": "7fcbb2e7fd2084d7979e45d0cfb8f103f54bf8b4e2b31fae6bb213f805e104ea", "sha256_in_prefix": "7fcbb2e7fd2084d7979e45d0cfb8f103f54bf8b4e2b31fae6bb213f805e104ea", "size_in_bytes": 4066304}, {"_path": "bin/npps64_12.dll", "path_type": "hardlink", "sha256": "77d1fff62ccb385d1bdf8cd473a3564785c8100321546ada65ac97bbfedeefc3", "sha256_in_prefix": "77d1fff62ccb385d1bdf8cd473a3564785c8100321546ada65ac97bbfedeefc3", "size_in_bytes": 17686016}, {"_path": "include/npp.h", "path_type": "hardlink", "sha256": "e58a417e65760fc21313a457c798c304921b3d227d8dfbf8bafc519fca197df4", "sha256_in_prefix": "e58a417e65760fc21313a457c798c304921b3d227d8dfbf8bafc519fca197df4", "size_in_bytes": 3280}, {"_path": "include/nppcore.h", "path_type": "hardlink", "sha256": "e2a03b1991a02aad75d293b70c0ae82ccede23258e968b6af0724b0da1dadc54", "sha256_in_prefix": "e2a03b1991a02aad75d293b70c0ae82ccede23258e968b6af0724b0da1dadc54", "size_in_bytes": 7599}, {"_path": "include/nppdefs.h", "path_type": "hardlink", "sha256": "e9cdb31fe374192557d08f0ac911267a5cf9cfa6e29dd4eca7887b4151f7e088", "sha256_in_prefix": "e9cdb31fe374192557d08f0ac911267a5cf9cfa6e29dd4eca7887b4151f7e088", "size_in_bytes": 33331}, {"_path": "include/nppi.h", "path_type": "hardlink", "sha256": "c2ee3d052f2ceca1396426c39b8f66ff2f0a92c7f6364feada9878c148b7a8ac", "sha256_in_prefix": "c2ee3d052f2ceca1396426c39b8f66ff2f0a92c7f6364feada9878c148b7a8ac", "size_in_bytes": 4181}, {"_path": "include/nppi_arithmetic_and_logical_operations.h", "path_type": "hardlink", "sha256": "f4b9e8e118e217d3818fd87a8394c585d0a5c51213cd2ed4f1fbf16578f5cacf", "sha256_in_prefix": "f4b9e8e118e217d3818fd87a8394c585d0a5c51213cd2ed4f1fbf16578f5cacf", "size_in_bytes": 1026693}, {"_path": "include/nppi_color_conversion.h", "path_type": "hardlink", "sha256": "ae8ea7b8ebaa78594036b7326637cea4f2ade36284c3497431a06c449a0cd6d5", "sha256_in_prefix": "ae8ea7b8ebaa78594036b7326637cea4f2ade36284c3497431a06c449a0cd6d5", "size_in_bytes": 611742}, {"_path": "include/nppi_data_exchange_and_initialization.h", "path_type": "hardlink", "sha256": "b8ed1ac0632b99337dce67706087f22bafea97040815888770bc9ddd9de5449f", "sha256_in_prefix": "b8ed1ac0632b99337dce67706087f22bafea97040815888770bc9ddd9de5449f", "size_in_bytes": 336102}, {"_path": "include/nppi_filtering_functions.h", "path_type": "hardlink", "sha256": "b99db9c4b3394135d278d1dbc4384686cfca4da6690872292c739074a39686e5", "sha256_in_prefix": "b99db9c4b3394135d278d1dbc4384686cfca4da6690872292c739074a39686e5", "size_in_bytes": 1048591}, {"_path": "include/nppi_geometry_transforms.h", "path_type": "hardlink", "sha256": "806a232321a8527930c932fa7bd799bf0f0d2d6cff85834d2a6cb61f7b8439d5", "sha256_in_prefix": "806a232321a8527930c932fa7bd799bf0f0d2d6cff85834d2a6cb61f7b8439d5", "size_in_bytes": 337976}, {"_path": "include/nppi_linear_transforms.h", "path_type": "hardlink", "sha256": "059563d3fe3b9beee4515477fb99d8c4023e855720dcf0aae9144cf31373bcfa", "sha256_in_prefix": "059563d3fe3b9beee4515477fb99d8c4023e855720dcf0aae9144cf31373bcfa", "size_in_bytes": 6084}, {"_path": "include/nppi_morphological_operations.h", "path_type": "hardlink", "sha256": "65f4ec64bf0ae54b5e5406c59c82cef811b3df336eb40d4eda25c2b3e8401484", "sha256_in_prefix": "65f4ec64bf0ae54b5e5406c59c82cef811b3df336eb40d4eda25c2b3e8401484", "size_in_bytes": 133623}, {"_path": "include/nppi_statistics_functions.h", "path_type": "hardlink", "sha256": "367869fa6748a837f00dd02e8730cd349af334544f9a7d428f7d37752900a70d", "sha256_in_prefix": "367869fa6748a837f00dd02e8730cd349af334544f9a7d428f7d37752900a70d", "size_in_bytes": 1047423}, {"_path": "include/nppi_support_functions.h", "path_type": "hardlink", "sha256": "9fff8477dfd526535ae89b70058e9ddc81af07ee1f15a4b6700d0b962f5188ba", "sha256_in_prefix": "9fff8477dfd526535ae89b70058e9ddc81af07ee1f15a4b6700d0b962f5188ba", "size_in_bytes": 13988}, {"_path": "include/nppi_threshold_and_compare_operations.h", "path_type": "hardlink", "sha256": "917e67b3c8abf919591b9d8cedbb21d31bb368af167e4f6bb125c1593569a1ee", "sha256_in_prefix": "917e67b3c8abf919591b9d8cedbb21d31bb368af167e4f6bb125c1593569a1ee", "size_in_bytes": 221749}, {"_path": "include/npps.h", "path_type": "hardlink", "sha256": "4c1a5bc08059fd8e76c29c8890c40d17158c8226fe054fcf0fdfcbb49251ae6e", "sha256_in_prefix": "4c1a5bc08059fd8e76c29c8890c40d17158c8226fe054fcf0fdfcbb49251ae6e", "size_in_bytes": 3772}, {"_path": "include/npps_arithmetic_and_logical_operations.h", "path_type": "hardlink", "sha256": "bb7e3f4f5b77ea2f0b2875f7268db9ebb32737725d388d0affa543728f60c7be", "sha256_in_prefix": "bb7e3f4f5b77ea2f0b2875f7268db9ebb32737725d388d0affa543728f60c7be", "size_in_bytes": 255096}, {"_path": "include/npps_conversion_functions.h", "path_type": "hardlink", "sha256": "c3acd0edd779b96ecf4dee1e3874811dd5959d97ae264d52cbc78ad1d7e09b5b", "sha256_in_prefix": "c3acd0edd779b96ecf4dee1e3874811dd5959d97ae264d52cbc78ad1d7e09b5b", "size_in_bytes": 55454}, {"_path": "include/npps_filtering_functions.h", "path_type": "hardlink", "sha256": "31d1ddf09a29998b554e1417ace78206451ac267c6f4b6d6c9933284d92f5d86", "sha256_in_prefix": "31d1ddf09a29998b554e1417ace78206451ac267c6f4b6d6c9933284d92f5d86", "size_in_bytes": 3820}, {"_path": "include/npps_initialization.h", "path_type": "hardlink", "sha256": "ed06f115fae51d1bc6589fcfd50583336aa2972a9b3aeedd5272f293d499d8a6", "sha256_in_prefix": "ed06f115fae51d1bc6589fcfd50583336aa2972a9b3aeedd5272f293d499d8a6", "size_in_bytes": 21695}, {"_path": "include/npps_statistics_functions.h", "path_type": "hardlink", "sha256": "3d6f77b6c87216492724cfd83bf9475509ba0a385afaa133872f64e98bd80396", "sha256_in_prefix": "3d6f77b6c87216492724cfd83bf9475509ba0a385afaa133872f64e98bd80396", "size_in_bytes": 285853}, {"_path": "include/npps_support_functions.h", "path_type": "hardlink", "sha256": "a708bf0cd8ec8ef1ff5444e494adb933291d6dcf498a689e0280c056108dcbac", "sha256_in_prefix": "a708bf0cd8ec8ef1ff5444e494adb933291d6dcf498a689e0280c056108dcbac", "size_in_bytes": 8116}], "paths_version": 1}, "requested_spec": "None", "sha256": "d75ef160b101440b5488bb028efbb9d6a99cc4b96c370e1ed79fc65c5fd3742c", "size": 142227817, "subdir": "win-64", "timestamp": 1675319959000, "url": "https://conda.anaconda.org/nvidia/win-64/libnpp-dev-12.0.2.50-0.tar.bz2", "version": "12.0.2.50"}