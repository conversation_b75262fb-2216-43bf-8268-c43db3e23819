{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": [], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjitlink-12.1.105-0", "files": ["lib/x64/nvJitLink.lib", "lib/x64/nvJitLink_static.lib"], "fn": "libnvjitlink-12.1.105-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjitlink-12.1.105-0", "type": 1}, "md5": "5d22c3eb256239d3d7bdd6d1b0692e81", "name": "libnvjitlink", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjitlink-12.1.105-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/nvJitLink.lib", "path_type": "hardlink", "sha256": "69349204a2dfaf76bc53d5fa460982125985ea0525ed4237c0e9d14b70bca656", "sha256_in_prefix": "69349204a2dfaf76bc53d5fa460982125985ea0525ed4237c0e9d14b70bca656", "size_in_bytes": 8708}, {"_path": "lib/x64/nvJitLink_static.lib", "path_type": "hardlink", "sha256": "c9293d67e7da75db0f20e086498a262307c6e9e7871173a5af1d2f49f1e23add", "sha256_in_prefix": "c9293d67e7da75db0f20e086498a262307c6e9e7871173a5af1d2f49f1e23add", "size_in_bytes": 309313046}], "paths_version": 1}, "requested_spec": "None", "sha256": "5f36301f850daf71fc1e37b49e3ba8ae164a7de5fe808171954f135cc8b0c7e1", "size": 70602454, "subdir": "win-64", "timestamp": 1680572479000, "url": "https://conda.anaconda.org/nvidia/win-64/libnvjitlink-12.1.105-0.tar.bz2", "version": "12.1.105"}