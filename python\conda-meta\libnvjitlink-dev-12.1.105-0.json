{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["libnvjitlink >=12.1.105"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjitlink-dev-12.1.105-0", "files": ["bin/nvJitLink_120_0.dll", "include/nvJitLink.h", "res/nvJitLink_win32.def"], "fn": "libnvjitlink-dev-12.1.105-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjitlink-dev-12.1.105-0", "type": 1}, "md5": "da87b6fe9a2b714502473df50f1e01ec", "name": "libnvjitlink-dev", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjitlink-dev-12.1.105-0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/nvJitLink_120_0.dll", "path_type": "hardlink", "sha256": "33c6abfe81cad9751f072f5766b29eeb2e4019fb33db82ffaac08f01160627a2", "sha256_in_prefix": "33c6abfe81cad9751f072f5766b29eeb2e4019fb33db82ffaac08f01160627a2", "size_in_bytes": 34385920}, {"_path": "include/nvJitLink.h", "path_type": "hardlink", "sha256": "72c8ba357f419b7a8695238b6ad7fced7fe2a7d4733dd3aee92f277658203cd8", "sha256_in_prefix": "72c8ba357f419b7a8695238b6ad7fced7fe2a7d4733dd3aee92f277658203cd8", "size_in_bytes": 14104}, {"_path": "res/nvJitLink_win32.def", "path_type": "hardlink", "sha256": "b783d4a6785ff41365fe4bd7ef226adcbd55606db71c71bdc29febbf5d00652c", "sha256_in_prefix": "b783d4a6785ff41365fe4bd7ef226adcbd55606db71c71bdc29febbf5d00652c", "size_in_bytes": 769}], "paths_version": 1}, "requested_spec": "None", "sha256": "fc4c7db8f01bea092b6427c62ad38070827b40c8e14cd573928d41c9be725459", "size": 14448601, "subdir": "win-64", "timestamp": 1680572605000, "url": "https://conda.anaconda.org/nvidia/win-64/libnvjitlink-dev-12.1.105-0.tar.bz2", "version": "12.1.105"}