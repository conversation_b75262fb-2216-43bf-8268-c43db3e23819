{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": [], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjpeg-12.1.1.14-0", "files": ["lib/x64/nvjpeg.lib"], "fn": "libnvjpeg-12.1.1.14-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjpeg-12.1.1.14-0", "type": 1}, "md5": "2b6dd43dd132b06d4cc5953be5c25ae7", "name": "libnvjpeg", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjpeg-12.1.1.14-0.tar.bz2", "paths_data": {"paths": [{"_path": "lib/x64/nvjpeg.lib", "path_type": "hardlink", "sha256": "1eb3735d8778acfa61259f4023b9815b1f81a67592030b983039c04d884fcb92", "sha256_in_prefix": "1eb3735d8778acfa61259f4023b9815b1f81a67592030b983039c04d884fcb92", "size_in_bytes": 21530}], "paths_version": 1}, "requested_spec": "None", "sha256": "a170efce680edff337f12c34fe32d30bf9dfea6c64c2e7513eacdface8e44032", "size": 4819, "subdir": "win-64", "timestamp": 1682912784000, "url": "https://conda.anaconda.org/nvidia/win-64/libnvjpeg-12.1.1.14-0.tar.bz2", "version": "12.1.1.14"}