{"build": "0", "build_number": 0, "channel": "https://conda.anaconda.org/nvidia/win-64", "constrains": [], "depends": ["libnvjpeg >=12.1.1.14"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjpeg-dev-12.1.1.14-0", "files": ["bin/nvjpeg64_12.dll", "include/nvjpeg.h"], "fn": "libnvjpeg-dev-12.1.1.14-0.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjpeg-dev-12.1.1.14-0", "type": 1}, "md5": "91f433f954b60c6c011a3754d8ffbc00", "name": "libnvjpeg-dev", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\libnvjpeg-dev-12.1.1.14-0.tar.bz2", "paths_data": {"paths": [{"_path": "bin/nvjpeg64_12.dll", "path_type": "hardlink", "sha256": "d199944df38220e226004ae311c4a136231247b6b81ef4a487ceb7f621225908", "sha256_in_prefix": "d199944df38220e226004ae311c4a136231247b6b81ef4a487ceb7f621225908", "size_in_bytes": 4799488}, {"_path": "include/nvjpeg.h", "path_type": "hardlink", "sha256": "ff427bafd4e4fee7bdd0ad9b6bcaa6730b48dfb37638f583f727cc33dd777473", "sha256_in_prefix": "ff427bafd4e4fee7bdd0ad9b6bcaa6730b48dfb37638f583f727cc33dd777473", "size_in_bytes": 34754}], "paths_version": 1}, "requested_spec": "None", "sha256": "bd8ede1faa096b2721bc1b10f116c2fbcc347e341f9a1b0dbe317b5c373bfdee", "size": 2539554, "subdir": "win-64", "timestamp": 1682912884000, "url": "https://conda.anaconda.org/nvidia/win-64/libnvjpeg-dev-12.1.1.14-0.tar.bz2", "version": "12.1.1.14"}