{"build": "h0e2418a_729", "build_number": 729, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["intel-openmp", "tbb 2021.*"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\mkl-2021.4.0-h0e2418a_729", "files": ["Library/bin/libimalloc.dll", "Library/bin/mkl_avx.1.dll", "Library/bin/mkl_avx2.1.dll", "Library/bin/mkl_avx512.1.dll", "Library/bin/mkl_blacs_ilp64.1.dll", "Library/bin/mkl_blacs_intelmpi_ilp64.1.dll", "Library/bin/mkl_blacs_intelmpi_lp64.1.dll", "Library/bin/mkl_blacs_lp64.1.dll", "Library/bin/mkl_blacs_mpich2_ilp64.1.dll", "Library/bin/mkl_blacs_mpich2_lp64.1.dll", "Library/bin/mkl_blacs_msmpi_ilp64.1.dll", "Library/bin/mkl_blacs_msmpi_lp64.1.dll", "Library/bin/mkl_cdft_core.1.dll", "Library/bin/mkl_core.1.dll", "Library/bin/mkl_def.1.dll", "Library/bin/mkl_intel_thread.1.dll", "Library/bin/mkl_mc.1.dll", "Library/bin/mkl_mc3.1.dll", "Library/bin/mkl_msg.dll", "Library/bin/mkl_pgi_thread.1.dll", "Library/bin/mkl_rt.1.dll", "Library/bin/mkl_scalapack_ilp64.1.dll", "Library/bin/mkl_scalapack_lp64.1.dll", "Library/bin/mkl_sequential.1.dll", "Library/bin/mkl_tbb_thread.1.dll", "Library/bin/mkl_vml_avx.1.dll", "Library/bin/mkl_vml_avx2.1.dll", "Library/bin/mkl_vml_avx512.1.dll", "Library/bin/mkl_vml_cmpt.1.dll", "Library/bin/mkl_vml_def.1.dll", "Library/bin/mkl_vml_mc.1.dll", "Library/bin/mkl_vml_mc2.1.dll", "Library/bin/mkl_vml_mc3.1.dll"], "fn": "mkl-2021.4.0-h0e2418a_729.tar.bz2", "license": "LicenseRef-ProprietaryIntel", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\mkl-2021.4.0-h0e2418a_729", "type": 1}, "md5": "42fcb45077a716cb8d967117b8b88f28", "name": "mkl", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\mkl-2021.4.0-h0e2418a_729.tar.bz2", "paths_data": {"paths": [{"_path": "Library/bin/libimalloc.dll", "path_type": "hardlink", "sha256": "3e43ca1c2358f91bec50e97a776386db7b6743598c49ae1b39468a1e3b5c53c0", "sha256_in_prefix": "3e43ca1c2358f91bec50e97a776386db7b6743598c49ae1b39468a1e3b5c53c0", "size_in_bytes": 12304}, {"_path": "Library/bin/mkl_avx.1.dll", "path_type": "hardlink", "sha256": "e00683b08c00a1aea6a88978cc101430d193d112c87d115b62af5d2a4f970437", "sha256_in_prefix": "e00683b08c00a1aea6a88978cc101430d193d112c87d115b62af5d2a4f970437", "size_in_bytes": 45634576}, {"_path": "Library/bin/mkl_avx2.1.dll", "path_type": "hardlink", "sha256": "e28cb7ce72cf8cc5c4a645ba1cc8b4c389b1129cc4d7548a952cbb415fd6b001", "sha256_in_prefix": "e28cb7ce72cf8cc5c4a645ba1cc8b4c389b1129cc4d7548a952cbb415fd6b001", "size_in_bytes": 43432464}, {"_path": "Library/bin/mkl_avx512.1.dll", "path_type": "hardlink", "sha256": "54f51ef4a51d1bbc1fe6c70772692b3ad4a71e75bfde4765e4e1d4c627355d02", "sha256_in_prefix": "54f51ef4a51d1bbc1fe6c70772692b3ad4a71e75bfde4765e4e1d4c627355d02", "size_in_bytes": 58468368}, {"_path": "Library/bin/mkl_blacs_ilp64.1.dll", "path_type": "hardlink", "sha256": "da1a67cddf09ca8395b43b9524b8c192a6b8d3005c0727abdafba2da024715ba", "sha256_in_prefix": "da1a67cddf09ca8395b43b9524b8c192a6b8d3005c0727abdafba2da024715ba", "size_in_bytes": 561168}, {"_path": "Library/bin/mkl_blacs_intelmpi_ilp64.1.dll", "path_type": "hardlink", "sha256": "3714b1f83a55479a4d88f2d07ffca6840535973ab0d5d51a667a5a553ab2b9d0", "sha256_in_prefix": "3714b1f83a55479a4d88f2d07ffca6840535973ab0d5d51a667a5a553ab2b9d0", "size_in_bytes": 133648}, {"_path": "Library/bin/mkl_blacs_intelmpi_lp64.1.dll", "path_type": "hardlink", "sha256": "193395b180ca86ac7a2511954c7c88e16b25297e88ea1577d5ce1726c305cf4b", "sha256_in_prefix": "193395b180ca86ac7a2511954c7c88e16b25297e88ea1577d5ce1726c305cf4b", "size_in_bytes": 133648}, {"_path": "Library/bin/mkl_blacs_lp64.1.dll", "path_type": "hardlink", "sha256": "31cd652079cd7dd21446d6c0a4aa8cb49c53443de8759ebec22a184344e1f4c1", "sha256_in_prefix": "31cd652079cd7dd21446d6c0a4aa8cb49c53443de8759ebec22a184344e1f4c1", "size_in_bytes": 313872}, {"_path": "Library/bin/mkl_blacs_mpich2_ilp64.1.dll", "path_type": "hardlink", "sha256": "5abcf3b3bdf30d1eeab7efe1e94fa89cd6c96ecbe9297a4815754fbbce737ad6", "sha256_in_prefix": "5abcf3b3bdf30d1eeab7efe1e94fa89cd6c96ecbe9297a4815754fbbce737ad6", "size_in_bytes": 133648}, {"_path": "Library/bin/mkl_blacs_mpich2_lp64.1.dll", "path_type": "hardlink", "sha256": "155b3ea1ea2b4de065860deab96422a1d26d88146f99276aeeffef742fd8ed40", "sha256_in_prefix": "155b3ea1ea2b4de065860deab96422a1d26d88146f99276aeeffef742fd8ed40", "size_in_bytes": 133648}, {"_path": "Library/bin/mkl_blacs_msmpi_ilp64.1.dll", "path_type": "hardlink", "sha256": "943ebc6c074bf5fd207d68918226d482310ec18d19d83f7db66e310a56a2c39f", "sha256_in_prefix": "943ebc6c074bf5fd207d68918226d482310ec18d19d83f7db66e310a56a2c39f", "size_in_bytes": 133648}, {"_path": "Library/bin/mkl_blacs_msmpi_lp64.1.dll", "path_type": "hardlink", "sha256": "86b7ad1a9db0d5c1adbb0511df161a736793161313ac2538226e70da4c3e810c", "sha256_in_prefix": "86b7ad1a9db0d5c1adbb0511df161a736793161313ac2538226e70da4c3e810c", "size_in_bytes": 133648}, {"_path": "Library/bin/mkl_cdft_core.1.dll", "path_type": "hardlink", "sha256": "3752cfb70e52fe8994ca03df14a7a0d25d851af608e31784949e09ba97737158", "sha256_in_prefix": "3752cfb70e52fe8994ca03df14a7a0d25d851af608e31784949e09ba97737158", "size_in_bytes": 392208}, {"_path": "Library/bin/mkl_core.1.dll", "path_type": "hardlink", "sha256": "53a8f1818fc3436831094798ae18acf4d499440a8aeb54aa535b827a850a427b", "sha256_in_prefix": "53a8f1818fc3436831094798ae18acf4d499440a8aeb54aa535b827a850a427b", "size_in_bytes": 78281744}, {"_path": "Library/bin/mkl_def.1.dll", "path_type": "hardlink", "sha256": "510ca669317c4bfb48f46b6838cfca2569b54bd4fc536aaae1aa8f8e28d98f48", "sha256_in_prefix": "510ca669317c4bfb48f46b6838cfca2569b54bd4fc536aaae1aa8f8e28d98f48", "size_in_bytes": 39061520}, {"_path": "Library/bin/mkl_intel_thread.1.dll", "path_type": "hardlink", "sha256": "a9216f24fe05a0d9a65a825d9a4f8e1a16bc24043cc2a004b6838b5a198a611a", "sha256_in_prefix": "a9216f24fe05a0d9a65a825d9a4f8e1a16bc24043cc2a004b6838b5a198a611a", "size_in_bytes": 54702096}, {"_path": "Library/bin/mkl_mc.1.dll", "path_type": "hardlink", "sha256": "6dfefc5acba8dba948ed06e0f3afd385e804bd109174b56b89ce89c09284ede7", "sha256_in_prefix": "6dfefc5acba8dba948ed06e0f3afd385e804bd109174b56b89ce89c09284ede7", "size_in_bytes": 42946064}, {"_path": "Library/bin/mkl_mc3.1.dll", "path_type": "hardlink", "sha256": "6520be406debfabc3ecce78d3c89701c2b2ce5c1b78d3d19349f8b17005a6171", "sha256_in_prefix": "6520be406debfabc3ecce78d3c89701c2b2ce5c1b78d3d19349f8b17005a6171", "size_in_bytes": 44276752}, {"_path": "Library/bin/mkl_msg.dll", "path_type": "hardlink", "sha256": "f4a89652de3f701c04bbc2097c977cbd4d451652848f103f732119c18003ed3c", "sha256_in_prefix": "f4a89652de3f701c04bbc2097c977cbd4d451652848f103f732119c18003ed3c", "size_in_bytes": 192528}, {"_path": "Library/bin/mkl_pgi_thread.1.dll", "path_type": "hardlink", "sha256": "fc725c59e4026c0f8451b9db7e3a6e3be7783fdcaa2801586ef1c39f185f52a6", "sha256_in_prefix": "fc725c59e4026c0f8451b9db7e3a6e3be7783fdcaa2801586ef1c39f185f52a6", "size_in_bytes": 53484560}, {"_path": "Library/bin/mkl_rt.1.dll", "path_type": "hardlink", "sha256": "0013424b25e0151a6d067179f40f6d064385bb024e26cb2ece8a86909fab9173", "sha256_in_prefix": "0013424b25e0151a6d067179f40f6d064385bb024e26cb2ece8a86909fab9173", "size_in_bytes": 20475408}, {"_path": "Library/bin/mkl_scalapack_ilp64.1.dll", "path_type": "hardlink", "sha256": "40411bc50183ebbc5a950c7533ef52d59ee3cf501a29ce9747ad7c293bb2ad7e", "sha256_in_prefix": "40411bc50183ebbc5a950c7533ef52d59ee3cf501a29ce9747ad7c293bb2ad7e", "size_in_bytes": 7733776}, {"_path": "Library/bin/mkl_scalapack_lp64.1.dll", "path_type": "hardlink", "sha256": "ab2247e718a09e8c185c0e4005d20b6f1510483c64c46c042eea11cc176b56e0", "sha256_in_prefix": "ab2247e718a09e8c185c0e4005d20b6f1510483c64c46c042eea11cc176b56e0", "size_in_bytes": 7687696}, {"_path": "Library/bin/mkl_sequential.1.dll", "path_type": "hardlink", "sha256": "00c7ce3f0088ce099651dc43d9df001f82dbfd959a0f7a85c807404a89442d7b", "sha256_in_prefix": "00c7ce3f0088ce099651dc43d9df001f82dbfd959a0f7a85c807404a89442d7b", "size_in_bytes": 25593360}, {"_path": "Library/bin/mkl_tbb_thread.1.dll", "path_type": "hardlink", "sha256": "740f55a036742d53821e56ffc2459b953cc0b14847e19c4ecf552dd29dd87111", "sha256_in_prefix": "740f55a036742d53821e56ffc2459b953cc0b14847e19c4ecf552dd29dd87111", "size_in_bytes": 31441936}, {"_path": "Library/bin/mkl_vml_avx.1.dll", "path_type": "hardlink", "sha256": "325a519dadafc2df3e5a28be7fb2e4c9536211fc4dd3bd9662f7223078cff9ed", "sha256_in_prefix": "325a519dadafc2df3e5a28be7fb2e4c9536211fc4dd3bd9662f7223078cff9ed", "size_in_bytes": 16532496}, {"_path": "Library/bin/mkl_vml_avx2.1.dll", "path_type": "hardlink", "sha256": "1ebde1d5a5304a7524316f81e83b44606cf04a78fe6d2fad9c04c79a3d5fb0f4", "sha256_in_prefix": "1ebde1d5a5304a7524316f81e83b44606cf04a78fe6d2fad9c04c79a3d5fb0f4", "size_in_bytes": 15548944}, {"_path": "Library/bin/mkl_vml_avx512.1.dll", "path_type": "hardlink", "sha256": "b2bc2e474444044c03b5172fc9ae37781d47f4f627651251c0ed3e1bc3546f9f", "sha256_in_prefix": "b2bc2e474444044c03b5172fc9ae37781d47f4f627651251c0ed3e1bc3546f9f", "size_in_bytes": 14653456}, {"_path": "Library/bin/mkl_vml_cmpt.1.dll", "path_type": "hardlink", "sha256": "a1b0f88217e957be42e65b8e318102d5ab84e589aeb1be26ef46f3c586beed27", "sha256_in_prefix": "a1b0f88217e957be42e65b8e318102d5ab84e589aeb1be26ef46f3c586beed27", "size_in_bytes": 8012304}, {"_path": "Library/bin/mkl_vml_def.1.dll", "path_type": "hardlink", "sha256": "d975f07862ad63d4b6a394c760b5ad662eda0dac5284ef96846d81b6c9ec6e98", "sha256_in_prefix": "d975f07862ad63d4b6a394c760b5ad662eda0dac5284ef96846d81b6c9ec6e98", "size_in_bytes": 8945168}, {"_path": "Library/bin/mkl_vml_mc.1.dll", "path_type": "hardlink", "sha256": "edd3bd1957a73ac5b6fd1992843f41cb976df1952aa73d0ad67a893e156280fd", "sha256_in_prefix": "edd3bd1957a73ac5b6fd1992843f41cb976df1952aa73d0ad67a893e156280fd", "size_in_bytes": 15631376}, {"_path": "Library/bin/mkl_vml_mc2.1.dll", "path_type": "hardlink", "sha256": "a2f684dcb5052fc12dcdcd70eb4c027d1c276ebaff47a7c73252d762c01be622", "sha256_in_prefix": "a2f684dcb5052fc12dcdcd70eb4c027d1c276ebaff47a7c73252d762c01be622", "size_in_bytes": 15349264}, {"_path": "Library/bin/mkl_vml_mc3.1.dll", "path_type": "hardlink", "sha256": "1415152068fd300a04ad77bcb7ebffde49cfea5ec22dfd66a7db0366ed4b22a2", "sha256_in_prefix": "1415152068fd300a04ad77bcb7ebffde49cfea5ec22dfd66a7db0366ed4b22a2", "size_in_bytes": 15371280}], "paths_version": 1}, "requested_spec": "None", "sha256": "176f7a7a85f0ab9aa088ac2f7ea4c71af1136d8848577679c07601500f02fe6e", "size": 190533975, "subdir": "win-64", "timestamp": 1634135963000, "url": "https://conda.anaconda.org/conda-forge/win-64/mkl-2021.4.0-h0e2418a_729.tar.bz2", "version": "2021.4.0"}