{"build": "pyhd8ed1ab_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\mpmath-1.3.0-pyhd8ed1ab_1", "files": ["Lib/site-packages/mpmath-1.3.0.dist-info/INSTALLER", "Lib/site-packages/mpmath-1.3.0.dist-info/LICENSE", "Lib/site-packages/mpmath-1.3.0.dist-info/METADATA", "Lib/site-packages/mpmath-1.3.0.dist-info/RECORD", "Lib/site-packages/mpmath-1.3.0.dist-info/REQUESTED", "Lib/site-packages/mpmath-1.3.0.dist-info/WHEEL", "Lib/site-packages/mpmath-1.3.0.dist-info/direct_url.json", "Lib/site-packages/mpmath-1.3.0.dist-info/top_level.txt", "Lib/site-packages/mpmath/__init__.py", "Lib/site-packages/mpmath/calculus/__init__.py", "Lib/site-packages/mpmath/calculus/approximation.py", "Lib/site-packages/mpmath/calculus/calculus.py", "Lib/site-packages/mpmath/calculus/differentiation.py", "Lib/site-packages/mpmath/calculus/extrapolation.py", "Lib/site-packages/mpmath/calculus/inverselaplace.py", "Lib/site-packages/mpmath/calculus/odes.py", "Lib/site-packages/mpmath/calculus/optimization.py", "Lib/site-packages/mpmath/calculus/polynomials.py", "Lib/site-packages/mpmath/calculus/quadrature.py", "Lib/site-packages/mpmath/ctx_base.py", "Lib/site-packages/mpmath/ctx_fp.py", "Lib/site-packages/mpmath/ctx_iv.py", "Lib/site-packages/mpmath/ctx_mp.py", "Lib/site-packages/mpmath/ctx_mp_python.py", "Lib/site-packages/mpmath/function_docs.py", "Lib/site-packages/mpmath/functions/__init__.py", "Lib/site-packages/mpmath/functions/bessel.py", "Lib/site-packages/mpmath/functions/elliptic.py", "Lib/site-packages/mpmath/functions/expintegrals.py", "Lib/site-packages/mpmath/functions/factorials.py", "Lib/site-packages/mpmath/functions/functions.py", "Lib/site-packages/mpmath/functions/hypergeometric.py", "Lib/site-packages/mpmath/functions/orthogonal.py", "Lib/site-packages/mpmath/functions/qfunctions.py", "Lib/site-packages/mpmath/functions/rszeta.py", "Lib/site-packages/mpmath/functions/signals.py", "Lib/site-packages/mpmath/functions/theta.py", "Lib/site-packages/mpmath/functions/zeta.py", "Lib/site-packages/mpmath/functions/zetazeros.py", "Lib/site-packages/mpmath/identification.py", "Lib/site-packages/mpmath/libmp/__init__.py", "Lib/site-packages/mpmath/libmp/backend.py", "Lib/site-packages/mpmath/libmp/gammazeta.py", "Lib/site-packages/mpmath/libmp/libelefun.py", "Lib/site-packages/mpmath/libmp/libhyper.py", "Lib/site-packages/mpmath/libmp/libintmath.py", "Lib/site-packages/mpmath/libmp/libmpc.py", "Lib/site-packages/mpmath/libmp/libmpf.py", "Lib/site-packages/mpmath/libmp/libmpi.py", "Lib/site-packages/mpmath/math2.py", "Lib/site-packages/mpmath/matrices/__init__.py", "Lib/site-packages/mpmath/matrices/calculus.py", "Lib/site-packages/mpmath/matrices/eigen.py", "Lib/site-packages/mpmath/matrices/eigen_symmetric.py", "Lib/site-packages/mpmath/matrices/linalg.py", "Lib/site-packages/mpmath/matrices/matrices.py", "Lib/site-packages/mpmath/rational.py", "Lib/site-packages/mpmath/tests/__init__.py", "Lib/site-packages/mpmath/tests/extratest_gamma.py", "Lib/site-packages/mpmath/tests/extratest_zeta.py", "Lib/site-packages/mpmath/tests/runtests.py", "Lib/site-packages/mpmath/tests/test_basic_ops.py", "Lib/site-packages/mpmath/tests/test_bitwise.py", "Lib/site-packages/mpmath/tests/test_calculus.py", "Lib/site-packages/mpmath/tests/test_compatibility.py", "Lib/site-packages/mpmath/tests/test_convert.py", "Lib/site-packages/mpmath/tests/test_diff.py", "Lib/site-packages/mpmath/tests/test_division.py", "Lib/site-packages/mpmath/tests/test_eigen.py", "Lib/site-packages/mpmath/tests/test_eigen_symmetric.py", "Lib/site-packages/mpmath/tests/test_elliptic.py", "Lib/site-packages/mpmath/tests/test_fp.py", "Lib/site-packages/mpmath/tests/test_functions.py", "Lib/site-packages/mpmath/tests/test_functions2.py", "Lib/site-packages/mpmath/tests/test_gammazeta.py", "Lib/site-packages/mpmath/tests/test_hp.py", "Lib/site-packages/mpmath/tests/test_identify.py", "Lib/site-packages/mpmath/tests/test_interval.py", "Lib/site-packages/mpmath/tests/test_levin.py", "Lib/site-packages/mpmath/tests/test_linalg.py", "Lib/site-packages/mpmath/tests/test_matrices.py", "Lib/site-packages/mpmath/tests/test_mpmath.py", "Lib/site-packages/mpmath/tests/test_ode.py", "Lib/site-packages/mpmath/tests/test_pickle.py", "Lib/site-packages/mpmath/tests/test_power.py", "Lib/site-packages/mpmath/tests/test_quad.py", "Lib/site-packages/mpmath/tests/test_rootfinding.py", "Lib/site-packages/mpmath/tests/test_special.py", "Lib/site-packages/mpmath/tests/test_str.py", "Lib/site-packages/mpmath/tests/test_summation.py", "Lib/site-packages/mpmath/tests/test_trig.py", "Lib/site-packages/mpmath/tests/test_visualization.py", "Lib/site-packages/mpmath/tests/torture.py", "Lib/site-packages/mpmath/usertools.py", "Lib/site-packages/mpmath/visualization.py", "Lib/site-packages/mpmath/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/mpmath/calculus/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/mpmath/calculus/__pycache__/approximation.cpython-310.pyc", "Lib/site-packages/mpmath/calculus/__pycache__/calculus.cpython-310.pyc", "Lib/site-packages/mpmath/calculus/__pycache__/differentiation.cpython-310.pyc", "Lib/site-packages/mpmath/calculus/__pycache__/extrapolation.cpython-310.pyc", "Lib/site-packages/mpmath/calculus/__pycache__/inverselaplace.cpython-310.pyc", "Lib/site-packages/mpmath/calculus/__pycache__/odes.cpython-310.pyc", "Lib/site-packages/mpmath/calculus/__pycache__/optimization.cpython-310.pyc", "Lib/site-packages/mpmath/calculus/__pycache__/polynomials.cpython-310.pyc", "Lib/site-packages/mpmath/calculus/__pycache__/quadrature.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/ctx_base.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/ctx_fp.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/ctx_iv.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/ctx_mp.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/ctx_mp_python.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/function_docs.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/bessel.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/elliptic.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/expintegrals.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/factorials.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/functions.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/hypergeometric.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/orthogonal.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/qfunctions.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/rszeta.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/signals.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/theta.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/zeta.cpython-310.pyc", "Lib/site-packages/mpmath/functions/__pycache__/zetazeros.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/identification.cpython-310.pyc", "Lib/site-packages/mpmath/libmp/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/mpmath/libmp/__pycache__/backend.cpython-310.pyc", "Lib/site-packages/mpmath/libmp/__pycache__/gammazeta.cpython-310.pyc", "Lib/site-packages/mpmath/libmp/__pycache__/libelefun.cpython-310.pyc", "Lib/site-packages/mpmath/libmp/__pycache__/libhyper.cpython-310.pyc", "Lib/site-packages/mpmath/libmp/__pycache__/libintmath.cpython-310.pyc", "Lib/site-packages/mpmath/libmp/__pycache__/libmpc.cpython-310.pyc", "Lib/site-packages/mpmath/libmp/__pycache__/libmpf.cpython-310.pyc", "Lib/site-packages/mpmath/libmp/__pycache__/libmpi.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/math2.cpython-310.pyc", "Lib/site-packages/mpmath/matrices/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/mpmath/matrices/__pycache__/calculus.cpython-310.pyc", "Lib/site-packages/mpmath/matrices/__pycache__/eigen.cpython-310.pyc", "Lib/site-packages/mpmath/matrices/__pycache__/eigen_symmetric.cpython-310.pyc", "Lib/site-packages/mpmath/matrices/__pycache__/linalg.cpython-310.pyc", "Lib/site-packages/mpmath/matrices/__pycache__/matrices.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/rational.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/extratest_gamma.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/extratest_zeta.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/runtests.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_basic_ops.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_bitwise.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_calculus.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_compatibility.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_convert.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_diff.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_division.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_eigen.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_eigen_symmetric.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_elliptic.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_fp.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_functions.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_functions2.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_gammazeta.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_hp.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_identify.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_interval.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_levin.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_linalg.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_matrices.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_mpmath.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_ode.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_pickle.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_power.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_quad.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_rootfinding.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_special.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_str.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_summation.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_trig.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/test_visualization.cpython-310.pyc", "Lib/site-packages/mpmath/tests/__pycache__/torture.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/usertools.cpython-310.pyc", "Lib/site-packages/mpmath/__pycache__/visualization.cpython-310.pyc"], "fn": "mpmath-1.3.0-pyhd8ed1ab_1.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\mpmath-1.3.0-pyhd8ed1ab_1", "type": 1}, "md5": "3585aa87c43ab15b167b574cd73b057b", "name": "mpmath", "noarch": "python", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\mpmath-1.3.0-pyhd8ed1ab_1.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/mpmath-1.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/mpmath-1.3.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "c26cae81da4508e5e249985777a33821f183223ebb74d7f8cfbf90fe7eef2fb7", "sha256_in_prefix": "c26cae81da4508e5e249985777a33821f183223ebb74d7f8cfbf90fe7eef2fb7", "size_in_bytes": 1537}, {"_path": "site-packages/mpmath-1.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "cf413b54c3117db7f0508588251d924fe297a52f67e102a82370230b83d47ef6", "sha256_in_prefix": "cf413b54c3117db7f0508588251d924fe297a52f67e102a82370230b83d47ef6", "size_in_bytes": 8611}, {"_path": "site-packages/mpmath-1.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "55671f0199becb40bb2438ac9e5a9a64fdb8981c2ff362f332750bf313fcf371", "sha256_in_prefix": "55671f0199becb40bb2438ac9e5a9a64fdb8981c2ff362f332750bf313fcf371", "size_in_bytes": 12781}, {"_path": "site-packages/mpmath-1.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/mpmath-1.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "sha256_in_prefix": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "size_in_bytes": 91}, {"_path": "site-packages/mpmath-1.3.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "a1d288968b5d51bb86076814248ca2cd77cc8f4b7a282d9eed583b737ea06230", "sha256_in_prefix": "a1d288968b5d51bb86076814248ca2cd77cc8f4b7a282d9eed583b737ea06230", "size_in_bytes": 102}, {"_path": "site-packages/mpmath-1.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "054556ae1f0456590e84cd67dd7f52f26b13695702fb7b3a4a3b7ad1abc762eb", "sha256_in_prefix": "054556ae1f0456590e84cd67dd7f52f26b13695702fb7b3a4a3b7ad1abc762eb", "size_in_bytes": 7}, {"_path": "site-packages/mpmath/__init__.py", "path_type": "hardlink", "sha256": "b241584d2c1fc0304b0a1015ea923749d7b0800411dd406dcab7c82bf25d9fe8", "sha256_in_prefix": "b241584d2c1fc0304b0a1015ea923749d7b0800411dd406dcab7c82bf25d9fe8", "size_in_bytes": 8765}, {"_path": "site-packages/mpmath/calculus/__init__.py", "path_type": "hardlink", "sha256": "500802209d5899a7b24eaa4dce306509919e2332ed51930412997df5558d8eeb", "sha256_in_prefix": "500802209d5899a7b24eaa4dce306509919e2332ed51930412997df5558d8eeb", "size_in_bytes": 162}, {"_path": "site-packages/mpmath/calculus/approximation.py", "path_type": "hardlink", "sha256": "bf2ceedd823aafadceab52851eb433d369865c0707db77a6a8d62126e51a159e", "sha256_in_prefix": "bf2ceedd823aafadceab52851eb433d369865c0707db77a6a8d62126e51a159e", "size_in_bytes": 8817}, {"_path": "site-packages/mpmath/calculus/calculus.py", "path_type": "hardlink", "sha256": "034812a748714b21037ee80956263709e59a945faf2bbd3561fb73ae3490cd0e", "sha256_in_prefix": "034812a748714b21037ee80956263709e59a945faf2bbd3561fb73ae3490cd0e", "size_in_bytes": 112}, {"_path": "site-packages/mpmath/calculus/differentiation.py", "path_type": "hardlink", "sha256": "d8be82063f31b57f628a9f7c34f6cab0bb70b518f18b9ef5c1899330715e2fdd", "sha256_in_prefix": "d8be82063f31b57f628a9f7c34f6cab0bb70b518f18b9ef5c1899330715e2fdd", "size_in_bytes": 20226}, {"_path": "site-packages/mpmath/calculus/extrapolation.py", "path_type": "hardlink", "sha256": "c4cd2bbe4d83144178891d49865f98dda4bdde25bd555257ef2f481a99b30be0", "sha256_in_prefix": "c4cd2bbe4d83144178891d49865f98dda4bdde25bd555257ef2f481a99b30be0", "size_in_bytes": 73306}, {"_path": "site-packages/mpmath/calculus/inverselaplace.py", "path_type": "hardlink", "sha256": "e7ea67f0dfedd0fb600535e2c6c5d9e31c6b8a12b6279818b157d329f0f1e200", "sha256_in_prefix": "e7ea67f0dfedd0fb600535e2c6c5d9e31c6b8a12b6279818b157d329f0f1e200", "size_in_bytes": 36056}, {"_path": "site-packages/mpmath/calculus/odes.py", "path_type": "hardlink", "sha256": "81a1e2c3b2098ec38d35301aea2cc53d9a66720f6ec93a7c3142e719dcd3206a", "sha256_in_prefix": "81a1e2c3b2098ec38d35301aea2cc53d9a66720f6ec93a7c3142e719dcd3206a", "size_in_bytes": 9908}, {"_path": "site-packages/mpmath/calculus/optimization.py", "path_type": "hardlink", "sha256": "6ca9d285712504e9953883a515e92c0ec602a7d7d84a66302a65c3b74cf6e8c3", "sha256_in_prefix": "6ca9d285712504e9953883a515e92c0ec602a7d7d84a66302a65c3b74cf6e8c3", "size_in_bytes": 32856}, {"_path": "site-packages/mpmath/calculus/polynomials.py", "path_type": "hardlink", "sha256": "0f5e81854fd21db562d3a2313700011d1f87efb23296774db0dde6b8f4ee158b", "sha256_in_prefix": "0f5e81854fd21db562d3a2313700011d1f87efb23296774db0dde6b8f4ee158b", "size_in_bytes": 7877}, {"_path": "site-packages/mpmath/calculus/quadrature.py", "path_type": "hardlink", "sha256": "9fe6afb52f04e377e857ee6daf99687e03816a2314604f0026341c588f5070a9", "sha256_in_prefix": "9fe6afb52f04e377e857ee6daf99687e03816a2314604f0026341c588f5070a9", "size_in_bytes": 42432}, {"_path": "site-packages/mpmath/ctx_base.py", "path_type": "hardlink", "sha256": "adf8e67ccc80e79c7c47f716148354c165531257d99b2c797ab28375ab92115c", "sha256_in_prefix": "adf8e67ccc80e79c7c47f716148354c165531257d99b2c797ab28375ab92115c", "size_in_bytes": 15985}, {"_path": "site-packages/mpmath/ctx_fp.py", "path_type": "hardlink", "sha256": "72d523c7f3685348855a4d397170d808bd99b4b64e956b359fa65aa37a5b1b68", "sha256_in_prefix": "72d523c7f3685348855a4d397170d808bd99b4b64e956b359fa65aa37a5b1b68", "size_in_bytes": 6572}, {"_path": "site-packages/mpmath/ctx_iv.py", "path_type": "hardlink", "sha256": "b6a74cafe1837e4664d4486819e0806a3cbba50bfe456b6b56a8588df23caf88", "sha256_in_prefix": "b6a74cafe1837e4664d4486819e0806a3cbba50bfe456b6b56a8588df23caf88", "size_in_bytes": 17211}, {"_path": "site-packages/mpmath/ctx_mp.py", "path_type": "hardlink", "sha256": "777af8b7bc4736a485b66aac03d06dab5369cb759333ea5c336fe3782c840b16", "sha256_in_prefix": "777af8b7bc4736a485b66aac03d06dab5369cb759333ea5c336fe3782c840b16", "size_in_bytes": 49452}, {"_path": "site-packages/mpmath/ctx_mp_python.py", "path_type": "hardlink", "sha256": "de89585a8e259354a743403f21a675f35aaa1bcbb9a7119ab7fbfe2f842d9f76", "sha256_in_prefix": "de89585a8e259354a743403f21a675f35aaa1bcbb9a7119ab7fbfe2f842d9f76", "size_in_bytes": 37815}, {"_path": "site-packages/mpmath/function_docs.py", "path_type": "hardlink", "sha256": "8383cff27e882d798770bc80e74b312ba4e6a7f678fe944dfb00c4ad4f03d62e", "sha256_in_prefix": "8383cff27e882d798770bc80e74b312ba4e6a7f678fe944dfb00c4ad4f03d62e", "size_in_bytes": 283512}, {"_path": "site-packages/mpmath/functions/__init__.py", "path_type": "hardlink", "sha256": "61755d86abfee8b2a6e9caf9c71b53353b6e0fdcc33ca19097c1a64b4c73db1a", "sha256_in_prefix": "61755d86abfee8b2a6e9caf9c71b53353b6e0fdcc33ca19097c1a64b4c73db1a", "size_in_bytes": 330}, {"_path": "site-packages/mpmath/functions/bessel.py", "path_type": "hardlink", "sha256": "7543cbbbc7eb94afaf99fdfe8ab5ffeeebf0cb0e3171cbfa1088b398867cf243", "sha256_in_prefix": "7543cbbbc7eb94afaf99fdfe8ab5ffeeebf0cb0e3171cbfa1088b398867cf243", "size_in_bytes": 37938}, {"_path": "site-packages/mpmath/functions/elliptic.py", "path_type": "hardlink", "sha256": "ab3d3254c6f895611e3930cbfc35b3e6ee5ac261883ca02cb991495e0c0724d5", "sha256_in_prefix": "ab3d3254c6f895611e3930cbfc35b3e6ee5ac261883ca02cb991495e0c0724d5", "size_in_bytes": 42237}, {"_path": "site-packages/mpmath/functions/expintegrals.py", "path_type": "hardlink", "sha256": "ef95ff31175873517f5fbddb80d88e26ac11952da1a80cdc14b97744cdad0837", "sha256_in_prefix": "ef95ff31175873517f5fbddb80d88e26ac11952da1a80cdc14b97744cdad0837", "size_in_bytes": 11644}, {"_path": "site-packages/mpmath/functions/factorials.py", "path_type": "hardlink", "sha256": "f3fea4091edee24d46c31880389bb435169d785e23036f2ac78862761bb820b9", "sha256_in_prefix": "f3fea4091edee24d46c31880389bb435169d785e23036f2ac78862761bb820b9", "size_in_bytes": 5273}, {"_path": "site-packages/mpmath/functions/functions.py", "path_type": "hardlink", "sha256": "b9bd89131beacc258b926e72026ef616be9f756999654927abdff7c3d3048a02", "sha256_in_prefix": "b9bd89131beacc258b926e72026ef616be9f756999654927abdff7c3d3048a02", "size_in_bytes": 18100}, {"_path": "site-packages/mpmath/functions/hypergeometric.py", "path_type": "hardlink", "sha256": "67438c00c0b8ca52b8da7fd29da9b21559d4c7acc72f208b0a8243499d49ac76", "sha256_in_prefix": "67438c00c0b8ca52b8da7fd29da9b21559d4c7acc72f208b0a8243499d49ac76", "size_in_bytes": 51570}, {"_path": "site-packages/mpmath/functions/orthogonal.py", "path_type": "hardlink", "sha256": "15a6e4c4a7c1a12b1e0397e55aed5adeb7bed8161a7b0f5aba0a88b13f0b68bc", "sha256_in_prefix": "15a6e4c4a7c1a12b1e0397e55aed5adeb7bed8161a7b0f5aba0a88b13f0b68bc", "size_in_bytes": 16097}, {"_path": "site-packages/mpmath/functions/qfunctions.py", "path_type": "hardlink", "sha256": "6b710718a42dfe331de31f48efbd89cfe4c6167198f9aad6a8fbd91b3f5049ed", "sha256_in_prefix": "6b710718a42dfe331de31f48efbd89cfe4c6167198f9aad6a8fbd91b3f5049ed", "size_in_bytes": 7633}, {"_path": "site-packages/mpmath/functions/rszeta.py", "path_type": "hardlink", "sha256": "cae515a788a52320e65f21375930710c38f07c424dca92676cf4bec4f1f91e8c", "sha256_in_prefix": "cae515a788a52320e65f21375930710c38f07c424dca92676cf4bec4f1f91e8c", "size_in_bytes": 46184}, {"_path": "site-packages/mpmath/functions/signals.py", "path_type": "hardlink", "sha256": "10ba2dc10696d420e9bfe79e273399e5cdb73617da65c8fdfc691bde9b2f4b44", "sha256_in_prefix": "10ba2dc10696d420e9bfe79e273399e5cdb73617da65c8fdfc691bde9b2f4b44", "size_in_bytes": 703}, {"_path": "site-packages/mpmath/functions/theta.py", "path_type": "hardlink", "sha256": "2a080ea1c733a0c1bafc73286a5e2810fee267848a3a8bbd24513e5b3636af73", "sha256_in_prefix": "2a080ea1c733a0c1bafc73286a5e2810fee267848a3a8bbd24513e5b3636af73", "size_in_bytes": 37320}, {"_path": "site-packages/mpmath/functions/zeta.py", "path_type": "hardlink", "sha256": "b9eec963b197034a17f2ad3cb10265d82491ad9ee43adf07b1fb695639d3c2b1", "sha256_in_prefix": "b9eec963b197034a17f2ad3cb10265d82491ad9ee43adf07b1fb695639d3c2b1", "size_in_bytes": 36410}, {"_path": "site-packages/mpmath/functions/zetazeros.py", "path_type": "hardlink", "sha256": "baae93572641718d8c2d7ed549d55f9f44ce928c012ccf5f5ed9f2484c1a373c", "sha256_in_prefix": "baae93572641718d8c2d7ed549d55f9f44ce928c012ccf5f5ed9f2484c1a373c", "size_in_bytes": 30858}, {"_path": "site-packages/mpmath/identification.py", "path_type": "hardlink", "sha256": "eda31d9e044069e2ff31a7c350d6e61089464129251c367ca663c5b7e38b824c", "sha256_in_prefix": "eda31d9e044069e2ff31a7c350d6e61089464129251c367ca663c5b7e38b824c", "size_in_bytes": 29253}, {"_path": "site-packages/mpmath/libmp/__init__.py", "path_type": "hardlink", "sha256": "5020e32d9c386eb6e495a0a64a2c4270f74b747933f2c17ffba17fc2bd1db808", "sha256_in_prefix": "5020e32d9c386eb6e495a0a64a2c4270f74b747933f2c17ffba17fc2bd1db808", "size_in_bytes": 3790}, {"_path": "site-packages/mpmath/libmp/backend.py", "path_type": "hardlink", "sha256": "dba03ca5491a1a8bf6eafaeb14d415c962792c3b4af2c977507ad82de71a4e30", "sha256_in_prefix": "dba03ca5491a1a8bf6eafaeb14d415c962792c3b4af2c977507ad82de71a4e30", "size_in_bytes": 3360}, {"_path": "site-packages/mpmath/libmp/gammazeta.py", "path_type": "hardlink", "sha256": "5ea770e8f328b300da49c6bfb0eb3e220951ba4ddf6fc73da78dccfe56dcae57", "sha256_in_prefix": "5ea770e8f328b300da49c6bfb0eb3e220951ba4ddf6fc73da78dccfe56dcae57", "size_in_bytes": 71469}, {"_path": "site-packages/mpmath/libmp/libelefun.py", "path_type": "hardlink", "sha256": "8e80593f814e7713df89e5aca352cfb52afa747c9da46fcb42217f6d841858c8", "sha256_in_prefix": "8e80593f814e7713df89e5aca352cfb52afa747c9da46fcb42217f6d841858c8", "size_in_bytes": 43861}, {"_path": "site-packages/mpmath/libmp/libhyper.py", "path_type": "hardlink", "sha256": "27d7e674317abb6ec472cb045af06e55a01ade11633ef3cdd5281182ed5d11b7", "sha256_in_prefix": "27d7e674317abb6ec472cb045af06e55a01ade11633ef3cdd5281182ed5d11b7", "size_in_bytes": 36624}, {"_path": "site-packages/mpmath/libmp/libintmath.py", "path_type": "hardlink", "sha256": "688453d2b91467fb1d1907f74cd08b77ea4132fb505a3b2c6ef14b7caed4d237", "sha256_in_prefix": "688453d2b91467fb1d1907f74cd08b77ea4132fb505a3b2c6ef14b7caed4d237", "size_in_bytes": 16688}, {"_path": "site-packages/mpmath/libmp/libmpc.py", "path_type": "hardlink", "sha256": "2819dd523b396154b7dbe21dddf7e50df620a5d5b53ebc7acdfa3c133e506ebb", "sha256_in_prefix": "2819dd523b396154b7dbe21dddf7e50df620a5d5b53ebc7acdfa3c133e506ebb", "size_in_bytes": 26875}, {"_path": "site-packages/mpmath/libmp/libmpf.py", "path_type": "hardlink", "sha256": "be93f490d56449c6c2568668809e166ad97823b09ed1de1dcc75637d57b9eeca", "sha256_in_prefix": "be93f490d56449c6c2568668809e166ad97823b09ed1de1dcc75637d57b9eeca", "size_in_bytes": 45021}, {"_path": "site-packages/mpmath/libmp/libmpi.py", "path_type": "hardlink", "sha256": "bb4239122c24a9afb8f9d5c44e2e64eccb9ac417996ef0803c5b65f7753d605d", "sha256_in_prefix": "bb4239122c24a9afb8f9d5c44e2e64eccb9ac417996ef0803c5b65f7753d605d", "size_in_bytes": 27622}, {"_path": "site-packages/mpmath/math2.py", "path_type": "hardlink", "sha256": "3b90e0960f354ac5b4c1f1c3509717383f3e70268bbdb548bf2c34b159916e92", "sha256_in_prefix": "3b90e0960f354ac5b4c1f1c3509717383f3e70268bbdb548bf2c34b159916e92", "size_in_bytes": 18561}, {"_path": "site-packages/mpmath/matrices/__init__.py", "path_type": "hardlink", "sha256": "113cc60dc8986eaf5fb622b068c6c9d79108f8a3571ebcd16fe6477a186a163b", "sha256_in_prefix": "113cc60dc8986eaf5fb622b068c6c9d79108f8a3571ebcd16fe6477a186a163b", "size_in_bytes": 94}, {"_path": "site-packages/mpmath/matrices/calculus.py", "path_type": "hardlink", "sha256": "3cd46afa9da7c604fe7f30b9e0ad9d7a98bc75d85dc7a43ce86f2aa548877946", "sha256_in_prefix": "3cd46afa9da7c604fe7f30b9e0ad9d7a98bc75d85dc7a43ce86f2aa548877946", "size_in_bytes": 18609}, {"_path": "site-packages/mpmath/matrices/eigen.py", "path_type": "hardlink", "sha256": "19b0d72370a2c7311d5f1af51bceae5169009e002f77ed39326490f93b2eff99", "sha256_in_prefix": "19b0d72370a2c7311d5f1af51bceae5169009e002f77ed39326490f93b2eff99", "size_in_bytes": 24394}, {"_path": "site-packages/mpmath/matrices/eigen_symmetric.py", "path_type": "hardlink", "sha256": "14f28f790af5706630e98e9e6b7d9ad587445900cb3fa265407100d967cd2d88", "sha256_in_prefix": "14f28f790af5706630e98e9e6b7d9ad587445900cb3fa265407100d967cd2d88", "size_in_bytes": 58534}, {"_path": "site-packages/mpmath/matrices/linalg.py", "path_type": "hardlink", "sha256": "d380b78a3ccc1689bba1be5f5c10837f23cf768dc121ba08784f31d80eafa85d", "sha256_in_prefix": "d380b78a3ccc1689bba1be5f5c10837f23cf768dc121ba08784f31d80eafa85d", "size_in_bytes": 26958}, {"_path": "site-packages/mpmath/matrices/matrices.py", "path_type": "hardlink", "sha256": "a3bf04abad841d09f172c4742c1a160c419110ea0de0bda20ccd6addd42bc34a", "sha256_in_prefix": "a3bf04abad841d09f172c4742c1a160c419110ea0de0bda20ccd6addd42bc34a", "size_in_bytes": 32331}, {"_path": "site-packages/mpmath/rational.py", "path_type": "hardlink", "sha256": "eb8779e9fbd95e7818653ee73801de151517efb789d40d11deba5f58153e992a", "sha256_in_prefix": "eb8779e9fbd95e7818653ee73801de151517efb789d40d11deba5f58153e992a", "size_in_bytes": 5976}, {"_path": "site-packages/mpmath/tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/mpmath/tests/extratest_gamma.py", "path_type": "hardlink", "sha256": "c627615d47a520b731b623c6a130478ea50e28827311a67fbf79e7b464325994", "sha256_in_prefix": "c627615d47a520b731b623c6a130478ea50e28827311a67fbf79e7b464325994", "size_in_bytes": 7228}, {"_path": "site-packages/mpmath/tests/extratest_zeta.py", "path_type": "hardlink", "sha256": "b20d748fd4618c1a55d84754ab2621195d84456bccfbe12fc30188cfa1d39a5c", "sha256_in_prefix": "b20d748fd4618c1a55d84754ab2621195d84456bccfbe12fc30188cfa1d39a5c", "size_in_bytes": 1003}, {"_path": "site-packages/mpmath/tests/runtests.py", "path_type": "hardlink", "sha256": "ecd515f361772bfe40854f2608b5057f93a26ed4fbba5a050b03f233797bd703", "sha256_in_prefix": "ecd515f361772bfe40854f2608b5057f93a26ed4fbba5a050b03f233797bd703", "size_in_bytes": 5189}, {"_path": "site-packages/mpmath/tests/test_basic_ops.py", "path_type": "hardlink", "sha256": "76c07c0d11be1ab3f305a67e6c86ae61a6da7aa5db7ea068f5220ff41a9c4d2b", "sha256_in_prefix": "76c07c0d11be1ab3f305a67e6c86ae61a6da7aa5db7ea068f5220ff41a9c4d2b", "size_in_bytes": 15348}, {"_path": "site-packages/mpmath/tests/test_bitwise.py", "path_type": "hardlink", "sha256": "fa72d886041b843cdadd240ceb706192d627b4009a82a318c7d89bddd3e74ca3", "sha256_in_prefix": "fa72d886041b843cdadd240ceb706192d627b4009a82a318c7d89bddd3e74ca3", "size_in_bytes": 7686}, {"_path": "site-packages/mpmath/tests/test_calculus.py", "path_type": "hardlink", "sha256": "e28c6d35f3293b844b2e83b3aefeebf7e87c05ca9f06c24813a529b077bb738c", "sha256_in_prefix": "e28c6d35f3293b844b2e83b3aefeebf7e87c05ca9f06c24813a529b077bb738c", "size_in_bytes": 9187}, {"_path": "site-packages/mpmath/tests/test_compatibility.py", "path_type": "hardlink", "sha256": "feddc0499de385f00c9cdd6fa165fb3c33480f39fedcfa24909188753d71ef2d", "sha256_in_prefix": "feddc0499de385f00c9cdd6fa165fb3c33480f39fedcfa24909188753d71ef2d", "size_in_bytes": 2306}, {"_path": "site-packages/mpmath/tests/test_convert.py", "path_type": "hardlink", "sha256": "24f7037132485a1e69ac8c63c790ccd5a35682a954a05d8aa47bc080adee1e2e", "sha256_in_prefix": "24f7037132485a1e69ac8c63c790ccd5a35682a954a05d8aa47bc080adee1e2e", "size_in_bytes": 8834}, {"_path": "site-packages/mpmath/tests/test_diff.py", "path_type": "hardlink", "sha256": "aa3885f0dc50f2fb9eb99e591c63d0fa48dc8ff23b261fdf11d16d680f03cc42", "sha256_in_prefix": "aa3885f0dc50f2fb9eb99e591c63d0fa48dc8ff23b261fdf11d16d680f03cc42", "size_in_bytes": 2466}, {"_path": "site-packages/mpmath/tests/test_division.py", "path_type": "hardlink", "sha256": "ea551e65f99a056befb3376a9562cc1e05cf8d5b31bd6d56669778fa31560a95", "sha256_in_prefix": "ea551e65f99a056befb3376a9562cc1e05cf8d5b31bd6d56669778fa31560a95", "size_in_bytes": 5340}, {"_path": "site-packages/mpmath/tests/test_eigen.py", "path_type": "hardlink", "sha256": "da69ea5404c66ec264bd25473e98ad7c093cf35b7015f6f72ec3b75e2915f47b", "sha256_in_prefix": "da69ea5404c66ec264bd25473e98ad7c093cf35b7015f6f72ec3b75e2915f47b", "size_in_bytes": 3905}, {"_path": "site-packages/mpmath/tests/test_eigen_symmetric.py", "path_type": "hardlink", "sha256": "bf456298289c214da8c0048330168ffadfb7d2eabea5772c825b7de4a06d34ee", "sha256_in_prefix": "bf456298289c214da8c0048330168ffadfb7d2eabea5772c825b7de4a06d34ee", "size_in_bytes": 8778}, {"_path": "site-packages/mpmath/tests/test_elliptic.py", "path_type": "hardlink", "sha256": "2a38b0abd05be8dfce3b3cd67b0190d4cfcf31aeef46ce36574b7dd20968671a", "sha256_in_prefix": "2a38b0abd05be8dfce3b3cd67b0190d4cfcf31aeef46ce36574b7dd20968671a", "size_in_bytes": 26225}, {"_path": "site-packages/mpmath/tests/test_fp.py", "path_type": "hardlink", "sha256": "009a34153c87e01b949d4b2fd7be8b0fde7aa26df4f0a1989ddcbe6f9e0a1b13", "sha256_in_prefix": "009a34153c87e01b949d4b2fd7be8b0fde7aa26df4f0a1989ddcbe6f9e0a1b13", "size_in_bytes": 89997}, {"_path": "site-packages/mpmath/tests/test_functions.py", "path_type": "hardlink", "sha256": "6f8ed5cb07689a8397e8a98c9b3f7e8afd88a95232770292b94c36a569451eb6", "sha256_in_prefix": "6f8ed5cb07689a8397e8a98c9b3f7e8afd88a95232770292b94c36a569451eb6", "size_in_bytes": 30955}, {"_path": "site-packages/mpmath/tests/test_functions2.py", "path_type": "hardlink", "sha256": "be5c3645684bd684dc89f9ce303c756bf63337de9480d34813949378a46fd476", "sha256_in_prefix": "be5c3645684bd684dc89f9ce303c756bf63337de9480d34813949378a46fd476", "size_in_bytes": 96990}, {"_path": "site-packages/mpmath/tests/test_gammazeta.py", "path_type": "hardlink", "sha256": "001df83b40d5ec09447fd6786eb9c269d790539fae030856470e4565ab3ae430", "sha256_in_prefix": "001df83b40d5ec09447fd6786eb9c269d790539fae030856470e4565ab3ae430", "size_in_bytes": 27917}, {"_path": "site-packages/mpmath/tests/test_hp.py", "path_type": "hardlink", "sha256": "ea170436ee937b69253c489349e2c12113e51fb3c00e527014a6f1f31a673a18", "sha256_in_prefix": "ea170436ee937b69253c489349e2c12113e51fb3c00e527014a6f1f31a673a18", "size_in_bytes": 10461}, {"_path": "site-packages/mpmath/tests/test_identify.py", "path_type": "hardlink", "sha256": "9465083dfac1da9693834705528eb81a633317bec5f60b3d1508d7ee0c461d5f", "sha256_in_prefix": "9465083dfac1da9693834705528eb81a633317bec5f60b3d1508d7ee0c461d5f", "size_in_bytes": 692}, {"_path": "site-packages/mpmath/tests/test_interval.py", "path_type": "hardlink", "sha256": "4e361dedaf5c6ba8912622e3c34ea2b0b7994ee1a81803e68257836747187c9d", "sha256_in_prefix": "4e361dedaf5c6ba8912622e3c34ea2b0b7994ee1a81803e68257836747187c9d", "size_in_bytes": 17527}, {"_path": "site-packages/mpmath/tests/test_levin.py", "path_type": "hardlink", "sha256": "3fc335d72575763fe075236fe71bb00b316217ce90c910ed3cc8d4472eb0276f", "sha256_in_prefix": "3fc335d72575763fe075236fe71bb00b316217ce90c910ed3cc8d4472eb0276f", "size_in_bytes": 5090}, {"_path": "site-packages/mpmath/tests/test_linalg.py", "path_type": "hardlink", "sha256": "9a22849f007c8b0595d77862d5b175720de1801e2b4ca391d1fbc355f5a65ddb", "sha256_in_prefix": "9a22849f007c8b0595d77862d5b175720de1801e2b4ca391d1fbc355f5a65ddb", "size_in_bytes": 10440}, {"_path": "site-packages/mpmath/tests/test_matrices.py", "path_type": "hardlink", "sha256": "ab2038325d82bcdbd6d37e25cc1d351bac1580dafb52b819aa1db090c5eda733", "sha256_in_prefix": "ab2038325d82bcdbd6d37e25cc1d351bac1580dafb52b819aa1db090c5eda733", "size_in_bytes": 7944}, {"_path": "site-packages/mpmath/tests/test_mpmath.py", "path_type": "hardlink", "sha256": "2d5c8951ea1f89ac56fb32ca595042cf9f4bf5442c8e55b44ecf7fa01884bffe", "sha256_in_prefix": "2d5c8951ea1f89ac56fb32ca595042cf9f4bf5442c8e55b44ecf7fa01884bffe", "size_in_bytes": 196}, {"_path": "site-packages/mpmath/tests/test_ode.py", "path_type": "hardlink", "sha256": "cc0c5ec411f87e798534ee1bbc41dbba0d4d2560b9cea7c56950e56228e8c246", "sha256_in_prefix": "cc0c5ec411f87e798534ee1bbc41dbba0d4d2560b9cea7c56950e56228e8c246", "size_in_bytes": 1822}, {"_path": "site-packages/mpmath/tests/test_pickle.py", "path_type": "hardlink", "sha256": "63c08a9832c5b091d4a86f020da070e6296b3cfe184f5c628d576e2e943b5c51", "sha256_in_prefix": "63c08a9832c5b091d4a86f020da070e6296b3cfe184f5c628d576e2e943b5c51", "size_in_bytes": 401}, {"_path": "site-packages/mpmath/tests/test_power.py", "path_type": "hardlink", "sha256": "b33fcad364a6371a5ae8a6f5b892cdfcde2d5d3246750ffffef3d1b2110dec69", "sha256_in_prefix": "b33fcad364a6371a5ae8a6f5b892cdfcde2d5d3246750ffffef3d1b2110dec69", "size_in_bytes": 5227}, {"_path": "site-packages/mpmath/tests/test_quad.py", "path_type": "hardlink", "sha256": "e3d2ed7edd2f67f91d28b2f9b3e2a3f81cc05681792cf54451e354cdd3a48212", "sha256_in_prefix": "e3d2ed7edd2f67f91d28b2f9b3e2a3f81cc05681792cf54451e354cdd3a48212", "size_in_bytes": 3893}, {"_path": "site-packages/mpmath/tests/test_rootfinding.py", "path_type": "hardlink", "sha256": "ba641e80468a1e660e125e63132a03f952ca0ed5ec4c92647a9284af8734742d", "sha256_in_prefix": "ba641e80468a1e660e125e63132a03f952ca0ed5ec4c92647a9284af8734742d", "size_in_bytes": 3132}, {"_path": "site-packages/mpmath/tests/test_special.py", "path_type": "hardlink", "sha256": "61b308a0c224244bef2982334b4097b612451b4fbe8faba7efeb5c13a6fb14f3", "sha256_in_prefix": "61b308a0c224244bef2982334b4097b612451b4fbe8faba7efeb5c13a6fb14f3", "size_in_bytes": 2848}, {"_path": "site-packages/mpmath/tests/test_str.py", "path_type": "hardlink", "sha256": "d16b060fd84c3d18bccdcb98300f42bb698ebd088216c90fc0cb0c7fc9410cae", "sha256_in_prefix": "d16b060fd84c3d18bccdcb98300f42bb698ebd088216c90fc0cb0c7fc9410cae", "size_in_bytes": 544}, {"_path": "site-packages/mpmath/tests/test_summation.py", "path_type": "hardlink", "sha256": "7dd365b2f4553ac6d6c5b865c832c36843b64bc91326b44c288bc1e7e68d722d", "sha256_in_prefix": "7dd365b2f4553ac6d6c5b865c832c36843b64bc91326b44c288bc1e7e68d722d", "size_in_bytes": 2035}, {"_path": "site-packages/mpmath/tests/test_trig.py", "path_type": "hardlink", "sha256": "ccfb642049d9693871716babe24ec15fcfb62668fe0213b5f754afbfb00d6145", "sha256_in_prefix": "ccfb642049d9693871716babe24ec15fcfb62668fe0213b5f754afbfb00d6145", "size_in_bytes": 4799}, {"_path": "site-packages/mpmath/tests/test_visualization.py", "path_type": "hardlink", "sha256": "d4faad928531f96b0a6204d18aee68f6905cf3993085fd65cd4d9ea1b0d00893", "sha256_in_prefix": "d4faad928531f96b0a6204d18aee68f6905cf3993085fd65cd4d9ea1b0d00893", "size_in_bytes": 944}, {"_path": "site-packages/mpmath/tests/torture.py", "path_type": "hardlink", "sha256": "2c3f79a044bb258d8aae810b2be9be8e1bed6ef65a282867b740aaee414c342c", "sha256_in_prefix": "2c3f79a044bb258d8aae810b2be9be8e1bed6ef65a282867b740aaee414c342c", "size_in_bytes": 7868}, {"_path": "site-packages/mpmath/usertools.py", "path_type": "hardlink", "sha256": "6be4c3c3b5d246c3dd0447dfc4ea280d5e160c57ee5e73b9f0fef975c003f3b2", "sha256_in_prefix": "6be4c3c3b5d246c3dd0447dfc4ea280d5e160c57ee5e73b9f0fef975c003f3b2", "size_in_bytes": 3029}, {"_path": "site-packages/mpmath/visualization.py", "path_type": "hardlink", "sha256": "a679db8dc77d02115544165abd85f9823c78cad2bf917a030e2b1847a1295e1b", "sha256_in_prefix": "a679db8dc77d02115544165abd85f9823c78cad2bf917a030e2b1847a1295e1b", "size_in_bytes": 10627}, {"_path": "Lib/site-packages/mpmath/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/calculus/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/calculus/__pycache__/approximation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/calculus/__pycache__/calculus.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/calculus/__pycache__/differentiation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/calculus/__pycache__/extrapolation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/calculus/__pycache__/inverselaplace.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/calculus/__pycache__/odes.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/calculus/__pycache__/optimization.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/calculus/__pycache__/polynomials.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/calculus/__pycache__/quadrature.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/ctx_base.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/ctx_fp.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/ctx_iv.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/ctx_mp.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/ctx_mp_python.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/function_docs.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/bessel.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/elliptic.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/expintegrals.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/factorials.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/functions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/hypergeometric.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/orthogonal.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/qfunctions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/rszeta.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/signals.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/theta.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/zeta.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/functions/__pycache__/zetazeros.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/identification.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/libmp/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/libmp/__pycache__/backend.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/libmp/__pycache__/gammazeta.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/libmp/__pycache__/libelefun.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/libmp/__pycache__/libhyper.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/libmp/__pycache__/libintmath.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/libmp/__pycache__/libmpc.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/libmp/__pycache__/libmpf.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/libmp/__pycache__/libmpi.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/math2.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/matrices/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/matrices/__pycache__/calculus.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/matrices/__pycache__/eigen.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/matrices/__pycache__/eigen_symmetric.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/matrices/__pycache__/linalg.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/matrices/__pycache__/matrices.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/rational.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/extratest_gamma.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/extratest_zeta.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/runtests.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_basic_ops.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_bitwise.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_calculus.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_compatibility.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_convert.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_diff.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_division.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_eigen.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_eigen_symmetric.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_elliptic.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_fp.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_functions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_functions2.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_gammazeta.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_hp.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_identify.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_interval.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_levin.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_linalg.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_matrices.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_mpmath.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_ode.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_pickle.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_power.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_quad.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_rootfinding.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_special.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_str.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_summation.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_trig.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/test_visualization.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/tests/__pycache__/torture.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/usertools.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/mpmath/__pycache__/visualization.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "7d7aa3fcd6f42b76bd711182f3776a02bef09a68c5f117d66b712a6d81368692", "size": 439705, "subdir": "noarch", "timestamp": 1733302781000, "url": "https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda", "version": "1.3.0"}