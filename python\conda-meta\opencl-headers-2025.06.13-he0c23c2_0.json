{"build": "he0c23c2_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\opencl-headers-2025.06.13-he0c23c2_0", "files": ["Library/include/CL/cl.h", "Library/include/CL/cl_d3d10.h", "Library/include/CL/cl_d3d11.h", "Library/include/CL/cl_dx9_media_sharing.h", "Library/include/CL/cl_dx9_media_sharing_intel.h", "Library/include/CL/cl_egl.h", "Library/include/CL/cl_ext.h", "Library/include/CL/cl_ext_intel.h", "Library/include/CL/cl_function_types.h", "Library/include/CL/cl_gl.h", "Library/include/CL/cl_gl_ext.h", "Library/include/CL/cl_half.h", "Library/include/CL/cl_icd.h", "Library/include/CL/cl_layer.h", "Library/include/CL/cl_platform.h", "Library/include/CL/cl_va_api_media_sharing_intel.h", "Library/include/CL/cl_version.h", "Library/include/CL/opencl.h", "Library/lib/pkgconfig/OpenCL-Headers.pc", "Library/share/cmake/OpenCLHeaders/OpenCLHeadersConfig.cmake", "Library/share/cmake/OpenCLHeaders/OpenCLHeadersConfigVersion.cmake", "Library/share/cmake/OpenCLHeaders/OpenCLHeadersTargets.cmake"], "fn": "opencl-headers-2025.06.13-he0c23c2_0.conda", "license": "Apache-2.0", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\opencl-headers-2025.06.13-he0c23c2_0", "type": 1}, "md5": "25b288eda332180bba67ef785a20ae45", "name": "opencl-headers", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\opencl-headers-2025.06.13-he0c23c2_0.conda", "paths_data": {"paths": [{"_path": "Library/include/CL/cl.h", "path_type": "hardlink", "sha256": "3c51949c38bf9bca6dbe919a1f60727bad1ef4a7a321c6d530f7502ddc1fd706", "sha256_in_prefix": "3c51949c38bf9bca6dbe919a1f60727bad1ef4a7a321c6d530f7502ddc1fd706", "size_in_bytes": 81582}, {"_path": "Library/include/CL/cl_d3d10.h", "path_type": "hardlink", "sha256": "9dd1c0706da71db79465285d723b43f12eef04680ea02efa529be8b2e38f5dbe", "sha256_in_prefix": "9dd1c0706da71db79465285d723b43f12eef04680ea02efa529be8b2e38f5dbe", "size_in_bytes": 8220}, {"_path": "Library/include/CL/cl_d3d11.h", "path_type": "hardlink", "sha256": "f18e5fed81f0133362f82f67bb5c4d8dd3364df1140343be4d1ec82e60f57a4c", "sha256_in_prefix": "f18e5fed81f0133362f82f67bb5c4d8dd3364df1140343be4d1ec82e60f57a4c", "size_in_bytes": 8258}, {"_path": "Library/include/CL/cl_dx9_media_sharing.h", "path_type": "hardlink", "sha256": "b1c2071aabd613738b5b60612413acfec618b14902015f3f55d25da9a2cd3a9e", "sha256_in_prefix": "b1c2071aabd613738b5b60612413acfec618b14902015f3f55d25da9a2cd3a9e", "size_in_bytes": 12491}, {"_path": "Library/include/CL/cl_dx9_media_sharing_intel.h", "path_type": "hardlink", "sha256": "0ce4432973392815885d4b6fe968135682502d702ec3dbde6db78e173adfa8da", "sha256_in_prefix": "0ce4432973392815885d4b6fe968135682502d702ec3dbde6db78e173adfa8da", "size_in_bytes": 959}, {"_path": "Library/include/CL/cl_egl.h", "path_type": "hardlink", "sha256": "a16fcaec96792b6768d82e351c07ebbf7c9e12be415ee735eb2af5ce379cfcb7", "sha256_in_prefix": "a16fcaec96792b6768d82e351c07ebbf7c9e12be415ee735eb2af5ce379cfcb7", "size_in_bytes": 5812}, {"_path": "Library/include/CL/cl_ext.h", "path_type": "hardlink", "sha256": "6e0dc74ef76bae23c2e80d5b4b7da8524cb1342880881a1d9e2321b92e5d75af", "sha256_in_prefix": "6e0dc74ef76bae23c2e80d5b4b7da8524cb1342880881a1d9e2321b92e5d75af", "size_in_bytes": 153814}, {"_path": "Library/include/CL/cl_ext_intel.h", "path_type": "hardlink", "sha256": "20fc359c30ad2339af7c8442da97ebd8e6ea4fb8f57c47b96b0f284d4c281953", "sha256_in_prefix": "20fc359c30ad2339af7c8442da97ebd8e6ea4fb8f57c47b96b0f284d4c281953", "size_in_bytes": 902}, {"_path": "Library/include/CL/cl_function_types.h", "path_type": "hardlink", "sha256": "a90d127737e412ef3edb2ffa90f6c237ddb58899b3b14a1ec412ea0eb88c8009", "sha256_in_prefix": "a90d127737e412ef3edb2ffa90f6c237ddb58899b3b14a1ec412ea0eb88c8009", "size_in_bytes": 33387}, {"_path": "Library/include/CL/cl_gl.h", "path_type": "hardlink", "sha256": "621a3a1fb33604a9026584fc423110b9ecaa094ac2c8cf3d2c3dfe795debbbff", "sha256_in_prefix": "621a3a1fb33604a9026584fc423110b9ecaa094ac2c8cf3d2c3dfe795debbbff", "size_in_bytes": 12535}, {"_path": "Library/include/CL/cl_gl_ext.h", "path_type": "hardlink", "sha256": "7fd0d4ad6072a90b05749fe4d2131b424b71d19ae809ae3d7d4862b0d0aee16a", "sha256_in_prefix": "7fd0d4ad6072a90b05749fe4d2131b424b71d19ae809ae3d7d4862b0d0aee16a", "size_in_bytes": 905}, {"_path": "Library/include/CL/cl_half.h", "path_type": "hardlink", "sha256": "c4d64cb15e710e60058d6072f8fbb1ed988eda60d16f6fc1ddb7df8c9e903109", "sha256_in_prefix": "c4d64cb15e710e60058d6072f8fbb1ed988eda60d16f6fc1ddb7df8c9e903109", "size_in_bytes": 10430}, {"_path": "Library/include/CL/cl_icd.h", "path_type": "hardlink", "sha256": "a43e3a5c9921a4f9a59001884e75d8b79a0afef50644f80543ec63f0ca71cfce", "sha256_in_prefix": "a43e3a5c9921a4f9a59001884e75d8b79a0afef50644f80543ec63f0ca71cfce", "size_in_bytes": 11505}, {"_path": "Library/include/CL/cl_layer.h", "path_type": "hardlink", "sha256": "2eff3e48b4f478e054d077074c72719d2b3e8006e6b8057a0e55f6078e00555e", "sha256_in_prefix": "2eff3e48b4f478e054d077074c72719d2b3e8006e6b8057a0e55f6078e00555e", "size_in_bytes": 3614}, {"_path": "Library/include/CL/cl_platform.h", "path_type": "hardlink", "sha256": "be9647d121bfea20cfac9fe2be2e3ac7851a0dd77a141363dcc72f126a1a358c", "sha256_in_prefix": "be9647d121bfea20cfac9fe2be2e3ac7851a0dd77a141363dcc72f126a1a358c", "size_in_bytes": 43222}, {"_path": "Library/include/CL/cl_va_api_media_sharing_intel.h", "path_type": "hardlink", "sha256": "fe9457119b44cdd0fca90e3b21fd2c8b81de792a81ce19faa17490767fd4ed07", "sha256_in_prefix": "fe9457119b44cdd0fca90e3b21fd2c8b81de792a81ce19faa17490767fd4ed07", "size_in_bytes": 7263}, {"_path": "Library/include/CL/cl_version.h", "path_type": "hardlink", "sha256": "1b161f73c0f0b07121314055f75df6c19d6a93e3a0d24202c6f3e2dd1615b2a2", "sha256_in_prefix": "1b161f73c0f0b07121314055f75df6c19d6a93e3a0d24202c6f3e2dd1615b2a2", "size_in_bytes": 3125}, {"_path": "Library/include/CL/opencl.h", "path_type": "hardlink", "sha256": "9a76fa63630e8fd5b30b5e6c692dbe7ab22edff708dfe6a06d04a3671a7716e1", "sha256_in_prefix": "9a76fa63630e8fd5b30b5e6c692dbe7ab22edff708dfe6a06d04a3671a7716e1", "size_in_bytes": 970}, {"_path": "Library/lib/pkgconfig/OpenCL-Headers.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "D:/bld/opencl-headers_1749853293006/_h_env", "sha256": "1b71d777107543fcc028cd22b44dac8632ec448db0c4c36981bfdf4e5e648d65", "sha256_in_prefix": "09f93899677e8a2d1a3249ab750ab2ed0c2a3ee9c0a3aecad29447bd76d13b2f", "size_in_bytes": 189}, {"_path": "Library/share/cmake/OpenCLHeaders/OpenCLHeadersConfig.cmake", "path_type": "hardlink", "sha256": "d106715ebf2c7512529434a58d83f754495bfcbbdb5d84ad34bab51ace354c3d", "sha256_in_prefix": "d106715ebf2c7512529434a58d83f754495bfcbbdb5d84ad34bab51ace354c3d", "size_in_bytes": 63}, {"_path": "Library/share/cmake/OpenCLHeaders/OpenCLHeadersConfigVersion.cmake", "path_type": "hardlink", "sha256": "f61e48935ed7c46d8c362655adabc57ecbecc169880c68875d9be9dfa7739d67", "sha256_in_prefix": "f61e48935ed7c46d8c362655adabc57ecbecc169880c68875d9be9dfa7739d67", "size_in_bytes": 1899}, {"_path": "Library/share/cmake/OpenCLHeaders/OpenCLHeadersTargets.cmake", "path_type": "hardlink", "sha256": "94073c8f192045327521ab1d4f93f1d171783124498a24cc810e202c0f570338", "sha256_in_prefix": "94073c8f192045327521ab1d4f93f1d171783124498a24cc810e202c0f570338", "size_in_bytes": 4236}], "paths_version": 1}, "requested_spec": "None", "sha256": "1958dd489d32c3635e411e1802607e04a42ec685f1b2d63292211383447cecd3", "size": 55411, "subdir": "win-64", "timestamp": 1749853655000, "url": "https://conda.anaconda.org/conda-forge/win-64/opencl-headers-2025.06.13-he0c23c2_0.conda", "version": "2025.06.13"}