{"build": "py310h6d647b9_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["lcms2 >=2.17,<3.0a0", "libfreetype >=2.13.3", "libfreetype6 >=2.13.3", "libjpeg-turbo >=3.1.0,<4.0a0", "libtiff >=4.7.0,<4.8.0a0", "libwebp-base >=1.5.0,<2.0a0", "libxcb >=1.17.0,<2.0a0", "libzlib >=1.3.1,<2.0a0", "openjpeg >=2.5.3,<3.0a0", "python >=3.10,<3.11.0a0", "python_abi 3.10.* *_cp310", "tk >=8.6.13,<8.7.0a0", "ucrt >=10.0.20348.0", "vc >=14.3,<15", "vc14_runtime >=14.44.35208"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\pillow-11.3.0-py310h6d647b9_0", "files": ["Lib/site-packages/PIL/AvifImagePlugin.py", "Lib/site-packages/PIL/BdfFontFile.py", "Lib/site-packages/PIL/BlpImagePlugin.py", "Lib/site-packages/PIL/BmpImagePlugin.py", "Lib/site-packages/PIL/BufrStubImagePlugin.py", "Lib/site-packages/PIL/ContainerIO.py", "Lib/site-packages/PIL/CurImagePlugin.py", "Lib/site-packages/PIL/DcxImagePlugin.py", "Lib/site-packages/PIL/DdsImagePlugin.py", "Lib/site-packages/PIL/EpsImagePlugin.py", "Lib/site-packages/PIL/ExifTags.py", "Lib/site-packages/PIL/FitsImagePlugin.py", "Lib/site-packages/PIL/FliImagePlugin.py", "Lib/site-packages/PIL/FontFile.py", "Lib/site-packages/PIL/FpxImagePlugin.py", "Lib/site-packages/PIL/FtexImagePlugin.py", "Lib/site-packages/PIL/GbrImagePlugin.py", "Lib/site-packages/PIL/GdImageFile.py", "Lib/site-packages/PIL/GifImagePlugin.py", "Lib/site-packages/PIL/GimpGradientFile.py", "Lib/site-packages/PIL/GimpPaletteFile.py", "Lib/site-packages/PIL/GribStubImagePlugin.py", "Lib/site-packages/PIL/Hdf5StubImagePlugin.py", "Lib/site-packages/PIL/IcnsImagePlugin.py", "Lib/site-packages/PIL/IcoImagePlugin.py", "Lib/site-packages/PIL/ImImagePlugin.py", "Lib/site-packages/PIL/Image.py", "Lib/site-packages/PIL/ImageChops.py", "Lib/site-packages/PIL/ImageCms.py", "Lib/site-packages/PIL/ImageColor.py", "Lib/site-packages/PIL/ImageDraw.py", "Lib/site-packages/PIL/ImageDraw2.py", "Lib/site-packages/PIL/ImageEnhance.py", "Lib/site-packages/PIL/ImageFile.py", "Lib/site-packages/PIL/ImageFilter.py", "Lib/site-packages/PIL/ImageFont.py", "Lib/site-packages/PIL/ImageGrab.py", "Lib/site-packages/PIL/ImageMath.py", "Lib/site-packages/PIL/ImageMode.py", "Lib/site-packages/PIL/ImageMorph.py", "Lib/site-packages/PIL/ImageOps.py", "Lib/site-packages/PIL/ImagePalette.py", "Lib/site-packages/PIL/ImagePath.py", "Lib/site-packages/PIL/ImageQt.py", "Lib/site-packages/PIL/ImageSequence.py", "Lib/site-packages/PIL/ImageShow.py", "Lib/site-packages/PIL/ImageStat.py", "Lib/site-packages/PIL/ImageTk.py", "Lib/site-packages/PIL/ImageTransform.py", "Lib/site-packages/PIL/ImageWin.py", "Lib/site-packages/PIL/ImtImagePlugin.py", "Lib/site-packages/PIL/IptcImagePlugin.py", "Lib/site-packages/PIL/Jpeg2KImagePlugin.py", "Lib/site-packages/PIL/JpegImagePlugin.py", "Lib/site-packages/PIL/JpegPresets.py", "Lib/site-packages/PIL/McIdasImagePlugin.py", "Lib/site-packages/PIL/MicImagePlugin.py", "Lib/site-packages/PIL/MpegImagePlugin.py", "Lib/site-packages/PIL/MpoImagePlugin.py", "Lib/site-packages/PIL/MspImagePlugin.py", "Lib/site-packages/PIL/PSDraw.py", "Lib/site-packages/PIL/PaletteFile.py", "Lib/site-packages/PIL/PalmImagePlugin.py", "Lib/site-packages/PIL/PcdImagePlugin.py", "Lib/site-packages/PIL/PcfFontFile.py", "Lib/site-packages/PIL/PcxImagePlugin.py", "Lib/site-packages/PIL/PdfImagePlugin.py", "Lib/site-packages/PIL/PdfParser.py", "Lib/site-packages/PIL/PixarImagePlugin.py", "Lib/site-packages/PIL/PngImagePlugin.py", "Lib/site-packages/PIL/PpmImagePlugin.py", "Lib/site-packages/PIL/PsdImagePlugin.py", "Lib/site-packages/PIL/QoiImagePlugin.py", "Lib/site-packages/PIL/SgiImagePlugin.py", "Lib/site-packages/PIL/SpiderImagePlugin.py", "Lib/site-packages/PIL/SunImagePlugin.py", "Lib/site-packages/PIL/TarIO.py", "Lib/site-packages/PIL/TgaImagePlugin.py", "Lib/site-packages/PIL/TiffImagePlugin.py", "Lib/site-packages/PIL/TiffTags.py", "Lib/site-packages/PIL/WalImageFile.py", "Lib/site-packages/PIL/WebPImagePlugin.py", "Lib/site-packages/PIL/WmfImagePlugin.py", "Lib/site-packages/PIL/XVThumbImagePlugin.py", "Lib/site-packages/PIL/XbmImagePlugin.py", "Lib/site-packages/PIL/XpmImagePlugin.py", "Lib/site-packages/PIL/__init__.py", "Lib/site-packages/PIL/__main__.py", "Lib/site-packages/PIL/__pycache__/AvifImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/BdfFontFile.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/BlpImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/BmpImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/BufrStubImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ContainerIO.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/CurImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/DcxImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/DdsImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/EpsImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ExifTags.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/FitsImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/FliImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/FontFile.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/FpxImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/FtexImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/GbrImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/GdImageFile.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/GifImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/GimpGradientFile.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/GimpPaletteFile.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/GribStubImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/Hdf5StubImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/IcnsImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/IcoImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/Image.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageChops.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageCms.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageColor.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageDraw.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageDraw2.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageEnhance.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageFile.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageFilter.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageFont.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageGrab.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageMath.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageMode.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageMorph.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageOps.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImagePalette.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImagePath.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageQt.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageSequence.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageShow.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageStat.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageTk.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageTransform.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImageWin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/ImtImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/IptcImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/Jpeg2KImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/JpegImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/JpegPresets.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/McIdasImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/MicImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/MpegImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/MpoImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/MspImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PSDraw.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PaletteFile.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PalmImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PcdImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PcfFontFile.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PcxImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PdfImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PdfParser.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PixarImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PngImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PpmImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/PsdImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/QoiImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/SgiImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/SpiderImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/SunImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/TarIO.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/TgaImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/TiffImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/TiffTags.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/WalImageFile.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/WebPImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/WmfImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/XVThumbImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/XbmImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/XpmImagePlugin.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/__main__.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/_binary.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/_deprecate.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/_tkinter_finder.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/_typing.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/_util.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/_version.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/features.cpython-310.pyc", "Lib/site-packages/PIL/__pycache__/report.cpython-310.pyc", "Lib/site-packages/PIL/_avif.pyi", "Lib/site-packages/PIL/_binary.py", "Lib/site-packages/PIL/_deprecate.py", "Lib/site-packages/PIL/_imaging.cp310-win_amd64.pyd", "Lib/site-packages/PIL/_imaging.pyi", "Lib/site-packages/PIL/_imagingcms.cp310-win_amd64.pyd", "Lib/site-packages/PIL/_imagingcms.pyi", "Lib/site-packages/PIL/_imagingft.cp310-win_amd64.pyd", "Lib/site-packages/PIL/_imagingft.pyi", "Lib/site-packages/PIL/_imagingmath.cp310-win_amd64.pyd", "Lib/site-packages/PIL/_imagingmath.pyi", "Lib/site-packages/PIL/_imagingmorph.cp310-win_amd64.pyd", "Lib/site-packages/PIL/_imagingmorph.pyi", "Lib/site-packages/PIL/_imagingtk.cp310-win_amd64.pyd", "Lib/site-packages/PIL/_imagingtk.pyi", "Lib/site-packages/PIL/_tkinter_finder.py", "Lib/site-packages/PIL/_typing.py", "Lib/site-packages/PIL/_util.py", "Lib/site-packages/PIL/_version.py", "Lib/site-packages/PIL/_webp.cp310-win_amd64.pyd", "Lib/site-packages/PIL/_webp.pyi", "Lib/site-packages/PIL/features.py", "Lib/site-packages/PIL/py.typed", "Lib/site-packages/PIL/report.py", "Lib/site-packages/pillow-11.3.0.dist-info/INSTALLER", "Lib/site-packages/pillow-11.3.0.dist-info/METADATA", "Lib/site-packages/pillow-11.3.0.dist-info/RECORD", "Lib/site-packages/pillow-11.3.0.dist-info/REQUESTED", "Lib/site-packages/pillow-11.3.0.dist-info/WHEEL", "Lib/site-packages/pillow-11.3.0.dist-info/direct_url.json", "Lib/site-packages/pillow-11.3.0.dist-info/licenses/LICENSE", "Lib/site-packages/pillow-11.3.0.dist-info/top_level.txt", "Lib/site-packages/pillow-11.3.0.dist-info/zip-safe"], "fn": "pillow-11.3.0-py310h6d647b9_0.conda", "license": "HPND", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\pillow-11.3.0-py310h6d647b9_0", "type": 1}, "md5": "246b33a0eb812754b529065262aeb1c5", "name": "pillow", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\pillow-11.3.0-py310h6d647b9_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/PIL/AvifImagePlugin.py", "path_type": "hardlink", "sha256": "e4888332f3194172e74b7b76e5726397080d5a679548d6867d1796029f95e65a", "sha256_in_prefix": "e4888332f3194172e74b7b76e5726397080d5a679548d6867d1796029f95e65a", "size_in_bytes": 8994}, {"_path": "Lib/site-packages/PIL/BdfFontFile.py", "path_type": "hardlink", "sha256": "3e19597c846611f9a8adb85965e48ce5e79b1a8d448bb7cbfa547d5e57d36590", "sha256_in_prefix": "3e19597c846611f9a8adb85965e48ce5e79b1a8d448bb7cbfa547d5e57d36590", "size_in_bytes": 3285}, {"_path": "Lib/site-packages/PIL/BlpImagePlugin.py", "path_type": "hardlink", "sha256": "51be2f54a0449e288d04480d8b3c52704a4ed5529b0b5c3a89e716514ed3f95b", "sha256_in_prefix": "51be2f54a0449e288d04480d8b3c52704a4ed5529b0b5c3a89e716514ed3f95b", "size_in_bytes": 16533}, {"_path": "Lib/site-packages/PIL/BmpImagePlugin.py", "path_type": "hardlink", "sha256": "f9235d8f682876668a600734f1d12783acf79913db6181dece3bde211e5efad5", "sha256_in_prefix": "f9235d8f682876668a600734f1d12783acf79913db6181dece3bde211e5efad5", "size_in_bytes": 19855}, {"_path": "Lib/site-packages/PIL/BufrStubImagePlugin.py", "path_type": "hardlink", "sha256": "252a838643cd3e7170d10733fa040997e0ff89208576d70bbcfca7b21289e163", "sha256_in_prefix": "252a838643cd3e7170d10733fa040997e0ff89208576d70bbcfca7b21289e163", "size_in_bytes": 1730}, {"_path": "Lib/site-packages/PIL/ContainerIO.py", "path_type": "hardlink", "sha256": "c2406a2f618301be5f877c2bb5f4c651faa2a098a90a5fa58361b16e342b4d9c", "sha256_in_prefix": "c2406a2f618301be5f877c2bb5f4c651faa2a098a90a5fa58361b16e342b4d9c", "size_in_bytes": 4604}, {"_path": "Lib/site-packages/PIL/CurImagePlugin.py", "path_type": "hardlink", "sha256": "6c80a2c1766bcd238d581bb86cab6c8716523458fcb2ed256c3a23627b44518b", "sha256_in_prefix": "6c80a2c1766bcd238d581bb86cab6c8716523458fcb2ed256c3a23627b44518b", "size_in_bytes": 1797}, {"_path": "Lib/site-packages/PIL/DcxImagePlugin.py", "path_type": "hardlink", "sha256": "0e1aac996ecc8e69d4493199f9292ff61cf55de5f75e8410a0097d1962c01046", "sha256_in_prefix": "0e1aac996ecc8e69d4493199f9292ff61cf55de5f75e8410a0097d1962c01046", "size_in_bytes": 2145}, {"_path": "Lib/site-packages/PIL/DdsImagePlugin.py", "path_type": "hardlink", "sha256": "7e375f64afde42d529ffe6e3a119adfb9c20393e464e6be0e9a23e8ad721e26a", "sha256_in_prefix": "7e375f64afde42d529ffe6e3a119adfb9c20393e464e6be0e9a23e8ad721e26a", "size_in_bytes": 18906}, {"_path": "Lib/site-packages/PIL/EpsImagePlugin.py", "path_type": "hardlink", "sha256": "44e5b00afd3c6c2fc1e3578c7f60057bc516e991f8fd7435f31d76281fda40b3", "sha256_in_prefix": "44e5b00afd3c6c2fc1e3578c7f60057bc516e991f8fd7435f31d76281fda40b3", "size_in_bytes": 16389}, {"_path": "Lib/site-packages/PIL/ExifTags.py", "path_type": "hardlink", "sha256": "cd6ea4562902a2c8b2a02a3b27b47ad9ebc3de1a318ca3dc867561f29a3b0997", "sha256_in_prefix": "cd6ea4562902a2c8b2a02a3b27b47ad9ebc3de1a318ca3dc867561f29a3b0997", "size_in_bytes": 9931}, {"_path": "Lib/site-packages/PIL/FitsImagePlugin.py", "path_type": "hardlink", "sha256": "fa80c99c01f5d7708ae6a3efc33f652fcd5f915d6095afed35fa8b72af332a0a", "sha256_in_prefix": "fa80c99c01f5d7708ae6a3efc33f652fcd5f915d6095afed35fa8b72af332a0a", "size_in_bytes": 4644}, {"_path": "Lib/site-packages/PIL/FliImagePlugin.py", "path_type": "hardlink", "sha256": "0da5ae1fc7fef468a14b455566a1756d3dee0efd556f45419747219cd77cd8ec", "sha256_in_prefix": "0da5ae1fc7fef468a14b455566a1756d3dee0efd556f45419747219cd77cd8ec", "size_in_bytes": 4786}, {"_path": "Lib/site-packages/PIL/FontFile.py", "path_type": "hardlink", "sha256": "4adeccc4ee50fa86a408b5a7dd9ae0aed693df04ac9806abc66f0053e1bc3287", "sha256_in_prefix": "4adeccc4ee50fa86a408b5a7dd9ae0aed693df04ac9806abc66f0053e1bc3287", "size_in_bytes": 3577}, {"_path": "Lib/site-packages/PIL/FpxImagePlugin.py", "path_type": "hardlink", "sha256": "6977e0d1876f35e261c6a87e7fe7f6d83d4da1b43cb525428feb692c4d8f29f1", "sha256_in_prefix": "6977e0d1876f35e261c6a87e7fe7f6d83d4da1b43cb525428feb692c4d8f29f1", "size_in_bytes": 7293}, {"_path": "Lib/site-packages/PIL/FtexImagePlugin.py", "path_type": "hardlink", "sha256": "bf623962475f340de25b7e49cca9d6af2f6fe9182faf4a1ecc654eb80ad111a7", "sha256_in_prefix": "bf623962475f340de25b7e49cca9d6af2f6fe9182faf4a1ecc654eb80ad111a7", "size_in_bytes": 3535}, {"_path": "Lib/site-packages/PIL/GbrImagePlugin.py", "path_type": "hardlink", "sha256": "e6dd147cbb9b4cf41cb830db69fc02efc38b47b22c0f9863a6f85467983ccf80", "sha256_in_prefix": "e6dd147cbb9b4cf41cb830db69fc02efc38b47b22c0f9863a6f85467983ccf80", "size_in_bytes": 3006}, {"_path": "Lib/site-packages/PIL/GdImageFile.py", "path_type": "hardlink", "sha256": "2cfe14c6fdd8da2bc6648c8e56e1896ab0c3552ef32ba17543a48d978c321ae4", "sha256_in_prefix": "2cfe14c6fdd8da2bc6648c8e56e1896ab0c3552ef32ba17543a48d978c321ae4", "size_in_bytes": 2788}, {"_path": "Lib/site-packages/PIL/GifImagePlugin.py", "path_type": "hardlink", "sha256": "4a45dba19c314e896ad2eb5e5d85f49dcad9894c72012cb0a80bab39c5408b75", "sha256_in_prefix": "4a45dba19c314e896ad2eb5e5d85f49dcad9894c72012cb0a80bab39c5408b75", "size_in_bytes": 42201}, {"_path": "Lib/site-packages/PIL/GimpGradientFile.py", "path_type": "hardlink", "sha256": "67fe1351831d3f252c88fe34292229309e722c632705a20a380907ca24061161", "sha256_in_prefix": "67fe1351831d3f252c88fe34292229309e722c632705a20a380907ca24061161", "size_in_bytes": 3906}, {"_path": "Lib/site-packages/PIL/GimpPaletteFile.py", "path_type": "hardlink", "sha256": "60712129386c1159575631505271af84380db0971f16a5003743b4b9c1bd1be4", "sha256_in_prefix": "60712129386c1159575631505271af84380db0971f16a5003743b4b9c1bd1be4", "size_in_bytes": 1815}, {"_path": "Lib/site-packages/PIL/GribStubImagePlugin.py", "path_type": "hardlink", "sha256": "75e807837e385f72572fcbbec7c3569f4f01b2633dc1187e260d3c1c7aef7ce7", "sha256_in_prefix": "75e807837e385f72572fcbbec7c3569f4f01b2633dc1187e260d3c1c7aef7ce7", "size_in_bytes": 1738}, {"_path": "Lib/site-packages/PIL/Hdf5StubImagePlugin.py", "path_type": "hardlink", "sha256": "3ae1108a31aa5704d3486e1d076bc0c90ce637e3584f6dad8ae647147d10d12c", "sha256_in_prefix": "3ae1108a31aa5704d3486e1d076bc0c90ce637e3584f6dad8ae647147d10d12c", "size_in_bytes": 1741}, {"_path": "Lib/site-packages/PIL/IcnsImagePlugin.py", "path_type": "hardlink", "sha256": "aaf8be38fd20f02465365244fbee7facf95fcb12c52e548e8a5eba170e133305", "sha256_in_prefix": "aaf8be38fd20f02465365244fbee7facf95fcb12c52e548e8a5eba170e133305", "size_in_bytes": 12949}, {"_path": "Lib/site-packages/PIL/IcoImagePlugin.py", "path_type": "hardlink", "sha256": "402a36f53a21d3c517f2f11c74201a21eba2752a256cf8999429d0e2b52ed924", "sha256_in_prefix": "402a36f53a21d3c517f2f11c74201a21eba2752a256cf8999429d0e2b52ed924", "size_in_bytes": 12491}, {"_path": "Lib/site-packages/PIL/ImImagePlugin.py", "path_type": "hardlink", "sha256": "c28e4e2f63c07105b63304642674be375eada19cd758bf798f1f4504cee5f689", "sha256_in_prefix": "c28e4e2f63c07105b63304642674be375eada19cd758bf798f1f4504cee5f689", "size_in_bytes": 11567}, {"_path": "Lib/site-packages/PIL/Image.py", "path_type": "hardlink", "sha256": "f7925e7e2d901487d960ec9f1cd0514f006dc2b9cd66c9f9a02b0b42c04b74af", "sha256_in_prefix": "f7925e7e2d901487d960ec9f1cd0514f006dc2b9cd66c9f9a02b0b42c04b74af", "size_in_bytes": 148332}, {"_path": "Lib/site-packages/PIL/ImageChops.py", "path_type": "hardlink", "sha256": "1848e5ca67280ed03938e788c505485fde810feb3a01785bed39922d89f6b548", "sha256_in_prefix": "1848e5ca67280ed03938e788c505485fde810feb3a01785bed39922d89f6b548", "size_in_bytes": 7946}, {"_path": "Lib/site-packages/PIL/ImageCms.py", "path_type": "hardlink", "sha256": "0396556938e3c51e8078334dbcafa19aed10a8a38c4ec728bba0540532e86f48", "sha256_in_prefix": "0396556938e3c51e8078334dbcafa19aed10a8a38c4ec728bba0540532e86f48", "size_in_bytes": 41934}, {"_path": "Lib/site-packages/PIL/ImageColor.py", "path_type": "hardlink", "sha256": "20603d0b6ba67840ff1334b60af8fa2ac53456eb42f51b2d58860f7bcb83b159", "sha256_in_prefix": "20603d0b6ba67840ff1334b60af8fa2ac53456eb42f51b2d58860f7bcb83b159", "size_in_bytes": 9441}, {"_path": "Lib/site-packages/PIL/ImageDraw.py", "path_type": "hardlink", "sha256": "127af472d0472819d21d504396a7086c80321e0563e596cb2feb3055bf2cf15a", "sha256_in_prefix": "127af472d0472819d21d504396a7086c80321e0563e596cb2feb3055bf2cf15a", "size_in_bytes": 42845}, {"_path": "Lib/site-packages/PIL/ImageDraw2.py", "path_type": "hardlink", "sha256": "a5d54c5bb6d5c372b0857bd1661dbc31de32fb6c457eea397c77039da62a54ae", "sha256_in_prefix": "a5d54c5bb6d5c372b0857bd1661dbc31de32fb6c457eea397c77039da62a54ae", "size_in_bytes": 7227}, {"_path": "Lib/site-packages/PIL/ImageEnhance.py", "path_type": "hardlink", "sha256": "e04961cff972cb12e6c741a4487af038098d2764e4a9540f1de8f31a285950c2", "sha256_in_prefix": "e04961cff972cb12e6c741a4487af038098d2764e4a9540f1de8f31a285950c2", "size_in_bytes": 3627}, {"_path": "Lib/site-packages/PIL/ImageFile.py", "path_type": "hardlink", "sha256": "1cb80aaa7e8af49e07967ca23c56544c07dcab15d88c4d3a7d978a3ba57e85ac", "sha256_in_prefix": "1cb80aaa7e8af49e07967ca23c56544c07dcab15d88c4d3a7d978a3ba57e85ac", "size_in_bytes": 29334}, {"_path": "Lib/site-packages/PIL/ImageFilter.py", "path_type": "hardlink", "sha256": "3224e8c18f6689c8357527f06641315d204d3ebd9bff5d640c21ab78fe96f31e", "sha256_in_prefix": "3224e8c18f6689c8357527f06641315d204d3ebd9bff5d640c21ab78fe96f31e", "size_in_bytes": 18671}, {"_path": "Lib/site-packages/PIL/ImageFont.py", "path_type": "hardlink", "sha256": "ad5426df3c274c56751d2a7839e0394c72a34dece113c1ccae73a11f39aa7c43", "sha256_in_prefix": "ad5426df3c274c56751d2a7839e0394c72a34dece113c1ccae73a11f39aa7c43", "size_in_bytes": 64292}, {"_path": "Lib/site-packages/PIL/ImageGrab.py", "path_type": "hardlink", "sha256": "23d3c7a6c41fd95c8d5f84fc40bfbc6b014da2dc80cc1d661b14edfc8e456d31", "sha256_in_prefix": "23d3c7a6c41fd95c8d5f84fc40bfbc6b014da2dc80cc1d661b14edfc8e456d31", "size_in_bytes": 6471}, {"_path": "Lib/site-packages/PIL/ImageMath.py", "path_type": "hardlink", "sha256": "5dab0cb208da0fda763996bb9da39da44002ab7c8997e4364464dfe31a3b0a03", "sha256_in_prefix": "5dab0cb208da0fda763996bb9da39da44002ab7c8997e4364464dfe31a3b0a03", "size_in_bytes": 11919}, {"_path": "Lib/site-packages/PIL/ImageMode.py", "path_type": "hardlink", "sha256": "e723b1383019ee31b4dc3b1416bb7b7906b24ed22958f82f7f28655c35b072ff", "sha256_in_prefix": "e723b1383019ee31b4dc3b1416bb7b7906b24ed22958f82f7f28655c35b072ff", "size_in_bytes": 2681}, {"_path": "Lib/site-packages/PIL/ImageMorph.py", "path_type": "hardlink", "sha256": "4e8c179e4d50db05fd0175410d645142109f01b14e51618ca34d2fab8ca7f9f5", "sha256_in_prefix": "4e8c179e4d50db05fd0175410d645142109f01b14e51618ca34d2fab8ca7f9f5", "size_in_bytes": 8563}, {"_path": "Lib/site-packages/PIL/ImageOps.py", "path_type": "hardlink", "sha256": "03af6a8edfa6c4323df77f3bcffe1c1c8fb0b47f3948b2ff10d4c8f4478e4062", "sha256_in_prefix": "03af6a8edfa6c4323df77f3bcffe1c1c8fb0b47f3948b2ff10d4c8f4478e4062", "size_in_bytes": 25525}, {"_path": "Lib/site-packages/PIL/ImagePalette.py", "path_type": "hardlink", "sha256": "339b5852069d591ee6c54101cb2565ec857d40517300688a2a6021099b5d1b4c", "sha256_in_prefix": "339b5852069d591ee6c54101cb2565ec857d40517300688a2a6021099b5d1b4c", "size_in_bytes": 9009}, {"_path": "Lib/site-packages/PIL/ImagePath.py", "path_type": "hardlink", "sha256": "e72506e570948a5d4a2934c0ffc3e01a172683e9a7b9ef862b41704c19618f0e", "sha256_in_prefix": "e72506e570948a5d4a2934c0ffc3e01a172683e9a7b9ef862b41704c19618f0e", "size_in_bytes": 371}, {"_path": "Lib/site-packages/PIL/ImageQt.py", "path_type": "hardlink", "sha256": "7506da745d8b839f4e2558e235571bcf7c2fca6a84a4bfae1191b7d9bf04f9b8", "sha256_in_prefix": "7506da745d8b839f4e2558e235571bcf7c2fca6a84a4bfae1191b7d9bf04f9b8", "size_in_bytes": 6841}, {"_path": "Lib/site-packages/PIL/ImageSequence.py", "path_type": "hardlink", "sha256": "831d84bf2c0f0448f1349ba30aa7696df026d81a72355dbf7f521a3b79e2b9bc", "sha256_in_prefix": "831d84bf2c0f0448f1349ba30aa7696df026d81a72355dbf7f521a3b79e2b9bc", "size_in_bytes": 2200}, {"_path": "Lib/site-packages/PIL/ImageShow.py", "path_type": "hardlink", "sha256": "26ed3f0dbd81e3f9f7c8a255f6c0ec17bfc702072211d5e5aba235122c35613a", "sha256_in_prefix": "26ed3f0dbd81e3f9f7c8a255f6c0ec17bfc702072211d5e5aba235122c35613a", "size_in_bytes": 10106}, {"_path": "Lib/site-packages/PIL/ImageStat.py", "path_type": "hardlink", "sha256": "4b8dc567cf6bfeee212828f9f6556e5a9c9525f85b532de2817929f43c1a3203", "sha256_in_prefix": "4b8dc567cf6bfeee212828f9f6556e5a9c9525f85b532de2817929f43c1a3203", "size_in_bytes": 5325}, {"_path": "Lib/site-packages/PIL/ImageTk.py", "path_type": "hardlink", "sha256": "6f94a7b5c9065ecd040ac2363267498370925fa02d10bb168743a1c6ee35bbf9", "sha256_in_prefix": "6f94a7b5c9065ecd040ac2363267498370925fa02d10bb168743a1c6ee35bbf9", "size_in_bytes": 8132}, {"_path": "Lib/site-packages/PIL/ImageTransform.py", "path_type": "hardlink", "sha256": "faa7a4ecfde5ccb75d717b7d716b79c3f2f5d491a9db2637009b4e7e62409037", "sha256_in_prefix": "faa7a4ecfde5ccb75d717b7d716b79c3f2f5d491a9db2637009b4e7e62409037", "size_in_bytes": 3916}, {"_path": "Lib/site-packages/PIL/ImageWin.py", "path_type": "hardlink", "sha256": "2d3d39c3cfef4df46b0b79fd4bda4cd1335b5ebcd92c424794226297b5eff349", "sha256_in_prefix": "2d3d39c3cfef4df46b0b79fd4bda4cd1335b5ebcd92c424794226297b5eff349", "size_in_bytes": 8085}, {"_path": "Lib/site-packages/PIL/ImtImagePlugin.py", "path_type": "hardlink", "sha256": "48be48aec1dc6e596dc6d5f8bff1d516162747a85a27400e77634785928c2266", "sha256_in_prefix": "48be48aec1dc6e596dc6d5f8bff1d516162747a85a27400e77634785928c2266", "size_in_bytes": 2665}, {"_path": "Lib/site-packages/PIL/IptcImagePlugin.py", "path_type": "hardlink", "sha256": "dc1548fe811b144242fb29face6a79268a9ff1e74224b287f4de4541a9f2695f", "sha256_in_prefix": "dc1548fe811b144242fb29face6a79268a9ff1e74224b287f4de4541a9f2695f", "size_in_bytes": 6719}, {"_path": "Lib/site-packages/PIL/Jpeg2KImagePlugin.py", "path_type": "hardlink", "sha256": "93d52853bf87abcbc05a2f59a28b00e1b7ee7cd26c72d05de2db4cd51171c279", "sha256_in_prefix": "93d52853bf87abcbc05a2f59a28b00e1b7ee7cd26c72d05de2db4cd51171c279", "size_in_bytes": 13865}, {"_path": "Lib/site-packages/PIL/JpegImagePlugin.py", "path_type": "hardlink", "sha256": "59a0994e9766cee08ce668b8e1b372378a751173ac9cacfadeabf85c46e6f0db", "sha256_in_prefix": "59a0994e9766cee08ce668b8e1b372378a751173ac9cacfadeabf85c46e6f0db", "size_in_bytes": 31786}, {"_path": "Lib/site-packages/PIL/JpegPresets.py", "path_type": "hardlink", "sha256": "967a961e8e032c81c8ba571d1e9d0d27b0967b11edf13df0e759082a52df9080", "sha256_in_prefix": "967a961e8e032c81c8ba571d1e9d0d27b0967b11edf13df0e759082a52df9080", "size_in_bytes": 12379}, {"_path": "Lib/site-packages/PIL/McIdasImagePlugin.py", "path_type": "hardlink", "sha256": "6da388903f822087828011537fc928b3ddbc3ca06e094a9861adfcbb75844bff", "sha256_in_prefix": "6da388903f822087828011537fc928b3ddbc3ca06e094a9861adfcbb75844bff", "size_in_bytes": 1877}, {"_path": "Lib/site-packages/PIL/MicImagePlugin.py", "path_type": "hardlink", "sha256": "6a8230916572aff5fe74fbc1ea57593891776bd91dfce7ae116dec6b2e58d103", "sha256_in_prefix": "6a8230916572aff5fe74fbc1ea57593891776bd91dfce7ae116dec6b2e58d103", "size_in_bytes": 2564}, {"_path": "Lib/site-packages/PIL/MpegImagePlugin.py", "path_type": "hardlink", "sha256": "83b05977dde45a9162e35486ff0285a22d3210fb22a08e248f8e5bd85fb756bc", "sha256_in_prefix": "83b05977dde45a9162e35486ff0285a22d3210fb22a08e248f8e5bd85fb756bc", "size_in_bytes": 2010}, {"_path": "Lib/site-packages/PIL/MpoImagePlugin.py", "path_type": "hardlink", "sha256": "4b8e6ab7b39c63bac18d8970124d27526123e483aee73f0a54ba34eba5754411", "sha256_in_prefix": "4b8e6ab7b39c63bac18d8970124d27526123e483aee73f0a54ba34eba5754411", "size_in_bytes": 6722}, {"_path": "Lib/site-packages/PIL/MspImagePlugin.py", "path_type": "hardlink", "sha256": "a3193f30b503bf3278243b8e6421e49aa38f5e7886e363c73b2346c1eeb4b256", "sha256_in_prefix": "a3193f30b503bf3278243b8e6421e49aa38f5e7886e363c73b2346c1eeb4b256", "size_in_bytes": 5892}, {"_path": "Lib/site-packages/PIL/PSDraw.py", "path_type": "hardlink", "sha256": "28c0468f7bd7685a5b95a21c03d2a31454e975a978d40420818f94833aa83244", "sha256_in_prefix": "28c0468f7bd7685a5b95a21c03d2a31454e975a978d40420818f94833aa83244", "size_in_bytes": 6918}, {"_path": "Lib/site-packages/PIL/PaletteFile.py", "path_type": "hardlink", "sha256": "b2e0dd00be953258d7c38a049f5be14ede03438bdba481c6777038a313a013ab", "sha256_in_prefix": "b2e0dd00be953258d7c38a049f5be14ede03438bdba481c6777038a313a013ab", "size_in_bytes": 1216}, {"_path": "Lib/site-packages/PIL/PalmImagePlugin.py", "path_type": "hardlink", "sha256": "589d5bf08d714d20176032612299151422eed8b9696e2043e5dd47afe9b6974f", "sha256_in_prefix": "589d5bf08d714d20176032612299151422eed8b9696e2043e5dd47afe9b6974f", "size_in_bytes": 8748}, {"_path": "Lib/site-packages/PIL/PcdImagePlugin.py", "path_type": "hardlink", "sha256": "570799d74f0704778d11fb28136e84391e24b71a8d1923963a7779f03852f05a", "sha256_in_prefix": "570799d74f0704778d11fb28136e84391e24b71a8d1923963a7779f03852f05a", "size_in_bytes": 1601}, {"_path": "Lib/site-packages/PIL/PcfFontFile.py", "path_type": "hardlink", "sha256": "34f650d1791b181f2e4e51aa82620f1a4c2e2cc601772903795baf160202ec95", "sha256_in_prefix": "34f650d1791b181f2e4e51aa82620f1a4c2e2cc601772903795baf160202ec95", "size_in_bytes": 7147}, {"_path": "Lib/site-packages/PIL/PcxImagePlugin.py", "path_type": "hardlink", "sha256": "d9daa78d18d22db8e6f0ea6e6f8b194613887582dd9f7cbb43f11357c12688e4", "sha256_in_prefix": "d9daa78d18d22db8e6f0ea6e6f8b194613887582dd9f7cbb43f11357c12688e4", "size_in_bytes": 6224}, {"_path": "Lib/site-packages/PIL/PdfImagePlugin.py", "path_type": "hardlink", "sha256": "01b240d9fe2acc7f06d6895f9a4d7c4b3425731dd6b22a546037396dc47c5af9", "sha256_in_prefix": "01b240d9fe2acc7f06d6895f9a4d7c4b3425731dd6b22a546037396dc47c5af9", "size_in_bytes": 9349}, {"_path": "Lib/site-packages/PIL/PdfParser.py", "path_type": "hardlink", "sha256": "2e7997d029bb650c06901d6e60fe2bbd7654904466511ce6628efcce37aae952", "sha256_in_prefix": "2e7997d029bb650c06901d6e60fe2bbd7654904466511ce6628efcce37aae952", "size_in_bytes": 37987}, {"_path": "Lib/site-packages/PIL/PixarImagePlugin.py", "path_type": "hardlink", "sha256": "97fe06c017749804e72176096f09a6383536bcfef07b02eee8146f887081d842", "sha256_in_prefix": "97fe06c017749804e72176096f09a6383536bcfef07b02eee8146f887081d842", "size_in_bytes": 1758}, {"_path": "Lib/site-packages/PIL/PngImagePlugin.py", "path_type": "hardlink", "sha256": "8cf04da99e74b71147588b038a471d91e7817cb358d4fc53e70cdc3cc7267264", "sha256_in_prefix": "8cf04da99e74b71147588b038a471d91e7817cb358d4fc53e70cdc3cc7267264", "size_in_bytes": 51117}, {"_path": "Lib/site-packages/PIL/PpmImagePlugin.py", "path_type": "hardlink", "sha256": "40933e57ea1d57bc3ea6b03b0796cb45073291d0b877b389e4105b0af3cf2336", "sha256_in_prefix": "40933e57ea1d57bc3ea6b03b0796cb45073291d0b877b389e4105b0af3cf2336", "size_in_bytes": 12370}, {"_path": "Lib/site-packages/PIL/PsdImagePlugin.py", "path_type": "hardlink", "sha256": "2269cd446e1500db361804d754407943ecb2d49b2473a5d1551b59622d9f00b8", "sha256_in_prefix": "2269cd446e1500db361804d754407943ecb2d49b2473a5d1551b59622d9f00b8", "size_in_bytes": 8685}, {"_path": "Lib/site-packages/PIL/QoiImagePlugin.py", "path_type": "hardlink", "sha256": "44f3badd0b201c0b323e973187bca678c6259e356ee604f910ba2592f26dd2f7", "sha256_in_prefix": "44f3badd0b201c0b323e973187bca678c6259e356ee604f910ba2592f26dd2f7", "size_in_bytes": 8572}, {"_path": "Lib/site-packages/PIL/SgiImagePlugin.py", "path_type": "hardlink", "sha256": "dd097cf6cf2fc9c3568dcc49c0cc36f2292c57d623db15a828143a7390c75c18", "sha256_in_prefix": "dd097cf6cf2fc9c3568dcc49c0cc36f2292c57d623db15a828143a7390c75c18", "size_in_bytes": 6389}, {"_path": "Lib/site-packages/PIL/SpiderImagePlugin.py", "path_type": "hardlink", "sha256": "06c83aa5f64c72d6acd71631fffa0bf9966c79445e65d9cbcb96be68a1098691", "sha256_in_prefix": "06c83aa5f64c72d6acd71631fffa0bf9966c79445e65d9cbcb96be68a1098691", "size_in_bytes": 10249}, {"_path": "Lib/site-packages/PIL/SunImagePlugin.py", "path_type": "hardlink", "sha256": "1ddc64864d29c69046c5884f25f0832f0b1870ed4a8f18eda6534c15889b22f9", "sha256_in_prefix": "1ddc64864d29c69046c5884f25f0832f0b1870ed4a8f18eda6534c15889b22f9", "size_in_bytes": 4589}, {"_path": "Lib/site-packages/PIL/TarIO.py", "path_type": "hardlink", "sha256": "06a6140a10816fd17b4a1fae67cea2cf50eda32d83d286cdc069bae73d6b75cd", "sha256_in_prefix": "06a6140a10816fd17b4a1fae67cea2cf50eda32d83d286cdc069bae73d6b75cd", "size_in_bytes": 1442}, {"_path": "Lib/site-packages/PIL/TgaImagePlugin.py", "path_type": "hardlink", "sha256": "daf0ec1537015011f0d55f34c2956fe2d8292c36cfafac951e2e85bdaa9fd076", "sha256_in_prefix": "daf0ec1537015011f0d55f34c2956fe2d8292c36cfafac951e2e85bdaa9fd076", "size_in_bytes": 6980}, {"_path": "Lib/site-packages/PIL/TiffImagePlugin.py", "path_type": "hardlink", "sha256": "b65175fe7197856155dcf51cc3b060680f32c2ed483071cc74664ef0605ef305", "sha256_in_prefix": "b65175fe7197856155dcf51cc3b060680f32c2ed483071cc74664ef0605ef305", "size_in_bytes": 85158}, {"_path": "Lib/site-packages/PIL/TiffTags.py", "path_type": "hardlink", "sha256": "fa06d72d9e6b9470fa72bc2d63a4e469f0e6dad6a695ce6ff1eec58d2f0f7088", "sha256_in_prefix": "fa06d72d9e6b9470fa72bc2d63a4e469f0e6dad6a695ce6ff1eec58d2f0f7088", "size_in_bytes": 17082}, {"_path": "Lib/site-packages/PIL/WalImageFile.py", "path_type": "hardlink", "sha256": "2dfbaafd667f57fa27c2e71f51ce8659fbd8ef3fcae2cfb911d6901aeff60c3e", "sha256_in_prefix": "2dfbaafd667f57fa27c2e71f51ce8659fbd8ef3fcae2cfb911d6901aeff60c3e", "size_in_bytes": 5704}, {"_path": "Lib/site-packages/PIL/WebPImagePlugin.py", "path_type": "hardlink", "sha256": "6055a8ebf158052af301fe9731b9ab178611b51e31eed61e702585ec4035dd64", "sha256_in_prefix": "6055a8ebf158052af301fe9731b9ab178611b51e31eed61e702585ec4035dd64", "size_in_bytes": 10010}, {"_path": "Lib/site-packages/PIL/WmfImagePlugin.py", "path_type": "hardlink", "sha256": "6758731ae1c6b74f2d04bb3180157b65538b7503f2903318778446930d49f2bc", "sha256_in_prefix": "6758731ae1c6b74f2d04bb3180157b65538b7503f2903318778446930d49f2bc", "size_in_bytes": 5243}, {"_path": "Lib/site-packages/PIL/XVThumbImagePlugin.py", "path_type": "hardlink", "sha256": "70949aa6405ab05b75d4ee97617c6a73203e9e3c40e410f7c0784d8fa542ec59", "sha256_in_prefix": "70949aa6405ab05b75d4ee97617c6a73203e9e3c40e410f7c0784d8fa542ec59", "size_in_bytes": 2115}, {"_path": "Lib/site-packages/PIL/XbmImagePlugin.py", "path_type": "hardlink", "sha256": "15de86543128ef79f2148080dd9df0e0b8e4c28656be11c1eab2829b9c95ae1a", "sha256_in_prefix": "15de86543128ef79f2148080dd9df0e0b8e4c28656be11c1eab2829b9c95ae1a", "size_in_bytes": 2669}, {"_path": "Lib/site-packages/PIL/XpmImagePlugin.py", "path_type": "hardlink", "sha256": "8ed50a6af242630200b09689c12c7cbc9b31d684db0b2c1f0f178f10d980f77c", "sha256_in_prefix": "8ed50a6af242630200b09689c12c7cbc9b31d684db0b2c1f0f178f10d980f77c", "size_in_bytes": 4400}, {"_path": "Lib/site-packages/PIL/__init__.py", "path_type": "hardlink", "sha256": "43828e12947b4bf5ec8f7d1fbceb2f47de311295f8294b15794c1a54fd5f53cd", "sha256_in_prefix": "43828e12947b4bf5ec8f7d1fbceb2f47de311295f8294b15794c1a54fd5f53cd", "size_in_bytes": 2031}, {"_path": "Lib/site-packages/PIL/__main__.py", "path_type": "hardlink", "sha256": "2e98f8bde7fc988ee3035b1109402855869e78f0ff51cf3df3117973b5cbc750", "sha256_in_prefix": "2e98f8bde7fc988ee3035b1109402855869e78f0ff51cf3df3117973b5cbc750", "size_in_bytes": 133}, {"_path": "Lib/site-packages/PIL/__pycache__/AvifImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "05fc0c3166eebe45c0f5e25ffdfcaa0d64426600f41dde0839d83fbadd5430fa", "sha256_in_prefix": "05fc0c3166eebe45c0f5e25ffdfcaa0d64426600f41dde0839d83fbadd5430fa", "size_in_bytes": 6538}, {"_path": "Lib/site-packages/PIL/__pycache__/BdfFontFile.cpython-310.pyc", "path_type": "hardlink", "sha256": "6e983cc908c26b516dada846d85789b80dca2129a774b2da892f8351a4f4df16", "sha256_in_prefix": "6e983cc908c26b516dada846d85789b80dca2129a774b2da892f8351a4f4df16", "size_in_bytes": 2504}, {"_path": "Lib/site-packages/PIL/__pycache__/BlpImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "dd6964f0e19816740d1cc3bc1af3ee831682986f313825dcaedd2b9c4b0ea1de", "sha256_in_prefix": "dd6964f0e19816740d1cc3bc1af3ee831682986f313825dcaedd2b9c4b0ea1de", "size_in_bytes": 13385}, {"_path": "Lib/site-packages/PIL/__pycache__/BmpImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "2e7fa36eeefb7f8917de0538caf687b39a1b03cd05d3f66935fc247c6e966e73", "sha256_in_prefix": "2e7fa36eeefb7f8917de0538caf687b39a1b03cd05d3f66935fc247c6e966e73", "size_in_bytes": 9833}, {"_path": "Lib/site-packages/PIL/__pycache__/BufrStubImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "e6f993a8765631ea9d966e46886ad3b9b1a805e0d9f75bae2797f6b9e1cca700", "sha256_in_prefix": "e6f993a8765631ea9d966e46886ad3b9b1a805e0d9f75bae2797f6b9e1cca700", "size_in_bytes": 1964}, {"_path": "Lib/site-packages/PIL/__pycache__/ContainerIO.cpython-310.pyc", "path_type": "hardlink", "sha256": "7b04c7caf869810d874ab2f2941b1f7b50c9049ea9df7ddebe79e87d34f12e9b", "sha256_in_prefix": "7b04c7caf869810d874ab2f2941b1f7b50c9049ea9df7ddebe79e87d34f12e9b", "size_in_bytes": 5518}, {"_path": "Lib/site-packages/PIL/__pycache__/CurImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "3dfa13016ea37d69c294d1200fe112d7dbce8a53d9aa75b514d5412c7e3d183c", "sha256_in_prefix": "3dfa13016ea37d69c294d1200fe112d7dbce8a53d9aa75b514d5412c7e3d183c", "size_in_bytes": 1469}, {"_path": "Lib/site-packages/PIL/__pycache__/DcxImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "8ffae5fa22f22a4c1cc484669a9e6d45db9af8068a86d3f7192716b9b2d183c2", "sha256_in_prefix": "8ffae5fa22f22a4c1cc484669a9e6d45db9af8068a86d3f7192716b9b2d183c2", "size_in_bytes": 1811}, {"_path": "Lib/site-packages/PIL/__pycache__/DdsImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "aecd8454a8236be4f060ac46573e9f3927fdaf6461050c863ab89a5ec13fd18a", "sha256_in_prefix": "aecd8454a8236be4f060ac46573e9f3927fdaf6461050c863ab89a5ec13fd18a", "size_in_bytes": 14695}, {"_path": "Lib/site-packages/PIL/__pycache__/EpsImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "6e9210aed8805c3fc04643bc83a57c6787ca0256d51fde43e25d800c5850cf40", "sha256_in_prefix": "6e9210aed8805c3fc04643bc83a57c6787ca0256d51fde43e25d800c5850cf40", "size_in_bytes": 9187}, {"_path": "Lib/site-packages/PIL/__pycache__/ExifTags.cpython-310.pyc", "path_type": "hardlink", "sha256": "d33c61e682e9f52c7684aadc131bcb1802242d1e164357cd1d2a0dc20de942e6", "sha256_in_prefix": "d33c61e682e9f52c7684aadc131bcb1802242d1e164357cd1d2a0dc20de942e6", "size_in_bytes": 10237}, {"_path": "Lib/site-packages/PIL/__pycache__/FitsImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "4de9932b1cfd700c5d245258fdbd78255ff13b58daf492b5a7b57250cb5a6a50", "sha256_in_prefix": "4de9932b1cfd700c5d245258fdbd78255ff13b58daf492b5a7b57250cb5a6a50", "size_in_bytes": 3745}, {"_path": "Lib/site-packages/PIL/__pycache__/FliImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "f456ad7b5c6aba89b6055565b47066e9872935e66f402b988504ab9fce1d13bd", "sha256_in_prefix": "f456ad7b5c6aba89b6055565b47066e9872935e66f402b988504ab9fce1d13bd", "size_in_bytes": 3984}, {"_path": "Lib/site-packages/PIL/__pycache__/FontFile.cpython-310.pyc", "path_type": "hardlink", "sha256": "e856a4bd31dd1a92ac65113629b665d534b418aa2502b99b4d09477093e910f2", "sha256_in_prefix": "e856a4bd31dd1a92ac65113629b665d534b418aa2502b99b4d09477093e910f2", "size_in_bytes": 2863}, {"_path": "Lib/site-packages/PIL/__pycache__/FpxImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "331fa52d7f8a6f82e08bc0428be31271f507c5c53cb718578b5ad21c36a50193", "sha256_in_prefix": "331fa52d7f8a6f82e08bc0428be31271f507c5c53cb718578b5ad21c36a50193", "size_in_bytes": 4561}, {"_path": "Lib/site-packages/PIL/__pycache__/FtexImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "91d95e2f4f1368979a802b888c7bd8f897f0356f6ed5059585425769de59eb3b", "sha256_in_prefix": "91d95e2f4f1368979a802b888c7bd8f897f0356f6ed5059585425769de59eb3b", "size_in_bytes": 3826}, {"_path": "Lib/site-packages/PIL/__pycache__/GbrImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "aed55cc520780f6c41e767115a2473bfeca506a49f99d72452ed9dab68bdcd7a", "sha256_in_prefix": "aed55cc520780f6c41e767115a2473bfeca506a49f99d72452ed9dab68bdcd7a", "size_in_bytes": 2049}, {"_path": "Lib/site-packages/PIL/__pycache__/GdImageFile.cpython-310.pyc", "path_type": "hardlink", "sha256": "ae275ae3de322317bde9a24d41d44f8f1aebbc30cf7f736d9b86d7a75edec2b9", "sha256_in_prefix": "ae275ae3de322317bde9a24d41d44f8f1aebbc30cf7f736d9b86d7a75edec2b9", "size_in_bytes": 2691}, {"_path": "Lib/site-packages/PIL/__pycache__/GifImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "6e28238ae6d18ff2d02c2d6c52a3df237a02c2b81627be3669a6c668e6fb0179", "sha256_in_prefix": "6e28238ae6d18ff2d02c2d6c52a3df237a02c2b81627be3669a6c668e6fb0179", "size_in_bytes": 23755}, {"_path": "Lib/site-packages/PIL/__pycache__/GimpGradientFile.cpython-310.pyc", "path_type": "hardlink", "sha256": "22852945b3d627485ba2182cb5aecfd5d42330941abd5254f3d66afea3b27ad0", "sha256_in_prefix": "22852945b3d627485ba2182cb5aecfd5d42330941abd5254f3d66afea3b27ad0", "size_in_bytes": 3655}, {"_path": "Lib/site-packages/PIL/__pycache__/GimpPaletteFile.cpython-310.pyc", "path_type": "hardlink", "sha256": "4e1d488b1b093b0f0f489e0e9d74c30aa98d36b7636ad06b609cd5cab9e475c9", "sha256_in_prefix": "4e1d488b1b093b0f0f489e0e9d74c30aa98d36b7636ad06b609cd5cab9e475c9", "size_in_bytes": 1955}, {"_path": "Lib/site-packages/PIL/__pycache__/GribStubImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "1f1c76b2f2600567eeb3fda69582ad17dd5d873d0dfdd1d4dc4d28df834d9e3e", "sha256_in_prefix": "1f1c76b2f2600567eeb3fda69582ad17dd5d873d0dfdd1d4dc4d28df834d9e3e", "size_in_bytes": 1975}, {"_path": "Lib/site-packages/PIL/__pycache__/Hdf5StubImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "045a4ba72edfebc9ce6ee9fa53bd2d60621c7052ad3ea0064eeca3d2188bf59c", "sha256_in_prefix": "045a4ba72edfebc9ce6ee9fa53bd2d60621c7052ad3ea0064eeca3d2188bf59c", "size_in_bytes": 1966}, {"_path": "Lib/site-packages/PIL/__pycache__/IcnsImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "5e938823bedb8fb15810b85538bd31296f83e1d42a0b6ddf181c7b753556269c", "sha256_in_prefix": "5e938823bedb8fb15810b85538bd31296f83e1d42a0b6ddf181c7b753556269c", "size_in_bytes": 10267}, {"_path": "Lib/site-packages/PIL/__pycache__/IcoImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "dbe615c2411cee1d628208f9e8418fc2b02218197b89ab4a095704ff9939dd51", "sha256_in_prefix": "dbe615c2411cee1d628208f9e8418fc2b02218197b89ab4a095704ff9939dd51", "size_in_bytes": 8671}, {"_path": "Lib/site-packages/PIL/__pycache__/ImImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "b96704fa99affddfb9161e2777446b0667827aec2cad03f612d3b1f2282f309c", "sha256_in_prefix": "b96704fa99affddfb9161e2777446b0667827aec2cad03f612d3b1f2282f309c", "size_in_bytes": 7132}, {"_path": "Lib/site-packages/PIL/__pycache__/Image.cpython-310.pyc", "path_type": "hardlink", "sha256": "bbb52df4fb47ec3d53adfb199889f1893e6cb0e47d75b050542d2f9d2ea64603", "sha256_in_prefix": "bbb52df4fb47ec3d53adfb199889f1893e6cb0e47d75b050542d2f9d2ea64603", "size_in_bytes": 122476}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageChops.cpython-310.pyc", "path_type": "hardlink", "sha256": "46023a13ea3e253141c7fae4142cffa28900a9bf699d66679041e8fa2f98ef2d", "sha256_in_prefix": "46023a13ea3e253141c7fae4142cffa28900a9bf699d66679041e8fa2f98ef2d", "size_in_bytes": 7343}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageCms.cpython-310.pyc", "path_type": "hardlink", "sha256": "73ed5b2da51a37c33e414d70eafd2d07c2bd45de45afd5dc57b5889fd1d43aa5", "sha256_in_prefix": "73ed5b2da51a37c33e414d70eafd2d07c2bd45de45afd5dc57b5889fd1d43aa5", "size_in_bytes": 35847}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageColor.cpython-310.pyc", "path_type": "hardlink", "sha256": "9b184c9c792d79a83cddcc003994a3cd128618026a8b4af4f9025e3cbfa41747", "sha256_in_prefix": "9b184c9c792d79a83cddcc003994a3cd128618026a8b4af4f9025e3cbfa41747", "size_in_bytes": 8135}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageDraw.cpython-310.pyc", "path_type": "hardlink", "sha256": "91270ba608042730a580e1988517f3c88110e081dffdf42196cb5ac4daf14a42", "sha256_in_prefix": "91270ba608042730a580e1988517f3c88110e081dffdf42196cb5ac4daf14a42", "size_in_bytes": 27356}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageDraw2.cpython-310.pyc", "path_type": "hardlink", "sha256": "1a3a0b1d06cab9c7fcc8a6081f690f147a18e82f35ff8a79af0eaa71cfd28884", "sha256_in_prefix": "1a3a0b1d06cab9c7fcc8a6081f690f147a18e82f35ff8a79af0eaa71cfd28884", "size_in_bytes": 7202}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageEnhance.cpython-310.pyc", "path_type": "hardlink", "sha256": "3beb6dd94baa2ed88cb87c29fc24db44886c65bac5763a0336502f514af1b4bf", "sha256_in_prefix": "3beb6dd94baa2ed88cb87c29fc24db44886c65bac5763a0336502f514af1b4bf", "size_in_bytes": 3754}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageFile.cpython-310.pyc", "path_type": "hardlink", "sha256": "506eac4ae1e562ee3cad31786553f5f608db988e269a1251af52ed80ac9d073d", "sha256_in_prefix": "506eac4ae1e562ee3cad31786553f5f608db988e269a1251af52ed80ac9d073d", "size_in_bytes": 23036}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageFilter.cpython-310.pyc", "path_type": "hardlink", "sha256": "f31481b0c121bd6c55e3deeac0b66348654a73816e35de9e2c58c6d035e499d9", "sha256_in_prefix": "f31481b0c121bd6c55e3deeac0b66348654a73816e35de9e2c58c6d035e499d9", "size_in_bytes": 18239}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageFont.cpython-310.pyc", "path_type": "hardlink", "sha256": "359924494dd3bffef82a1aebd30862d27545a941ab3362d24af95c3ee13eca80", "sha256_in_prefix": "359924494dd3bffef82a1aebd30862d27545a941ab3362d24af95c3ee13eca80", "size_in_bytes": 60452}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageGrab.cpython-310.pyc", "path_type": "hardlink", "sha256": "12f8144d39af89fb304d7b3d12e187439dbf8c8522047a7c54ee6a51e7a5fb4c", "sha256_in_prefix": "12f8144d39af89fb304d7b3d12e187439dbf8c8522047a7c54ee6a51e7a5fb4c", "size_in_bytes": 3814}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageMath.cpython-310.pyc", "path_type": "hardlink", "sha256": "d9b38b6deb38d1bfea83093d5dc965c5fb53777f4027f37c510102c3ce5f3150", "sha256_in_prefix": "d9b38b6deb38d1bfea83093d5dc965c5fb53777f4027f37c510102c3ce5f3150", "size_in_bytes": 11129}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageMode.cpython-310.pyc", "path_type": "hardlink", "sha256": "68dd49064866859a17c2322e05077f0135a4f896992314ad1bff5380c7d93411", "sha256_in_prefix": "68dd49064866859a17c2322e05077f0135a4f896992314ad1bff5380c7d93411", "size_in_bytes": 2262}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageMorph.cpython-310.pyc", "path_type": "hardlink", "sha256": "2de3117d0319527fb7bc9d8ea8f9f5c259b64d788dccb7524db2be4b7ef6a492", "sha256_in_prefix": "2de3117d0319527fb7bc9d8ea8f9f5c259b64d788dccb7524db2be4b7ef6a492", "size_in_bytes": 8009}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageOps.cpython-310.pyc", "path_type": "hardlink", "sha256": "5e70a5563f2cb13843b5b38e6b38b024b69f4fb689914a572c001ecf329ed28c", "sha256_in_prefix": "5e70a5563f2cb13843b5b38e6b38b024b69f4fb689914a572c001ecf329ed28c", "size_in_bytes": 20642}, {"_path": "Lib/site-packages/PIL/__pycache__/ImagePalette.cpython-310.pyc", "path_type": "hardlink", "sha256": "843ac39bcd995aede418d0955221c105c961cece63df86f573cd63847d25b75f", "sha256_in_prefix": "843ac39bcd995aede418d0955221c105c961cece63df86f573cd63847d25b75f", "size_in_bytes": 8382}, {"_path": "Lib/site-packages/PIL/__pycache__/ImagePath.cpython-310.pyc", "path_type": "hardlink", "sha256": "1ada2efc76f4bfa085fa840120592bcdccc46a5563e0ff9898527ee63aed52c2", "sha256_in_prefix": "1ada2efc76f4bfa085fa840120592bcdccc46a5563e0ff9898527ee63aed52c2", "size_in_bytes": 236}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageQt.cpython-310.pyc", "path_type": "hardlink", "sha256": "7b579b2ec2cf3b81ebab09b9bf6290491c56dab67125ec4883a27e5b9ebb7236", "sha256_in_prefix": "7b579b2ec2cf3b81ebab09b9bf6290491c56dab67125ec4883a27e5b9ebb7236", "size_in_bytes": 5744}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageSequence.cpython-310.pyc", "path_type": "hardlink", "sha256": "90ed71b73678f83f35107b9b7ba835bb3e69eacb4c0ea7d81e5b9dec26af9c27", "sha256_in_prefix": "90ed71b73678f83f35107b9b7ba835bb3e69eacb4c0ea7d81e5b9dec26af9c27", "size_in_bytes": 2726}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageShow.cpython-310.pyc", "path_type": "hardlink", "sha256": "2b9790dbbe1a1cba548269c02adeac43f4379332c0d9fa026a8c283a7158f67f", "sha256_in_prefix": "2b9790dbbe1a1cba548269c02adeac43f4379332c0d9fa026a8c283a7158f67f", "size_in_bytes": 10087}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageStat.cpython-310.pyc", "path_type": "hardlink", "sha256": "bcafa72c18ec31b052ee00de512932bd5519c797489c2ea35e85235372d4374a", "sha256_in_prefix": "bcafa72c18ec31b052ee00de512932bd5519c797489c2ea35e85235372d4374a", "size_in_bytes": 5714}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageTk.cpython-310.pyc", "path_type": "hardlink", "sha256": "0fb3fb61ac157933b506977b78771610d5c3c4d1ab9a1dca93086c97551636d2", "sha256_in_prefix": "0fb3fb61ac157933b506977b78771610d5c3c4d1ab9a1dca93086c97551636d2", "size_in_bytes": 6897}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageTransform.cpython-310.pyc", "path_type": "hardlink", "sha256": "1573b69ffc14944fd9e58e9cb894fb195579bdc899db4543070ea0e96f742d14", "sha256_in_prefix": "1573b69ffc14944fd9e58e9cb894fb195579bdc899db4543070ea0e96f742d14", "size_in_bytes": 4655}, {"_path": "Lib/site-packages/PIL/__pycache__/ImageWin.cpython-310.pyc", "path_type": "hardlink", "sha256": "1c4548223a740c20488e8bfd4dafbbdd0ba9e9a7a5489b721e8550b18ed545ae", "sha256_in_prefix": "1c4548223a740c20488e8bfd4dafbbdd0ba9e9a7a5489b721e8550b18ed545ae", "size_in_bytes": 9217}, {"_path": "Lib/site-packages/PIL/__pycache__/ImtImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "84a148f7855ce28b1a877141942f74419e5bc411b7ea30eaf0b5afd747a94a0f", "sha256_in_prefix": "84a148f7855ce28b1a877141942f74419e5bc411b7ea30eaf0b5afd747a94a0f", "size_in_bytes": 1441}, {"_path": "Lib/site-packages/PIL/__pycache__/IptcImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "0dbce8db10cd7fed5ab72ffac4d2bd8ba2587387711206c11cfdb5ca27068257", "sha256_in_prefix": "0dbce8db10cd7fed5ab72ffac4d2bd8ba2587387711206c11cfdb5ca27068257", "size_in_bytes": 5497}, {"_path": "Lib/site-packages/PIL/__pycache__/Jpeg2KImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "e6463f81509a0f0a7c2d04434db742c25279cf96f71ba065a5d0e2b9a88099b7", "sha256_in_prefix": "e6463f81509a0f0a7c2d04434db742c25279cf96f71ba065a5d0e2b9a88099b7", "size_in_bytes": 10424}, {"_path": "Lib/site-packages/PIL/__pycache__/JpegImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "5697e71d9a638dd722f33a2ce3c7e77e9ef93f8510855296eaed43bb09d99650", "sha256_in_prefix": "5697e71d9a638dd722f33a2ce3c7e77e9ef93f8510855296eaed43bb09d99650", "size_in_bytes": 20319}, {"_path": "Lib/site-packages/PIL/__pycache__/JpegPresets.cpython-310.pyc", "path_type": "hardlink", "sha256": "2927e047902bcfc68cb981fb5b4821d3a1987dd72bb91c4428947bae737f37ed", "sha256_in_prefix": "2927e047902bcfc68cb981fb5b4821d3a1987dd72bb91c4428947bae737f37ed", "size_in_bytes": 7901}, {"_path": "Lib/site-packages/PIL/__pycache__/McIdasImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "58733c0bb5e3dff747df22b50df02e57c8b259f3b45eecd6f941993e42973a16", "sha256_in_prefix": "58733c0bb5e3dff747df22b50df02e57c8b259f3b45eecd6f941993e42973a16", "size_in_bytes": 1478}, {"_path": "Lib/site-packages/PIL/__pycache__/MicImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "7d9bce81845b9298b306d845e48caec22b1ac08ab6c0c8b8c10edd4a9835e1e3", "sha256_in_prefix": "7d9bce81845b9298b306d845e48caec22b1ac08ab6c0c8b8c10edd4a9835e1e3", "size_in_bytes": 2426}, {"_path": "Lib/site-packages/PIL/__pycache__/MpegImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "908c59bb9971215dc0615cadb7a49891ded971b62f34bae42dbe45527c3099ab", "sha256_in_prefix": "908c59bb9971215dc0615cadb7a49891ded971b62f34bae42dbe45527c3099ab", "size_in_bytes": 2331}, {"_path": "Lib/site-packages/PIL/__pycache__/MpoImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "68f1f80a42d6d1a6af4e82381a5dac3a33f05c6283b06ecfeb8fbb50f958c30a", "sha256_in_prefix": "68f1f80a42d6d1a6af4e82381a5dac3a33f05c6283b06ecfeb8fbb50f958c30a", "size_in_bytes": 5178}, {"_path": "Lib/site-packages/PIL/__pycache__/MspImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "283c5494e3287ff64e26b6c98062a20ba2db2b8b4d4254b55db2f5b7f6a70e39", "sha256_in_prefix": "283c5494e3287ff64e26b6c98062a20ba2db2b8b4d4254b55db2f5b7f6a70e39", "size_in_bytes": 3435}, {"_path": "Lib/site-packages/PIL/__pycache__/PSDraw.cpython-310.pyc", "path_type": "hardlink", "sha256": "0d308f4e9b2b60344caaf44dcc250a5a6194bbbb9dc8308229b4a4790e90cfd1", "sha256_in_prefix": "0d308f4e9b2b60344caaf44dcc250a5a6194bbbb9dc8308229b4a4790e90cfd1", "size_in_bytes": 6090}, {"_path": "Lib/site-packages/PIL/__pycache__/PaletteFile.cpython-310.pyc", "path_type": "hardlink", "sha256": "b18d88db3f379ae6a75f915adfaa1570aceccc6a583da1ee7eacce7a2ef3b600", "sha256_in_prefix": "b18d88db3f379ae6a75f915adfaa1570aceccc6a583da1ee7eacce7a2ef3b600", "size_in_bytes": 1509}, {"_path": "Lib/site-packages/PIL/__pycache__/PalmImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "cc54bff5b73b4088bcec1c7353b8c4113b6c09a702757a12ca6f8674a16cb377", "sha256_in_prefix": "cc54bff5b73b4088bcec1c7353b8c4113b6c09a702757a12ca6f8674a16cb377", "size_in_bytes": 6751}, {"_path": "Lib/site-packages/PIL/__pycache__/PcdImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "86b094a1cb456ef31fd75dc4b493e05af1d16884827f869fb318ad0d977b85a0", "sha256_in_prefix": "86b094a1cb456ef31fd75dc4b493e05af1d16884827f869fb318ad0d977b85a0", "size_in_bytes": 1252}, {"_path": "Lib/site-packages/PIL/__pycache__/PcfFontFile.cpython-310.pyc", "path_type": "hardlink", "sha256": "96f040421bdf824208025348fe75b18766ec264aae5e1b6740a25c92c44820e2", "sha256_in_prefix": "96f040421bdf824208025348fe75b18766ec264aae5e1b6740a25c92c44820e2", "size_in_bytes": 6083}, {"_path": "Lib/site-packages/PIL/__pycache__/PcxImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "52eb41bc261ec100aeaa08d498a8495bed044d090540a7ed1d7962e24253e3fd", "sha256_in_prefix": "52eb41bc261ec100aeaa08d498a8495bed044d090540a7ed1d7962e24253e3fd", "size_in_bytes": 4011}, {"_path": "Lib/site-packages/PIL/__pycache__/PdfImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "2ff6df08777a98a6bd996d7717d76ea15ce0520febccf3f47c1ced3cb0b69a68", "sha256_in_prefix": "2ff6df08777a98a6bd996d7717d76ea15ce0520febccf3f47c1ced3cb0b69a68", "size_in_bytes": 5007}, {"_path": "Lib/site-packages/PIL/__pycache__/PdfParser.cpython-310.pyc", "path_type": "hardlink", "sha256": "358343b7c5e207237edddc1e7e4c365ccba622aba45a292c67d2d07234cd0de6", "sha256_in_prefix": "358343b7c5e207237edddc1e7e4c365ccba622aba45a292c67d2d07234cd0de6", "size_in_bytes": 29788}, {"_path": "Lib/site-packages/PIL/__pycache__/PixarImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "5f43dd2d0ff5b8e4e5f898686025df1aa05389d0dc461ef260ba056e688a18ee", "sha256_in_prefix": "5f43dd2d0ff5b8e4e5f898686025df1aa05389d0dc461ef260ba056e688a18ee", "size_in_bytes": 1255}, {"_path": "Lib/site-packages/PIL/__pycache__/PngImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "a11631c19384448ecaf225284359ecccbfb7687eb22dd010984fe32799cc14b9", "sha256_in_prefix": "a11631c19384448ecaf225284359ecccbfb7687eb22dd010984fe32799cc14b9", "size_in_bytes": 34032}, {"_path": "Lib/site-packages/PIL/__pycache__/PpmImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "4c2ae41467907cfc868e8dbbf98e2a96b25cd7502b970b969f8c433b3984bced", "sha256_in_prefix": "4c2ae41467907cfc868e8dbbf98e2a96b25cd7502b970b969f8c433b3984bced", "size_in_bytes": 8305}, {"_path": "Lib/site-packages/PIL/__pycache__/PsdImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "258bbe1397fa238993cc91bf9e97099ff4ace5dd690ee0ca7e2fcb4b5894050f", "sha256_in_prefix": "258bbe1397fa238993cc91bf9e97099ff4ace5dd690ee0ca7e2fcb4b5894050f", "size_in_bytes": 6096}, {"_path": "Lib/site-packages/PIL/__pycache__/QoiImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "85915385eb935e4870f977822ebadeed588be9df1145e125ec4446ca3890e192", "sha256_in_prefix": "85915385eb935e4870f977822ebadeed588be9df1145e125ec4446ca3890e192", "size_in_bytes": 6289}, {"_path": "Lib/site-packages/PIL/__pycache__/SgiImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "405549d518258d5368c52103300e70419f32b70fe97146b892b3558b02e9c2ac", "sha256_in_prefix": "405549d518258d5368c52103300e70419f32b70fe97146b892b3558b02e9c2ac", "size_in_bytes": 4209}, {"_path": "Lib/site-packages/PIL/__pycache__/SpiderImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "e39052e5cf0d8509279205aaf3a9466def34bae41094d4661b4b6355eca70039", "sha256_in_prefix": "e39052e5cf0d8509279205aaf3a9466def34bae41094d4661b4b6355eca70039", "size_in_bytes": 7461}, {"_path": "Lib/site-packages/PIL/__pycache__/SunImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "bae9d5da37705da1fa7a32cb3c3c6e3e59aadf851a0c7a2c87b7ec98d7ea719d", "sha256_in_prefix": "bae9d5da37705da1fa7a32cb3c3c6e3e59aadf851a0c7a2c87b7ec98d7ea719d", "size_in_bytes": 2051}, {"_path": "Lib/site-packages/PIL/__pycache__/TarIO.cpython-310.pyc", "path_type": "hardlink", "sha256": "5131e92111692b2bacbe767c186158c19ddb69c20cf3f4eed29f768e4d653963", "sha256_in_prefix": "5131e92111692b2bacbe767c186158c19ddb69c20cf3f4eed29f768e4d653963", "size_in_bytes": 1304}, {"_path": "Lib/site-packages/PIL/__pycache__/TgaImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "21eedcc55790a3f3bd55239b13adae2c344e3ef20cf56362bcd5c5992d662efe", "sha256_in_prefix": "21eedcc55790a3f3bd55239b13adae2c344e3ef20cf56362bcd5c5992d662efe", "size_in_bytes": 4316}, {"_path": "Lib/site-packages/PIL/__pycache__/TiffImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "81bde6110d90ab9b90ed084c0664b2a4ec50d40f3b2ce3313e6663d091f85ae1", "sha256_in_prefix": "81bde6110d90ab9b90ed084c0664b2a4ec50d40f3b2ce3313e6663d091f85ae1", "size_in_bytes": 59224}, {"_path": "Lib/site-packages/PIL/__pycache__/TiffTags.cpython-310.pyc", "path_type": "hardlink", "sha256": "7806f6cf2468a69382b07764c4ea20e31512928e2952ac486c12a1758038ddd2", "sha256_in_prefix": "7806f6cf2468a69382b07764c4ea20e31512928e2952ac486c12a1758038ddd2", "size_in_bytes": 13797}, {"_path": "Lib/site-packages/PIL/__pycache__/WalImageFile.cpython-310.pyc", "path_type": "hardlink", "sha256": "a104405e7767707c090949a1cdee873024e21f0b2908c3d58db97a322d63bc9f", "sha256_in_prefix": "a104405e7767707c090949a1cdee873024e21f0b2908c3d58db97a322d63bc9f", "size_in_bytes": 3107}, {"_path": "Lib/site-packages/PIL/__pycache__/WebPImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "97a6aab33edfe8e849196da0a1c581f04f66c382bf3839046f051b94a4ba58b9", "sha256_in_prefix": "97a6aab33edfe8e849196da0a1c581f04f66c382bf3839046f051b94a4ba58b9", "size_in_bytes": 7291}, {"_path": "Lib/site-packages/PIL/__pycache__/WmfImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "fd1725e82788dcd8dab28c3793f43842aded68a4c15a2e2e5860521a41f9d038", "sha256_in_prefix": "fd1725e82788dcd8dab28c3793f43842aded68a4c15a2e2e5860521a41f9d038", "size_in_bytes": 4034}, {"_path": "Lib/site-packages/PIL/__pycache__/XVThumbImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "941f34bf8b8d726c1ccb3d7e36766fb4590e062e427af48c5bffb205fbfc0e3e", "sha256_in_prefix": "941f34bf8b8d726c1ccb3d7e36766fb4590e062e427af48c5bffb205fbfc0e3e", "size_in_bytes": 1562}, {"_path": "Lib/site-packages/PIL/__pycache__/XbmImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "1727cbc849e50b1a744a92329f84ae3b5800f0462ba5d3382ab3c3e806cffcfb", "sha256_in_prefix": "1727cbc849e50b1a744a92329f84ae3b5800f0462ba5d3382ab3c3e806cffcfb", "size_in_bytes": 2358}, {"_path": "Lib/site-packages/PIL/__pycache__/XpmImagePlugin.cpython-310.pyc", "path_type": "hardlink", "sha256": "cf041751a04cd560f17bda8955e8432506eb2c5082505560faf574e3eb5caaf3", "sha256_in_prefix": "cf041751a04cd560f17bda8955e8432506eb2c5082505560faf574e3eb5caaf3", "size_in_bytes": 3489}, {"_path": "Lib/site-packages/PIL/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "9f94a7e13ff4056ae3ee863c1915b17d0c4023676e94069c08ef569b52df188c", "sha256_in_prefix": "9f94a7e13ff4056ae3ee863c1915b17d0c4023676e94069c08ef569b52df188c", "size_in_bytes": 1917}, {"_path": "Lib/site-packages/PIL/__pycache__/__main__.cpython-310.pyc", "path_type": "hardlink", "sha256": "621fd96dcece3fadae199d3071140ec9dfd2a591ba2e42e2905060d91ea607fc", "sha256_in_prefix": "621fd96dcece3fadae199d3071140ec9dfd2a591ba2e42e2905060d91ea607fc", "size_in_bytes": 287}, {"_path": "Lib/site-packages/PIL/__pycache__/_binary.cpython-310.pyc", "path_type": "hardlink", "sha256": "a5f18a0b8f1daade899ee76405549e6a9ec1113c19bad358afa01e49b6119861", "sha256_in_prefix": "a5f18a0b8f1daade899ee76405549e6a9ec1113c19bad358afa01e49b6119861", "size_in_bytes": 2967}, {"_path": "Lib/site-packages/PIL/__pycache__/_deprecate.cpython-310.pyc", "path_type": "hardlink", "sha256": "4b3925e5ef8d740444cd1a09fccb909588e08d52818d0a35d6cf3d4d79800119", "sha256_in_prefix": "4b3925e5ef8d740444cd1a09fccb909588e08d52818d0a35d6cf3d4d79800119", "size_in_bytes": 2095}, {"_path": "Lib/site-packages/PIL/__pycache__/_tkinter_finder.cpython-310.pyc", "path_type": "hardlink", "sha256": "0c7a126039328a253702fe9cb104fc453d653d85891927b32d2bf0e382932547", "sha256_in_prefix": "0c7a126039328a253702fe9cb104fc453d653d85891927b32d2bf0e382932547", "size_in_bytes": 507}, {"_path": "Lib/site-packages/PIL/__pycache__/_typing.cpython-310.pyc", "path_type": "hardlink", "sha256": "2e9e5204b2184534444bae0eb657213d0dfd7a37a75f90814824b3ebc47c977d", "sha256_in_prefix": "2e9e5204b2184534444bae0eb657213d0dfd7a37a75f90814824b3ebc47c977d", "size_in_bytes": 1691}, {"_path": "Lib/site-packages/PIL/__pycache__/_util.cpython-310.pyc", "path_type": "hardlink", "sha256": "7fe083abde58bff6cba88da0b9d61cd110f558f310717bce7502e15d166ad599", "sha256_in_prefix": "7fe083abde58bff6cba88da0b9d61cd110f558f310717bce7502e15d166ad599", "size_in_bytes": 1351}, {"_path": "Lib/site-packages/PIL/__pycache__/_version.cpython-310.pyc", "path_type": "hardlink", "sha256": "5c36431078ec268bf22d40be240a790f21166c65653e749c3b6d3ce2146f1e3e", "sha256_in_prefix": "5c36431078ec268bf22d40be240a790f21166c65653e749c3b6d3ce2146f1e3e", "size_in_bytes": 199}, {"_path": "Lib/site-packages/PIL/__pycache__/features.cpython-310.pyc", "path_type": "hardlink", "sha256": "3dbf7c91067dd02cfe2ae5fb1923562189f7f9d6e9103b783da880844182a97b", "sha256_in_prefix": "3dbf7c91067dd02cfe2ae5fb1923562189f7f9d6e9103b783da880844182a97b", "size_in_bytes": 9999}, {"_path": "Lib/site-packages/PIL/__pycache__/report.cpython-310.pyc", "path_type": "hardlink", "sha256": "4423de21812a7179198db5b7f7bcfbc17d72aae1926cbbc1131da7d74683dff9", "sha256_in_prefix": "4423de21812a7179198db5b7f7bcfbc17d72aae1926cbbc1131da7d74683dff9", "size_in_bytes": 249}, {"_path": "Lib/site-packages/PIL/_avif.pyi", "path_type": "hardlink", "sha256": "ddf071712a6926be84384714a23bdf946dc47a083b96fd90a7474d41020bacfe", "sha256_in_prefix": "ddf071712a6926be84384714a23bdf946dc47a083b96fd90a7474d41020bacfe", "size_in_bytes": 63}, {"_path": "Lib/site-packages/PIL/_binary.py", "path_type": "hardlink", "sha256": "a5c33a00bd381b182619e2df707d55d4164710dc08ad01f4bb1849eebd0720bd", "sha256_in_prefix": "a5c33a00bd381b182619e2df707d55d4164710dc08ad01f4bb1849eebd0720bd", "size_in_bytes": 2550}, {"_path": "Lib/site-packages/PIL/_deprecate.py", "path_type": "hardlink", "sha256": "25825f2607a6bde75c747307ebf4454c304b369e2f489a9dfa8df67b75b33653", "sha256_in_prefix": "25825f2607a6bde75c747307ebf4454c304b369e2f489a9dfa8df67b75b33653", "size_in_bytes": 2034}, {"_path": "Lib/site-packages/PIL/_imaging.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "6fec1354e9d74d36b98884952bbc19b8c490e0af081a53796cda52f29467bc09", "sha256_in_prefix": "6fec1354e9d74d36b98884952bbc19b8c490e0af081a53796cda52f29467bc09", "size_in_bytes": 428544}, {"_path": "Lib/site-packages/PIL/_imaging.pyi", "path_type": "hardlink", "sha256": "4ad31b5d4652df601e8004cfd6c5077ece4fd390374c3fc18902ac0c74015b8d", "sha256_in_prefix": "4ad31b5d4652df601e8004cfd6c5077ece4fd390374c3fc18902ac0c74015b8d", "size_in_bytes": 868}, {"_path": "Lib/site-packages/PIL/_imagingcms.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "77d4822a7f9baeb3ec5044e64fec6be7b5fa321650ddf21d2a834c2f8e2baecf", "sha256_in_prefix": "77d4822a7f9baeb3ec5044e64fec6be7b5fa321650ddf21d2a834c2f8e2baecf", "size_in_bytes": 32256}, {"_path": "Lib/site-packages/PIL/_imagingcms.pyi", "path_type": "hardlink", "sha256": "6eba63c51a2263fd88b727d3ae384a78612c1317b818f1bedb9abd0103fc269f", "sha256_in_prefix": "6eba63c51a2263fd88b727d3ae384a78612c1317b818f1bedb9abd0103fc269f", "size_in_bytes": 4389}, {"_path": "Lib/site-packages/PIL/_imagingft.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "e85fbe7afa777a00eb8c4d3c53d3b50a4e75653a8f420fede01a449624397ce9", "sha256_in_prefix": "e85fbe7afa777a00eb8c4d3c53d3b50a4e75653a8f420fede01a449624397ce9", "size_in_bytes": 33280}, {"_path": "Lib/site-packages/PIL/_imagingft.pyi", "path_type": "hardlink", "sha256": "21874519f029c2ca988895680f90153af80cbe73b578fd49ddc300e8bd18649d", "sha256_in_prefix": "21874519f029c2ca988895680f90153af80cbe73b578fd49ddc300e8bd18649d", "size_in_bytes": 1806}, {"_path": "Lib/site-packages/PIL/_imagingmath.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "10c8182786a5b9d73f486ccceb86e6034ea3f821369ecdbfbf3d406aff830d29", "sha256_in_prefix": "10c8182786a5b9d73f486ccceb86e6034ea3f821369ecdbfbf3d406aff830d29", "size_in_bytes": 25088}, {"_path": "Lib/site-packages/PIL/_imagingmath.pyi", "path_type": "hardlink", "sha256": "ddf071712a6926be84384714a23bdf946dc47a083b96fd90a7474d41020bacfe", "sha256_in_prefix": "ddf071712a6926be84384714a23bdf946dc47a083b96fd90a7474d41020bacfe", "size_in_bytes": 63}, {"_path": "Lib/site-packages/PIL/_imagingmorph.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "c3e2256bc81a367ff4b654bd290f2c1d2d1d88b7aab73682cad3c27d23d64d2d", "sha256_in_prefix": "c3e2256bc81a367ff4b654bd290f2c1d2d1d88b7aab73682cad3c27d23d64d2d", "size_in_bytes": 13824}, {"_path": "Lib/site-packages/PIL/_imagingmorph.pyi", "path_type": "hardlink", "sha256": "ddf071712a6926be84384714a23bdf946dc47a083b96fd90a7474d41020bacfe", "sha256_in_prefix": "ddf071712a6926be84384714a23bdf946dc47a083b96fd90a7474d41020bacfe", "size_in_bytes": 63}, {"_path": "Lib/site-packages/PIL/_imagingtk.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "94b6f08ee32218740db837d2ba03d1bba14da53e12fdd7aef80100271bfa3879", "sha256_in_prefix": "94b6f08ee32218740db837d2ba03d1bba14da53e12fdd7aef80100271bfa3879", "size_in_bytes": 14848}, {"_path": "Lib/site-packages/PIL/_imagingtk.pyi", "path_type": "hardlink", "sha256": "ddf071712a6926be84384714a23bdf946dc47a083b96fd90a7474d41020bacfe", "sha256_in_prefix": "ddf071712a6926be84384714a23bdf946dc47a083b96fd90a7474d41020bacfe", "size_in_bytes": 63}, {"_path": "Lib/site-packages/PIL/_tkinter_finder.py", "path_type": "hardlink", "sha256": "188678b2d985854a2c987292addc5c8d2b62a1c0cd7f2267ed10627b6496c54d", "sha256_in_prefix": "188678b2d985854a2c987292addc5c8d2b62a1c0cd7f2267ed10627b6496c54d", "size_in_bytes": 538}, {"_path": "Lib/site-packages/PIL/_typing.py", "path_type": "hardlink", "sha256": "d4d01627b679f533fdf1c16ff6a1a904c8121dbc91e02001c8b8cc4516d26716", "sha256_in_prefix": "d4d01627b679f533fdf1c16ff6a1a904c8121dbc91e02001c8b8cc4516d26716", "size_in_bytes": 1251}, {"_path": "Lib/site-packages/PIL/_util.py", "path_type": "hardlink", "sha256": "13be89d562c07ba5e0e72356633b50c18cf1513fec47f550c4526eec86774b79", "sha256_in_prefix": "13be89d562c07ba5e0e72356633b50c18cf1513fec47f550c4526eec86774b79", "size_in_bytes": 635}, {"_path": "Lib/site-packages/PIL/_version.py", "path_type": "hardlink", "sha256": "670bf62ca5adeafdf64932f92bdb8decf75c2666610e5a242932e40c0ecacb5c", "sha256_in_prefix": "670bf62ca5adeafdf64932f92bdb8decf75c2666610e5a242932e40c0ecacb5c", "size_in_bytes": 87}, {"_path": "Lib/site-packages/PIL/_webp.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "cd788feba7da20926c07d3af52c2655a171b84137e1f4a8e01e6e161275a6ee1", "sha256_in_prefix": "cd788feba7da20926c07d3af52c2655a171b84137e1f4a8e01e6e161275a6ee1", "size_in_bytes": 21504}, {"_path": "Lib/site-packages/PIL/_webp.pyi", "path_type": "hardlink", "sha256": "ddf071712a6926be84384714a23bdf946dc47a083b96fd90a7474d41020bacfe", "sha256_in_prefix": "ddf071712a6926be84384714a23bdf946dc47a083b96fd90a7474d41020bacfe", "size_in_bytes": 63}, {"_path": "Lib/site-packages/PIL/features.py", "path_type": "hardlink", "sha256": "15fc9839b5496f36105177fc2ae46ea98ea4bc0f267762e8ac4f3593712e42bc", "sha256_in_prefix": "15fc9839b5496f36105177fc2ae46ea98ea4bc0f267762e8ac4f3593712e42bc", "size_in_bytes": 11479}, {"_path": "Lib/site-packages/PIL/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/PIL/report.py", "path_type": "hardlink", "sha256": "e0963af8853bb07d512ae45b3afcb57d4b74740a22efd157e2d609377a750d3d", "sha256_in_prefix": "e0963af8853bb07d512ae45b3afcb57d4b74740a22efd157e2d609377a750d3d", "size_in_bytes": 100}, {"_path": "Lib/site-packages/pillow-11.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/pillow-11.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "f64a8de5505192ea2dc96230bd156f3a94009613f21618b21585014996c07aed", "sha256_in_prefix": "f64a8de5505192ea2dc96230bd156f3a94009613f21618b21585014996c07aed", "size_in_bytes": 9200}, {"_path": "Lib/site-packages/pillow-11.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "fdac7c9ba30d9ac4926d60852285629de1bb7332019f948207f7b9a3fe015cc2", "sha256_in_prefix": "fdac7c9ba30d9ac4926d60852285629de1bb7332019f948207f7b9a3fe015cc2", "size_in_bytes": 14101}, {"_path": "Lib/site-packages/pillow-11.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pillow-11.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "294b810ba97101b1c22a294ab9af11f56fd333bd7ffbd4a0e6e10fdee0d67285", "sha256_in_prefix": "294b810ba97101b1c22a294ab9af11f56fd333bd7ffbd4a0e6e10fdee0d67285", "size_in_bytes": 101}, {"_path": "Lib/site-packages/pillow-11.3.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "fa5bbb85aefd833e2dd2dc63696f381772ee5c635aab5d314de16c40596f8007", "sha256_in_prefix": "fa5bbb85aefd833e2dd2dc63696f381772ee5c635aab5d314de16c40596f8007", "size_in_bytes": 67}, {"_path": "Lib/site-packages/pillow-11.3.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "17f240ae101143707e5e7303a5d800450d9ccc7475b463cedb555cefdb3c6ece", "sha256_in_prefix": "17f240ae101143707e5e7303a5d800450d9ccc7475b463cedb555cefdb3c6ece", "size_in_bytes": 1453}, {"_path": "Lib/site-packages/pillow-11.3.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "ae266aae4fa1c99aa1e5fd59d19c228b774a7f112c07286ef5c53d20e0c5f8d6", "sha256_in_prefix": "ae266aae4fa1c99aa1e5fd59d19c228b774a7f112c07286ef5c53d20e0c5f8d6", "size_in_bytes": 4}, {"_path": "Lib/site-packages/pillow-11.3.0.dist-info/zip-safe", "path_type": "hardlink", "sha256": "7eb70257593da06f682a3ddda54a9d260d4fc514f645237f5ca74b08f8da61a6", "sha256_in_prefix": "7eb70257593da06f682a3ddda54a9d260d4fc514f645237f5ca74b08f8da61a6", "size_in_bytes": 2}], "paths_version": 1}, "requested_spec": "None", "sha256": "d7fd5a0b9ca4cc9fe945cae018cf054aa3b6139cd9022290e8c35e163829ef72", "size": 42010130, "subdir": "win-64", "timestamp": 1751482283000, "url": "https://conda.anaconda.org/conda-forge/win-64/pillow-11.3.0-py310h6d647b9_0.conda", "version": "11.3.0"}