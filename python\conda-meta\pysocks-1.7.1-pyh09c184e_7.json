{"build": "pyh09c184e_7", "build_number": 7, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["__win", "python >=3.9", "win_inet_pton"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\pysocks-1.7.1-pyh09c184e_7", "files": ["Lib/site-packages/PySocks-1.7.1.dist-info/INSTALLER", "Lib/site-packages/PySocks-1.7.1.dist-info/LICENSE", "Lib/site-packages/PySocks-1.7.1.dist-info/METADATA", "Lib/site-packages/PySocks-1.7.1.dist-info/RECORD", "Lib/site-packages/PySocks-1.7.1.dist-info/REQUESTED", "Lib/site-packages/PySocks-1.7.1.dist-info/WHEEL", "Lib/site-packages/PySocks-1.7.1.dist-info/direct_url.json", "Lib/site-packages/PySocks-1.7.1.dist-info/top_level.txt", "Lib/site-packages/socks.py", "Lib/site-packages/sockshandler.py", "Lib/site-packages/__pycache__/socks.cpython-310.pyc", "Lib/site-packages/__pycache__/sockshandler.cpython-310.pyc"], "fn": "pysocks-1.7.1-pyh09c184e_7.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\pysocks-1.7.1-pyh09c184e_7", "type": 1}, "md5": "e2fd202833c4a981ce8a65974fe4abd1", "name": "pysocks", "noarch": "python", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\pysocks-1.7.1-pyh09c184e_7.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/PySocks-1.7.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/PySocks-1.7.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "7027e214e014eb78b7adcc1ceda5aca713a79fc4f6a0c52c9da5b3e707e6ffe9", "sha256_in_prefix": "7027e214e014eb78b7adcc1ceda5aca713a79fc4f6a0c52c9da5b3e707e6ffe9", "size_in_bytes": 1401}, {"_path": "site-packages/PySocks-1.7.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "b27b89e3d44b7211119ff38456094675b69063eb2b82aefca3ce1a6b0dcec322", "sha256_in_prefix": "b27b89e3d44b7211119ff38456094675b69063eb2b82aefca3ce1a6b0dcec322", "size_in_bytes": 13556}, {"_path": "site-packages/PySocks-1.7.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "f173f371a9f3b581cd6afa789e5458a1bcad21b2a28876490cae2870599bc034", "sha256_in_prefix": "f173f371a9f3b581cd6afa789e5458a1bcad21b2a28876490cae2870599bc034", "size_in_bytes": 882}, {"_path": "site-packages/PySocks-1.7.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/PySocks-1.7.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "sha256_in_prefix": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "size_in_bytes": 91}, {"_path": "site-packages/PySocks-1.7.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "4cf7505cb8215c2e9336fdeebf051bb96d22c4fb089fa1711c0a4104dbc4c862", "sha256_in_prefix": "4cf7505cb8215c2e9336fdeebf051bb96d22c4fb089fa1711c0a4104dbc4c862", "size_in_bytes": 68}, {"_path": "site-packages/PySocks-1.7.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "4ca48e21f0850682bd118f050586d8a82dcf59ddfef86d79a61f67f3e4073c39", "sha256_in_prefix": "4ca48e21f0850682bd118f050586d8a82dcf59ddfef86d79a61f67f3e4073c39", "size_in_bytes": 19}, {"_path": "site-packages/socks.py", "path_type": "hardlink", "sha256": "c4e627dbbb7d206adb4c1cd6b1452e3dad1806ea6582253293390e0795798856", "sha256_in_prefix": "c4e627dbbb7d206adb4c1cd6b1452e3dad1806ea6582253293390e0795798856", "size_in_bytes": 31086}, {"_path": "site-packages/sockshandler.py", "path_type": "hardlink", "sha256": "d926068fea70b7592380ba19026c9e6845c27990d64667d54bf406ea412b1ad6", "sha256_in_prefix": "d926068fea70b7592380ba19026c9e6845c27990d64667d54bf406ea412b1ad6", "size_in_bytes": 3966}, {"_path": "Lib/site-packages/__pycache__/socks.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/__pycache__/sockshandler.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "d016e04b0e12063fbee4a2d5fbb9b39a8d191b5a0042f0b8459188aedeabb0ca", "size": 21784, "subdir": "noarch", "timestamp": 1733217448000, "url": "https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyh09c184e_7.conda", "version": "1.7.1"}