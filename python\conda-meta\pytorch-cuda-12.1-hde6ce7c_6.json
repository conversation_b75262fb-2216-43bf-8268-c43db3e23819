{"build": "hde6ce7c_6", "build_number": 6, "channel": "https://conda.anaconda.org/pytorch/win-64", "constrains": [], "depends": ["cuda-cudart >=12.1,<12.2", "cuda-cudart-dev >=12.1,<12.2", "cuda-cupti >=12.1,<12.2", "cuda-libraries >=12.1,<12.2", "cuda-libraries-dev >=12.1,<12.2", "cuda-nvrtc >=12.1,<12.2", "cuda-nvrtc-dev >=12.1,<12.2", "cuda-nvtx >=12.1,<12.2", "cuda-runtime >=12.1,<12.2", "libcublas >=12.1.0.26,<12.1.3.1", "libcublas-dev >=12.1.0.26,<12.1.3.1", "libcufft >=11.0.2.4,<11.0.2.54", "libcufft-dev >=11.0.2.4,<11.0.2.54", "libcusolver >=11.4.4.55,<11.4.5.107", "libcusolver-dev >=11.4.4.55,<11.4.5.107", "libcusparse >=12.0.2.55,<12.1.0.106", "libcusparse-dev >=12.0.2.55,<12.1.0.106", "libnpp >=12.0.2.50,<12.1.0.40", "libnpp-dev >=12.0.2.50,<12.1.0.40", "libnvjitlink >=12.1.105,<12.2.0", "libnvjpeg >=12.1.0.39,<12.2.0.2", "libnvjpeg-dev >=12.1.0.39,<12.2.0.2"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\pytorch-cuda-12.1-hde6ce7c_6", "files": [], "fn": "pytorch-cuda-12.1-hde6ce7c_6.tar.bz2", "license": "", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\pytorch-cuda-12.1-hde6ce7c_6", "type": 1}, "md5": "f906287cd2284d948aa310ac7bf9600c", "name": "pytorch-cuda", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\pytorch-cuda-12.1-hde6ce7c_6.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "pytorch-cuda=12.1", "sha256": "0b783c1cb706d1da8f88ff99f53102cf4179ba74e4a112c373479ae5d63d8570", "size": 7247, "subdir": "win-64", "timestamp": 1713892084000, "url": "https://conda.anaconda.org/pytorch/win-64/pytorch-cuda-12.1-hde6ce7c_6.tar.bz2", "version": "12.1"}