{"build": "py310_cu121", "build_number": 0, "channel": "https://conda.anaconda.org/pytorch/win-64", "constrains": ["cpuonly <0"], "depends": ["numpy", "python >=3.10,<3.11.0a0", "pytorch 2.3.1", "pytorch-cuda 12.1.*", "pytorch-mutex 1.0 cuda", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\torchaudio-2.3.1-py310_cu121", "files": ["lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/PKG-INFO", "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/SOURCES.txt", "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/dependency_links.txt", "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/not-zip-safe", "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/requires.txt", "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/top_level.txt", "lib/site-packages/torchaudio/__init__.py", "lib/site-packages/torchaudio/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/__pycache__/kaldi_io.cpython-310.pyc", "lib/site-packages/torchaudio/__pycache__/version.cpython-310.pyc", "lib/site-packages/torchaudio/_backend/__init__.py", "lib/site-packages/torchaudio/_backend/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/_backend/__pycache__/backend.cpython-310.pyc", "lib/site-packages/torchaudio/_backend/__pycache__/common.cpython-310.pyc", "lib/site-packages/torchaudio/_backend/__pycache__/ffmpeg.cpython-310.pyc", "lib/site-packages/torchaudio/_backend/__pycache__/soundfile.cpython-310.pyc", "lib/site-packages/torchaudio/_backend/__pycache__/soundfile_backend.cpython-310.pyc", "lib/site-packages/torchaudio/_backend/__pycache__/sox.cpython-310.pyc", "lib/site-packages/torchaudio/_backend/__pycache__/utils.cpython-310.pyc", "lib/site-packages/torchaudio/_backend/backend.py", "lib/site-packages/torchaudio/_backend/common.py", "lib/site-packages/torchaudio/_backend/ffmpeg.py", "lib/site-packages/torchaudio/_backend/soundfile.py", "lib/site-packages/torchaudio/_backend/soundfile_backend.py", "lib/site-packages/torchaudio/_backend/sox.py", "lib/site-packages/torchaudio/_backend/utils.py", "lib/site-packages/torchaudio/_extension/__init__.py", "lib/site-packages/torchaudio/_extension/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/_extension/__pycache__/utils.cpython-310.pyc", "lib/site-packages/torchaudio/_extension/utils.py", "lib/site-packages/torchaudio/_internal/__init__.py", "lib/site-packages/torchaudio/_internal/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/_internal/__pycache__/module_utils.cpython-310.pyc", "lib/site-packages/torchaudio/_internal/module_utils.py", "lib/site-packages/torchaudio/backend/__init__.py", "lib/site-packages/torchaudio/backend/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/backend/__pycache__/_no_backend.cpython-310.pyc", "lib/site-packages/torchaudio/backend/__pycache__/_sox_io_backend.cpython-310.pyc", "lib/site-packages/torchaudio/backend/__pycache__/common.cpython-310.pyc", "lib/site-packages/torchaudio/backend/__pycache__/no_backend.cpython-310.pyc", "lib/site-packages/torchaudio/backend/__pycache__/soundfile_backend.cpython-310.pyc", "lib/site-packages/torchaudio/backend/__pycache__/sox_io_backend.cpython-310.pyc", "lib/site-packages/torchaudio/backend/_no_backend.py", "lib/site-packages/torchaudio/backend/_sox_io_backend.py", "lib/site-packages/torchaudio/backend/common.py", "lib/site-packages/torchaudio/backend/no_backend.py", "lib/site-packages/torchaudio/backend/soundfile_backend.py", "lib/site-packages/torchaudio/backend/sox_io_backend.py", "lib/site-packages/torchaudio/compliance/__init__.py", "lib/site-packages/torchaudio/compliance/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/compliance/__pycache__/kaldi.cpython-310.pyc", "lib/site-packages/torchaudio/compliance/kaldi.py", "lib/site-packages/torchaudio/datasets/__init__.py", "lib/site-packages/torchaudio/datasets/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/cmuarctic.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/cmudict.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/commonvoice.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/dr_vctk.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/fluentcommands.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/gtzan.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/iemocap.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/librilight_limited.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/librimix.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/librispeech.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/librispeech_biasing.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/libritts.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/ljspeech.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/musdb_hq.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/quesst14.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/snips.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/speechcommands.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/tedlium.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/utils.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/vctk.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/voxceleb1.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/__pycache__/yesno.cpython-310.pyc", "lib/site-packages/torchaudio/datasets/cmuarctic.py", "lib/site-packages/torchaudio/datasets/cmudict.py", "lib/site-packages/torchaudio/datasets/commonvoice.py", "lib/site-packages/torchaudio/datasets/dr_vctk.py", "lib/site-packages/torchaudio/datasets/fluentcommands.py", "lib/site-packages/torchaudio/datasets/gtzan.py", "lib/site-packages/torchaudio/datasets/iemocap.py", "lib/site-packages/torchaudio/datasets/librilight_limited.py", "lib/site-packages/torchaudio/datasets/librimix.py", "lib/site-packages/torchaudio/datasets/librispeech.py", "lib/site-packages/torchaudio/datasets/librispeech_biasing.py", "lib/site-packages/torchaudio/datasets/libritts.py", "lib/site-packages/torchaudio/datasets/ljspeech.py", "lib/site-packages/torchaudio/datasets/musdb_hq.py", "lib/site-packages/torchaudio/datasets/quesst14.py", "lib/site-packages/torchaudio/datasets/snips.py", "lib/site-packages/torchaudio/datasets/speechcommands.py", "lib/site-packages/torchaudio/datasets/tedlium.py", "lib/site-packages/torchaudio/datasets/utils.py", "lib/site-packages/torchaudio/datasets/vctk.py", "lib/site-packages/torchaudio/datasets/voxceleb1.py", "lib/site-packages/torchaudio/datasets/yesno.py", "lib/site-packages/torchaudio/functional/__init__.py", "lib/site-packages/torchaudio/functional/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/functional/__pycache__/_alignment.cpython-310.pyc", "lib/site-packages/torchaudio/functional/__pycache__/filtering.cpython-310.pyc", "lib/site-packages/torchaudio/functional/__pycache__/functional.cpython-310.pyc", "lib/site-packages/torchaudio/functional/_alignment.py", "lib/site-packages/torchaudio/functional/filtering.py", "lib/site-packages/torchaudio/functional/functional.py", "lib/site-packages/torchaudio/io/__init__.py", "lib/site-packages/torchaudio/io/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/io/__pycache__/_effector.cpython-310.pyc", "lib/site-packages/torchaudio/io/__pycache__/_playback.cpython-310.pyc", "lib/site-packages/torchaudio/io/_effector.py", "lib/site-packages/torchaudio/io/_playback.py", "lib/site-packages/torchaudio/kaldi_io.py", "lib/site-packages/torchaudio/lib/__init__.py", "lib/site-packages/torchaudio/lib/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/lib/_torchaudio.pyd", "lib/site-packages/torchaudio/lib/libctc_prefix_decoder.pyd", "lib/site-packages/torchaudio/lib/libtorchaudio.pyd", "lib/site-packages/torchaudio/lib/pybind11_prefixctc.pyd", "lib/site-packages/torchaudio/models/__init__.py", "lib/site-packages/torchaudio/models/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/models/__pycache__/_hdemucs.cpython-310.pyc", "lib/site-packages/torchaudio/models/__pycache__/conformer.cpython-310.pyc", "lib/site-packages/torchaudio/models/__pycache__/conv_tasnet.cpython-310.pyc", "lib/site-packages/torchaudio/models/__pycache__/deepspeech.cpython-310.pyc", "lib/site-packages/torchaudio/models/__pycache__/emformer.cpython-310.pyc", "lib/site-packages/torchaudio/models/__pycache__/rnnt.cpython-310.pyc", "lib/site-packages/torchaudio/models/__pycache__/rnnt_decoder.cpython-310.pyc", "lib/site-packages/torchaudio/models/__pycache__/tacotron2.cpython-310.pyc", "lib/site-packages/torchaudio/models/__pycache__/wav2letter.cpython-310.pyc", "lib/site-packages/torchaudio/models/__pycache__/wavernn.cpython-310.pyc", "lib/site-packages/torchaudio/models/_hdemucs.py", "lib/site-packages/torchaudio/models/conformer.py", "lib/site-packages/torchaudio/models/conv_tasnet.py", "lib/site-packages/torchaudio/models/decoder/__init__.py", "lib/site-packages/torchaudio/models/decoder/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/models/decoder/__pycache__/_ctc_decoder.cpython-310.pyc", "lib/site-packages/torchaudio/models/decoder/__pycache__/_cuda_ctc_decoder.cpython-310.pyc", "lib/site-packages/torchaudio/models/decoder/_ctc_decoder.py", "lib/site-packages/torchaudio/models/decoder/_cuda_ctc_decoder.py", "lib/site-packages/torchaudio/models/deepspeech.py", "lib/site-packages/torchaudio/models/emformer.py", "lib/site-packages/torchaudio/models/rnnt.py", "lib/site-packages/torchaudio/models/rnnt_decoder.py", "lib/site-packages/torchaudio/models/squim/__init__.py", "lib/site-packages/torchaudio/models/squim/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/models/squim/__pycache__/objective.cpython-310.pyc", "lib/site-packages/torchaudio/models/squim/__pycache__/subjective.cpython-310.pyc", "lib/site-packages/torchaudio/models/squim/objective.py", "lib/site-packages/torchaudio/models/squim/subjective.py", "lib/site-packages/torchaudio/models/tacotron2.py", "lib/site-packages/torchaudio/models/wav2letter.py", "lib/site-packages/torchaudio/models/wav2vec2/__init__.py", "lib/site-packages/torchaudio/models/wav2vec2/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/models/wav2vec2/__pycache__/components.cpython-310.pyc", "lib/site-packages/torchaudio/models/wav2vec2/__pycache__/model.cpython-310.pyc", "lib/site-packages/torchaudio/models/wav2vec2/__pycache__/wavlm_attention.cpython-310.pyc", "lib/site-packages/torchaudio/models/wav2vec2/components.py", "lib/site-packages/torchaudio/models/wav2vec2/model.py", "lib/site-packages/torchaudio/models/wav2vec2/utils/__init__.py", "lib/site-packages/torchaudio/models/wav2vec2/utils/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/models/wav2vec2/utils/__pycache__/import_fairseq.cpython-310.pyc", "lib/site-packages/torchaudio/models/wav2vec2/utils/__pycache__/import_huggingface.cpython-310.pyc", "lib/site-packages/torchaudio/models/wav2vec2/utils/import_fairseq.py", "lib/site-packages/torchaudio/models/wav2vec2/utils/import_huggingface.py", "lib/site-packages/torchaudio/models/wav2vec2/wavlm_attention.py", "lib/site-packages/torchaudio/models/wavernn.py", "lib/site-packages/torchaudio/pipelines/__init__.py", "lib/site-packages/torchaudio/pipelines/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/__pycache__/_source_separation_pipeline.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/__pycache__/_squim_pipeline.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/__pycache__/rnnt_pipeline.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/_source_separation_pipeline.py", "lib/site-packages/torchaudio/pipelines/_squim_pipeline.py", "lib/site-packages/torchaudio/pipelines/_tts/__init__.py", "lib/site-packages/torchaudio/pipelines/_tts/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/_tts/__pycache__/impl.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/_tts/__pycache__/interface.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/_tts/__pycache__/utils.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/_tts/impl.py", "lib/site-packages/torchaudio/pipelines/_tts/interface.py", "lib/site-packages/torchaudio/pipelines/_tts/utils.py", "lib/site-packages/torchaudio/pipelines/_wav2vec2/__init__.py", "lib/site-packages/torchaudio/pipelines/_wav2vec2/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/_wav2vec2/__pycache__/aligner.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/_wav2vec2/__pycache__/impl.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/_wav2vec2/__pycache__/utils.cpython-310.pyc", "lib/site-packages/torchaudio/pipelines/_wav2vec2/aligner.py", "lib/site-packages/torchaudio/pipelines/_wav2vec2/impl.py", "lib/site-packages/torchaudio/pipelines/_wav2vec2/utils.py", "lib/site-packages/torchaudio/pipelines/rnnt_pipeline.py", "lib/site-packages/torchaudio/prototype/__init__.py", "lib/site-packages/torchaudio/prototype/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/datasets/__init__.py", "lib/site-packages/torchaudio/prototype/datasets/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/datasets/__pycache__/musan.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/datasets/musan.py", "lib/site-packages/torchaudio/prototype/functional/__init__.py", "lib/site-packages/torchaudio/prototype/functional/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/functional/__pycache__/_dsp.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/functional/__pycache__/_rir.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/functional/__pycache__/functional.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/functional/_dsp.py", "lib/site-packages/torchaudio/prototype/functional/_rir.py", "lib/site-packages/torchaudio/prototype/functional/functional.py", "lib/site-packages/torchaudio/prototype/models/__init__.py", "lib/site-packages/torchaudio/prototype/models/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/models/__pycache__/_conformer_wav2vec2.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/models/__pycache__/_emformer_hubert.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/models/__pycache__/conv_emformer.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/models/__pycache__/hifi_gan.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/models/__pycache__/rnnt.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/models/__pycache__/rnnt_decoder.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/models/_conformer_wav2vec2.py", "lib/site-packages/torchaudio/prototype/models/_emformer_hubert.py", "lib/site-packages/torchaudio/prototype/models/conv_emformer.py", "lib/site-packages/torchaudio/prototype/models/hifi_gan.py", "lib/site-packages/torchaudio/prototype/models/rnnt.py", "lib/site-packages/torchaudio/prototype/models/rnnt_decoder.py", "lib/site-packages/torchaudio/prototype/pipelines/__init__.py", "lib/site-packages/torchaudio/prototype/pipelines/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/pipelines/__pycache__/hifigan_pipeline.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/pipelines/__pycache__/rnnt_pipeline.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/pipelines/_vggish/__init__.py", "lib/site-packages/torchaudio/prototype/pipelines/_vggish/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/pipelines/_vggish/__pycache__/_vggish_impl.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/pipelines/_vggish/__pycache__/_vggish_pipeline.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/pipelines/_vggish/_vggish_impl.py", "lib/site-packages/torchaudio/prototype/pipelines/_vggish/_vggish_pipeline.py", "lib/site-packages/torchaudio/prototype/pipelines/hifigan_pipeline.py", "lib/site-packages/torchaudio/prototype/pipelines/rnnt_pipeline.py", "lib/site-packages/torchaudio/prototype/transforms/__init__.py", "lib/site-packages/torchaudio/prototype/transforms/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/transforms/__pycache__/_transforms.cpython-310.pyc", "lib/site-packages/torchaudio/prototype/transforms/_transforms.py", "lib/site-packages/torchaudio/sox_effects/__init__.py", "lib/site-packages/torchaudio/sox_effects/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/sox_effects/__pycache__/sox_effects.cpython-310.pyc", "lib/site-packages/torchaudio/sox_effects/sox_effects.py", "lib/site-packages/torchaudio/transforms/__init__.py", "lib/site-packages/torchaudio/transforms/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/transforms/__pycache__/_multi_channel.cpython-310.pyc", "lib/site-packages/torchaudio/transforms/__pycache__/_transforms.cpython-310.pyc", "lib/site-packages/torchaudio/transforms/_multi_channel.py", "lib/site-packages/torchaudio/transforms/_transforms.py", "lib/site-packages/torchaudio/utils/__init__.py", "lib/site-packages/torchaudio/utils/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchaudio/utils/__pycache__/download.cpython-310.pyc", "lib/site-packages/torchaudio/utils/__pycache__/ffmpeg_utils.cpython-310.pyc", "lib/site-packages/torchaudio/utils/__pycache__/sox_utils.cpython-310.pyc", "lib/site-packages/torchaudio/utils/download.py", "lib/site-packages/torchaudio/utils/ffmpeg_utils.py", "lib/site-packages/torchaudio/utils/sox_utils.py", "lib/site-packages/torchaudio/version.py", "lib/site-packages/torio/__init__.py", "lib/site-packages/torio/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torio/_extension/__init__.py", "lib/site-packages/torio/_extension/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torio/_extension/__pycache__/utils.cpython-310.pyc", "lib/site-packages/torio/_extension/utils.py", "lib/site-packages/torio/io/__init__.py", "lib/site-packages/torio/io/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torio/io/__pycache__/_streaming_media_decoder.cpython-310.pyc", "lib/site-packages/torio/io/__pycache__/_streaming_media_encoder.cpython-310.pyc", "lib/site-packages/torio/io/_streaming_media_decoder.py", "lib/site-packages/torio/io/_streaming_media_encoder.py", "lib/site-packages/torio/lib/__init__.py", "lib/site-packages/torio/lib/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torio/lib/_torio_ffmpeg4.pyd", "lib/site-packages/torio/lib/_torio_ffmpeg5.pyd", "lib/site-packages/torio/lib/_torio_ffmpeg6.pyd", "lib/site-packages/torio/lib/libtorio_ffmpeg4.pyd", "lib/site-packages/torio/lib/libtorio_ffmpeg5.pyd", "lib/site-packages/torio/lib/libtorio_ffmpeg6.pyd", "lib/site-packages/torio/utils/__init__.py", "lib/site-packages/torio/utils/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torio/utils/__pycache__/ffmpeg_utils.cpython-310.pyc", "lib/site-packages/torio/utils/ffmpeg_utils.py"], "fn": "torchaudio-2.3.1-py310_cu121.tar.bz2", "license": "BSD", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\torchaudio-2.3.1-py310_cu121", "type": 1}, "md5": "1405523118c9701a4380344591f05a34", "name": "<PERSON><PERSON><PERSON>", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\torchaudio-2.3.1-py310_cu121.tar.bz2", "paths_data": {"paths": [{"_path": "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "3e85c89ea240b97077a51897d08ee4c4cac8f3567de90b6b6509838275b68b20", "sha256_in_prefix": "3e85c89ea240b97077a51897d08ee4c4cac8f3567de90b6b6509838275b68b20", "size_in_bytes": 6463}, {"_path": "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "97f9399adbc13913fcf02789f791b3e2526eda2076c99c6f5610761941be3932", "sha256_in_prefix": "97f9399adbc13913fcf02789f791b3e2526eda2076c99c6f5610761941be3932", "size_in_bytes": 5464}, {"_path": "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/not-zip-safe", "path_type": "hardlink", "sha256": "7eb70257593da06f682a3ddda54a9d260d4fc514f645237f5ca74b08f8da61a6", "sha256_in_prefix": "7eb70257593da06f682a3ddda54a9d260d4fc514f645237f5ca74b08f8da61a6", "size_in_bytes": 2}, {"_path": "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/requires.txt", "path_type": "hardlink", "sha256": "0f05ba596aaa675853af500ead6fc350a757ace1a123cdfd95f279d502c3028b", "sha256_in_prefix": "0f05ba596aaa675853af500ead6fc350a757ace1a123cdfd95f279d502c3028b", "size_in_bytes": 13}, {"_path": "lib/site-packages/torchaudio-2.3.1-py3.10.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "193d0c92d11b1caa0b9ef77eea28bbfdd84956fb21ba93ae8e4f38d017075385", "sha256_in_prefix": "193d0c92d11b1caa0b9ef77eea28bbfdd84956fb21ba93ae8e4f38d017075385", "size_in_bytes": 17}, {"_path": "lib/site-packages/torchaudio/__init__.py", "path_type": "hardlink", "sha256": "9145a793070a111323ee6f81e04f36e9a1995866b9b09b350c4a4b3fa2d9a04b", "sha256_in_prefix": "9145a793070a111323ee6f81e04f36e9a1995866b9b09b350c4a4b3fa2d9a04b", "size_in_bytes": 945}, {"_path": "lib/site-packages/torchaudio/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "05f3a75bcc61f1afa52da00a3344357e8fbad338e4fa628158c77ab6507bcee5", "sha256_in_prefix": "05f3a75bcc61f1afa52da00a3344357e8fbad338e4fa628158c77ab6507bcee5", "size_in_bytes": 792}, {"_path": "lib/site-packages/torchaudio/__pycache__/kaldi_io.cpython-310.pyc", "path_type": "hardlink", "sha256": "1e3cd109c6fb40b8113989f39656eb9d5a73194e11e67b031bfaf827f174adef", "sha256_in_prefix": "1e3cd109c6fb40b8113989f39656eb9d5a73194e11e67b031bfaf827f174adef", "size_in_bytes": 4468}, {"_path": "lib/site-packages/torchaudio/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "adbfe4a049ba5dd529a6850c46d64425d7179278d976e128289b8c48df8f44f6", "sha256_in_prefix": "adbfe4a049ba5dd529a6850c46d64425d7179278d976e128289b8c48df8f44f6", "size_in_bytes": 214}, {"_path": "lib/site-packages/torchaudio/_backend/__init__.py", "path_type": "hardlink", "sha256": "d9b313677b46dbfe67be7a80026bc49c60489cee5af437543cda325c49d7d4cd", "sha256_in_prefix": "d9b313677b46dbfe67be7a80026bc49c60489cee5af437543cda325c49d7d4cd", "size_in_bytes": 1692}, {"_path": "lib/site-packages/torchaudio/_backend/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "739877fa1bd07cfcf26527dd2c1d3b5bdee531272fdf3f65fc5a5ac3e13c7ded", "sha256_in_prefix": "739877fa1bd07cfcf26527dd2c1d3b5bdee531272fdf3f65fc5a5ac3e13c7ded", "size_in_bytes": 1747}, {"_path": "lib/site-packages/torchaudio/_backend/__pycache__/backend.cpython-310.pyc", "path_type": "hardlink", "sha256": "2040d58f49788a655fccdfb05cce960caf7b2816fdb24744990069c8b9928a29", "sha256_in_prefix": "2040d58f49788a655fccdfb05cce960caf7b2816fdb24744990069c8b9928a29", "size_in_bytes": 1964}, {"_path": "lib/site-packages/torchaudio/_backend/__pycache__/common.cpython-310.pyc", "path_type": "hardlink", "sha256": "04ee23f4426acc5628380206d36253ad563c69333d08cc54b383372a261a6a0b", "sha256_in_prefix": "04ee23f4426acc5628380206d36253ad563c69333d08cc54b383372a261a6a0b", "size_in_bytes": 1970}, {"_path": "lib/site-packages/torchaudio/_backend/__pycache__/ffmpeg.cpython-310.pyc", "path_type": "hardlink", "sha256": "ab59959cffe5315d02d33a46c38c85441d1f6caa44ef4a3d2b6bb38d64b88600", "sha256_in_prefix": "ab59959cffe5315d02d33a46c38c85441d1f6caa44ef4a3d2b6bb38d64b88600", "size_in_bytes": 8088}, {"_path": "lib/site-packages/torchaudio/_backend/__pycache__/soundfile.cpython-310.pyc", "path_type": "hardlink", "sha256": "bcdcc3b2e74e74eed0ceea9ce31987d8cb6da4b6571def1c4fc382d5eb65c248", "sha256_in_prefix": "bcdcc3b2e74e74eed0ceea9ce31987d8cb6da4b6571def1c4fc382d5eb65c248", "size_in_bytes": 2126}, {"_path": "lib/site-packages/torchaudio/_backend/__pycache__/soundfile_backend.cpython-310.pyc", "path_type": "hardlink", "sha256": "4fe8dc727885ef081ed5597bc24bc32f1f5470d3759ddf55488464ae8388393d", "sha256_in_prefix": "4fe8dc727885ef081ed5597bc24bc32f1f5470d3759ddf55488464ae8388393d", "size_in_bytes": 13539}, {"_path": "lib/site-packages/torchaudio/_backend/__pycache__/sox.cpython-310.pyc", "path_type": "hardlink", "sha256": "ba0fbb2cbf68450aebe3af63e31aeea132332794032d4c7b95b582de22256297", "sha256_in_prefix": "ba0fbb2cbf68450aebe3af63e31aeea132332794032d4c7b95b582de22256297", "size_in_bytes": 3091}, {"_path": "lib/site-packages/torchaudio/_backend/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "7de870f87fb91413b2b1499285bcdcfaf6d1d59346b15da11fa940bdbf372493", "sha256_in_prefix": "7de870f87fb91413b2b1499285bcdcfaf6d1d59346b15da11fa940bdbf372493", "size_in_bytes": 12941}, {"_path": "lib/site-packages/torchaudio/_backend/backend.py", "path_type": "hardlink", "sha256": "09a92a07dcffe19b4bb104f23196c46c1d244eaa547bf82274780e6eff5a7324", "sha256_in_prefix": "09a92a07dcffe19b4bb104f23196c46c1d244eaa547bf82274780e6eff5a7324", "size_in_bytes": 1618}, {"_path": "lib/site-packages/torchaudio/_backend/common.py", "path_type": "hardlink", "sha256": "87d476f514d35b696a3a22980721135df5d6b2b4c7eb9b87c460cac376d68feb", "sha256_in_prefix": "87d476f514d35b696a3a22980721135df5d6b2b4c7eb9b87c460cac376d68feb", "size_in_bytes": 1835}, {"_path": "lib/site-packages/torchaudio/_backend/ffmpeg.py", "path_type": "hardlink", "sha256": "6fa76bebbb16838eee149c48c962d70adadd3cc9608d87569a3ee7f75f4f8753", "sha256_in_prefix": "6fa76bebbb16838eee149c48c962d70adadd3cc9608d87569a3ee7f75f4f8753", "size_in_bytes": 11628}, {"_path": "lib/site-packages/torchaudio/_backend/soundfile.py", "path_type": "hardlink", "sha256": "0ddf8ae9383f1b7cded3c8489af9c0b0ee65a40111a6651df68c6436c189527a", "sha256_in_prefix": "0ddf8ae9383f1b7cded3c8489af9c0b0ee65a40111a6651df68c6436c189527a", "size_in_bytes": 1757}, {"_path": "lib/site-packages/torchaudio/_backend/soundfile_backend.py", "path_type": "hardlink", "sha256": "b154843363a7e8f318ec06cfaa97ef135bb5060dbfd1a892ad9e137250058bfc", "sha256_in_prefix": "b154843363a7e8f318ec06cfaa97ef135bb5060dbfd1a892ad9e137250058bfc", "size_in_bytes": 17833}, {"_path": "lib/site-packages/torchaudio/_backend/sox.py", "path_type": "hardlink", "sha256": "506177e761c0ff991779b1c47713a5bf2360aee61a7310e4789cb812b8204612", "sha256_in_prefix": "506177e761c0ff991779b1c47713a5bf2360aee61a7310e4789cb812b8204612", "size_in_bytes": 3451}, {"_path": "lib/site-packages/torchaudio/_backend/utils.py", "path_type": "hardlink", "sha256": "f0792657f1821d9843f9768a350eb49c616cbbbad582bd523a16ac37abfdd5a2", "sha256_in_prefix": "f0792657f1821d9843f9768a350eb49c616cbbbad582bd523a16ac37abfdd5a2", "size_in_bytes": 13616}, {"_path": "lib/site-packages/torchaudio/_extension/__init__.py", "path_type": "hardlink", "sha256": "b3a0335e870372e8749ad61550238e8d9fe60a64829e8f236d937be74608f8fb", "sha256_in_prefix": "b3a0335e870372e8749ad61550238e8d9fe60a64829e8f236d937be74608f8fb", "size_in_bytes": 2276}, {"_path": "lib/site-packages/torchaudio/_extension/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "a2680c3dcc1e92bf05b849a99501323e7b3e322209a805d29a11894094b3b0d5", "sha256_in_prefix": "a2680c3dcc1e92bf05b849a99501323e7b3e322209a805d29a11894094b3b0d5", "size_in_bytes": 1493}, {"_path": "lib/site-packages/torchaudio/_extension/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "18605a2c36dc49b41cc9d7cd9857f8f86750e306e39303de060bf123135d1e86", "sha256_in_prefix": "18605a2c36dc49b41cc9d7cd9857f8f86750e306e39303de060bf123135d1e86", "size_in_bytes": 5938}, {"_path": "lib/site-packages/torchaudio/_extension/utils.py", "path_type": "hardlink", "sha256": "c050c5f01e90db650bdf3c97f2cc19f898e50e09bdf957637ab6ac950af6c886", "sha256_in_prefix": "c050c5f01e90db650bdf3c97f2cc19f898e50e09bdf957637ab6ac950af6c886", "size_in_bytes": 6438}, {"_path": "lib/site-packages/torchaudio/_internal/__init__.py", "path_type": "hardlink", "sha256": "f3472925f4d2f3defb618ad4deae69e0344018092a109ae61bd2eada1103a68a", "sha256_in_prefix": "f3472925f4d2f3defb618ad4deae69e0344018092a109ae61bd2eada1103a68a", "size_in_bytes": 251}, {"_path": "lib/site-packages/torchaudio/_internal/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "df86c4c811a791cd7555ce1fc61fd67e0ddc2ccccb146df350271c275ff6dd20", "sha256_in_prefix": "df86c4c811a791cd7555ce1fc61fd67e0ddc2ccccb146df350271c275ff6dd20", "size_in_bytes": 335}, {"_path": "lib/site-packages/torchaudio/_internal/__pycache__/module_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "4b640139c700521369f6c01d98590f7800c00880cbe6c8d410850fd0a4481a38", "sha256_in_prefix": "4b640139c700521369f6c01d98590f7800c00880cbe6c8d410850fd0a4481a38", "size_in_bytes": 4420}, {"_path": "lib/site-packages/torchaudio/_internal/module_utils.py", "path_type": "hardlink", "sha256": "77419ffc385a03e7c4b712878d6843c18ac1b07e820a4e357948bff531e143d9", "sha256_in_prefix": "77419ffc385a03e7c4b712878d6843c18ac1b07e820a4e357948bff531e143d9", "size_in_bytes": 3675}, {"_path": "lib/site-packages/torchaudio/backend/__init__.py", "path_type": "hardlink", "sha256": "724293fed99c99cfd9e3765396a277f5fc146e6bfe8fe9803f6056a74b14e138", "sha256_in_prefix": "724293fed99c99cfd9e3765396a277f5fc146e6bfe8fe9803f6056a74b14e138", "size_in_bytes": 289}, {"_path": "lib/site-packages/torchaudio/backend/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "609e7c84bb15ec1189273eed985163a4c363460be93646c67e39cd419baa09fe", "sha256_in_prefix": "609e7c84bb15ec1189273eed985163a4c363460be93646c67e39cd419baa09fe", "size_in_bytes": 261}, {"_path": "lib/site-packages/torchaudio/backend/__pycache__/_no_backend.cpython-310.pyc", "path_type": "hardlink", "sha256": "902f8c07b4a94d85748abab754a1fa2e32387f5ace15851e17287a400c66b6d2", "sha256_in_prefix": "902f8c07b4a94d85748abab754a1fa2e32387f5ace15851e17287a400c66b6d2", "size_in_bytes": 1082}, {"_path": "lib/site-packages/torchaudio/backend/__pycache__/_sox_io_backend.cpython-310.pyc", "path_type": "hardlink", "sha256": "f222620fbae7c409261ed833d86e7a45ab2ababb547b050e44214097c51a009b", "sha256_in_prefix": "f222620fbae7c409261ed833d86e7a45ab2ababb547b050e44214097c51a009b", "size_in_bytes": 11416}, {"_path": "lib/site-packages/torchaudio/backend/__pycache__/common.cpython-310.pyc", "path_type": "hardlink", "sha256": "0637779d21e32460d9ee4390b944a3815f0d8dbc5e0f55806250520b81804625", "sha256_in_prefix": "0637779d21e32460d9ee4390b944a3815f0d8dbc5e0f55806250520b81804625", "size_in_bytes": 598}, {"_path": "lib/site-packages/torchaudio/backend/__pycache__/no_backend.cpython-310.pyc", "path_type": "hardlink", "sha256": "ac81bb0a8c1e31b0a6fd34b91ec3106c42b3581b86cce0074abe1cf0020cab81", "sha256_in_prefix": "ac81bb0a8c1e31b0a6fd34b91ec3106c42b3581b86cce0074abe1cf0020cab81", "size_in_bytes": 661}, {"_path": "lib/site-packages/torchaudio/backend/__pycache__/soundfile_backend.cpython-310.pyc", "path_type": "hardlink", "sha256": "31efd31c59056a2c3dbda043fccf50272455db58d40a528897e9bdd5a46cc7ae", "sha256_in_prefix": "31efd31c59056a2c3dbda043fccf50272455db58d40a528897e9bdd5a46cc7ae", "size_in_bytes": 688}, {"_path": "lib/site-packages/torchaudio/backend/__pycache__/sox_io_backend.cpython-310.pyc", "path_type": "hardlink", "sha256": "ad4bdeae56f9244c385a7124aad8753d3b513f58b8c979270a6cee7ada0d07bc", "sha256_in_prefix": "ad4bdeae56f9244c385a7124aad8753d3b513f58b8c979270a6cee7ada0d07bc", "size_in_bytes": 669}, {"_path": "lib/site-packages/torchaudio/backend/_no_backend.py", "path_type": "hardlink", "sha256": "084a582746d98bddfb674ab62477515670ca77b1d694251547bac454713fc661", "sha256_in_prefix": "084a582746d98bddfb674ab62477515670ca77b1d694251547bac454713fc661", "size_in_bytes": 782}, {"_path": "lib/site-packages/torchaudio/backend/_sox_io_backend.py", "path_type": "hardlink", "sha256": "52ddfe41aab3689d0cbcd73b35da4c4d24fbfe6672d714a2c466c30bbe35f109", "sha256_in_prefix": "52ddfe41aab3689d0cbcd73b35da4c4d24fbfe6672d714a2c466c30bbe35f109", "size_in_bytes": 11750}, {"_path": "lib/site-packages/torchaudio/backend/common.py", "path_type": "hardlink", "sha256": "9a7d25e860707adec3bd140f51185820717e1eb405bc415528cdb73dac1f6c7f", "sha256_in_prefix": "9a7d25e860707adec3bd140f51185820717e1eb405bc415528cdb73dac1f6c7f", "size_in_bytes": 456}, {"_path": "lib/site-packages/torchaudio/backend/no_backend.py", "path_type": "hardlink", "sha256": "c5cfa4d24ab360e10ce9dbdc23088c0c468bbad2ab6175c2b025c5d485453473", "sha256_in_prefix": "c5cfa4d24ab360e10ce9dbdc23088c0c468bbad2ab6175c2b025c5d485453473", "size_in_bytes": 483}, {"_path": "lib/site-packages/torchaudio/backend/soundfile_backend.py", "path_type": "hardlink", "sha256": "3451c041ccfc93096023ba881b56d8adb833b23b430a4367fc69a2a7abc12fa8", "sha256_in_prefix": "3451c041ccfc93096023ba881b56d8adb833b23b430a4367fc69a2a7abc12fa8", "size_in_bytes": 513}, {"_path": "lib/site-packages/torchaudio/backend/sox_io_backend.py", "path_type": "hardlink", "sha256": "fc30f5ffacb8795e0cb089760a57319eaf7a93f1174f45ccbb9a7667ad609c4b", "sha256_in_prefix": "fc30f5ffacb8795e0cb089760a57319eaf7a93f1174f45ccbb9a7667ad609c4b", "size_in_bytes": 491}, {"_path": "lib/site-packages/torchaudio/compliance/__init__.py", "path_type": "hardlink", "sha256": "24d1fff9d4d05669b9e58c1c54cb95bd460559d5e11a7e02ffff52f0852c3685", "sha256_in_prefix": "24d1fff9d4d05669b9e58c1c54cb95bd460559d5e11a7e02ffff52f0852c3685", "size_in_bytes": 53}, {"_path": "lib/site-packages/torchaudio/compliance/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "930d963428537caef8341919d921e824aced8c06b2ff3be7fd9d125a2cf4bc64", "sha256_in_prefix": "930d963428537caef8341919d921e824aced8c06b2ff3be7fd9d125a2cf4bc64", "size_in_bytes": 198}, {"_path": "lib/site-packages/torchaudio/compliance/__pycache__/kaldi.cpython-310.pyc", "path_type": "hardlink", "sha256": "41abd6cf074a5bc46cbcaaa61d87cc6932774dc73e394b08d83861ac76b44b0a", "sha256_in_prefix": "41abd6cf074a5bc46cbcaaa61d87cc6932774dc73e394b08d83861ac76b44b0a", "size_in_bytes": 26400}, {"_path": "lib/site-packages/torchaudio/compliance/kaldi.py", "path_type": "hardlink", "sha256": "6d2eea2604b793c14ad5190c88d1283f7ab4c618de57f57844743d8e8feba8e3", "sha256_in_prefix": "6d2eea2604b793c14ad5190c88d1283f7ab4c618de57f57844743d8e8feba8e3", "size_in_bytes": 37479}, {"_path": "lib/site-packages/torchaudio/datasets/__init__.py", "path_type": "hardlink", "sha256": "85d1e5766dcece840b6c8d241e3f2d2f1ab00e1ccc7de76ad3fb759002bb3918", "sha256_in_prefix": "85d1e5766dcece840b6c8d241e3f2d2f1ab00e1ccc7de76ad3fb759002bb3918", "size_in_bytes": 1218}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "2dd475cd489ac5652409d8be4dcc1f8411b781dca2799ea316ff2608ade6f6bc", "sha256_in_prefix": "2dd475cd489ac5652409d8be4dcc1f8411b781dca2799ea316ff2608ade6f6bc", "size_in_bytes": 1236}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/cmuarctic.cpython-310.pyc", "path_type": "hardlink", "sha256": "3de5ea0d40c960c73c5be9aae554f7bdd739a242d852753e540167742b3414e9", "sha256_in_prefix": "3de5ea0d40c960c73c5be9aae554f7bdd739a242d852753e540167742b3414e9", "size_in_bytes": 6559}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/cmudict.cpython-310.pyc", "path_type": "hardlink", "sha256": "32d8ac9a998048e67c03ff9d5e3fdcfb59ca0e78df813c96c2c1b4b7bf26afd9", "sha256_in_prefix": "32d8ac9a998048e67c03ff9d5e3fdcfb59ca0e78df813c96c2c1b4b7bf26afd9", "size_in_bytes": 5063}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/commonvoice.cpython-310.pyc", "path_type": "hardlink", "sha256": "ec841b3a6cd0f296cde253a2dae04acfe648245d4e20a2627b27f9c54b55979a", "sha256_in_prefix": "ec841b3a6cd0f296cde253a2dae04acfe648245d4e20a2627b27f9c54b55979a", "size_in_bytes": 3148}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/dr_vctk.cpython-310.pyc", "path_type": "hardlink", "sha256": "1f7e3898cac65aa984e9c6317636c0874e5ded19284f917dcef40dda71f2a632", "sha256_in_prefix": "1f7e3898cac65aa984e9c6317636c0874e5ded19284f917dcef40dda71f2a632", "size_in_bytes": 4267}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/fluentcommands.cpython-310.pyc", "path_type": "hardlink", "sha256": "9d31406001bd079686f476941f09a8a7c73203b8f23c9399c3faff0a2e486430", "sha256_in_prefix": "9d31406001bd079686f476941f09a8a7c73203b8f23c9399c3faff0a2e486430", "size_in_bytes": 3712}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/gtzan.cpython-310.pyc", "path_type": "hardlink", "sha256": "0a2423a5b98aceb0ea320206cae51327825fdbb82cc4d737ac356c4f0eebf3f2", "sha256_in_prefix": "0a2423a5b98aceb0ea320206cae51327825fdbb82cc4d737ac356c4f0eebf3f2", "size_in_bytes": 17200}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/iemocap.cpython-310.pyc", "path_type": "hardlink", "sha256": "494f7c92fd43fe7870c25cae5949b27ddfa81a2016782705638d8aa783dc2bd5", "sha256_in_prefix": "494f7c92fd43fe7870c25cae5949b27ddfa81a2016782705638d8aa783dc2bd5", "size_in_bytes": 4592}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/librilight_limited.cpython-310.pyc", "path_type": "hardlink", "sha256": "350f9494208f93818442566621deb7d683043caac686ef8325436fc746e9003b", "sha256_in_prefix": "350f9494208f93818442566621deb7d683043caac686ef8325436fc746e9003b", "size_in_bytes": 4823}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/librimix.cpython-310.pyc", "path_type": "hardlink", "sha256": "d418bbe7609b6acb13a2cf9299f15fd0fdc6da7ed919b3d4e2ac0a415288ff60", "sha256_in_prefix": "d418bbe7609b6acb13a2cf9299f15fd0fdc6da7ed919b3d4e2ac0a415288ff60", "size_in_bytes": 5404}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/librispeech.cpython-310.pyc", "path_type": "hardlink", "sha256": "6a4d029d5305ac7177eb89a8915f45b1e7cba220e39123aa3ebb9105793bc1db", "sha256_in_prefix": "6a4d029d5305ac7177eb89a8915f45b1e7cba220e39123aa3ebb9105793bc1db", "size_in_bytes": 6276}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/librispeech_biasing.cpython-310.pyc", "path_type": "hardlink", "sha256": "e4bcd8c0b4bd09b5997e0e008a11504572790702a296287f737c89cb67a9f087", "sha256_in_prefix": "e4bcd8c0b4bd09b5997e0e008a11504572790702a296287f737c89cb67a9f087", "size_in_bytes": 6795}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/libritts.cpython-310.pyc", "path_type": "hardlink", "sha256": "6afb1988cedfd1f44079942c907e2022ab169cbb45a70873c9b7618723ae6147", "sha256_in_prefix": "6afb1988cedfd1f44079942c907e2022ab169cbb45a70873c9b7618723ae6147", "size_in_bytes": 5303}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/ljspeech.cpython-310.pyc", "path_type": "hardlink", "sha256": "3d5d012e6a453d985afb0e939223f07050eee54d270254927a1d7620d8ad45c6", "sha256_in_prefix": "3d5d012e6a453d985afb0e939223f07050eee54d270254927a1d7620d8ad45c6", "size_in_bytes": 3470}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/musdb_hq.cpython-310.pyc", "path_type": "hardlink", "sha256": "f76226ceda1c9799489265f9c6ebf5b6755887707c09862c16dc038efd07408b", "sha256_in_prefix": "f76226ceda1c9799489265f9c6ebf5b6755887707c09862c16dc038efd07408b", "size_in_bytes": 4987}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/quesst14.cpython-310.pyc", "path_type": "hardlink", "sha256": "85c1470c39ac6eaadf39201f12e6099afb2a6bea68f8cc444ecba13639139678", "sha256_in_prefix": "85c1470c39ac6eaadf39201f12e6099afb2a6bea68f8cc444ecba13639139678", "size_in_bytes": 4502}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/snips.cpython-310.pyc", "path_type": "hardlink", "sha256": "e5b132671d9e3b012c3640140d09baeedc6248cd0ee285b764dfb3a4edd1a774", "sha256_in_prefix": "e5b132671d9e3b012c3640140d09baeedc6248cd0ee285b764dfb3a4edd1a774", "size_in_bytes": 5046}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/speechcommands.cpython-310.pyc", "path_type": "hardlink", "sha256": "cbca3d804b94663bdd338faa5bd85b0bb2c062e1cfbe8b5b93af1fe53c26ecb5", "sha256_in_prefix": "cbca3d804b94663bdd338faa5bd85b0bb2c062e1cfbe8b5b93af1fe53c26ecb5", "size_in_bytes": 6919}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/tedlium.cpython-310.pyc", "path_type": "hardlink", "sha256": "0d1145cc5013ae17a1f8e889f8ec192e551ffa6d0ce834f71c96cbf398b72a97", "sha256_in_prefix": "0d1145cc5013ae17a1f8e889f8ec192e551ffa6d0ce834f71c96cbf398b72a97", "size_in_bytes": 7246}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "0e445021ca446ec22c31e9daf4d5b4e1e9791c686fa34119e9fe2948a17036e6", "sha256_in_prefix": "0e445021ca446ec22c31e9daf4d5b4e1e9791c686fa34119e9fe2948a17036e6", "size_in_bytes": 1616}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/vctk.cpython-310.pyc", "path_type": "hardlink", "sha256": "5f3e7ffa5c106d871cd2c0448cbb93d0ba6a7006f569132b1fea1c3f2adc5a22", "sha256_in_prefix": "5f3e7ffa5c106d871cd2c0448cbb93d0ba6a7006f569132b1fea1c3f2adc5a22", "size_in_bytes": 4826}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/voxceleb1.cpython-310.pyc", "path_type": "hardlink", "sha256": "c1e4554331424f8c70e85a3ea23dc183c0eb2c7a4aa12a0f47a225e66c9697f9", "sha256_in_prefix": "c1e4554331424f8c70e85a3ea23dc183c0eb2c7a4aa12a0f47a225e66c9697f9", "size_in_bytes": 11747}, {"_path": "lib/site-packages/torchaudio/datasets/__pycache__/yesno.cpython-310.pyc", "path_type": "hardlink", "sha256": "4afb5b1b33261e968f15e539cb2d55a6e78cecf5c01beb2531692bdca5cbd38a", "sha256_in_prefix": "4afb5b1b33261e968f15e539cb2d55a6e78cecf5c01beb2531692bdca5cbd38a", "size_in_bytes": 3593}, {"_path": "lib/site-packages/torchaudio/datasets/cmuarctic.py", "path_type": "hardlink", "sha256": "c441c1b4ef28879b9bf156cb6b5a9c2fe99e16161e838110a4c51d88669e9460", "sha256_in_prefix": "c441c1b4ef28879b9bf156cb6b5a9c2fe99e16161e838110a4c51d88669e9460", "size_in_bytes": 7254}, {"_path": "lib/site-packages/torchaudio/datasets/cmudict.py", "path_type": "hardlink", "sha256": "ffdbd3cfbffc04556b707780eb5ffe8765cb3a5e88b1d582a6d90c5b388e5bb5", "sha256_in_prefix": "ffdbd3cfbffc04556b707780eb5ffe8765cb3a5e88b1d582a6d90c5b388e5bb5", "size_in_bytes": 6176}, {"_path": "lib/site-packages/torchaudio/datasets/commonvoice.py", "path_type": "hardlink", "sha256": "39c167fa71b861f048cf4608a47f75c47f6b1646bcc85266af1cb8bc56640b82", "sha256_in_prefix": "39c167fa71b861f048cf4608a47f75c47f6b1646bcc85266af1cb8bc56640b82", "size_in_bytes": 2849}, {"_path": "lib/site-packages/torchaudio/datasets/dr_vctk.py", "path_type": "hardlink", "sha256": "0327fce69ac336bd4b7164386f2b1559d4553ebcb62402e3bfa32dabeea20696", "sha256_in_prefix": "0327fce69ac336bd4b7164386f2b1559d4553ebcb62402e3bfa32dabeea20696", "size_in_bytes": 4498}, {"_path": "lib/site-packages/torchaudio/datasets/fluentcommands.py", "path_type": "hardlink", "sha256": "2a7987d58dbc9393e1a905fa795fbbe4cab04d1c621d251472f02c6be2bde78b", "sha256_in_prefix": "2a7987d58dbc9393e1a905fa795fbbe4cab04d1c621d251472f02c6be2bde78b", "size_in_bytes": 3353}, {"_path": "lib/site-packages/torchaudio/datasets/gtzan.py", "path_type": "hardlink", "sha256": "92ddb92f2f6a0c6ba28955e0b17852d39b468ba95a461464a3cd7e050e2c67ec", "sha256_in_prefix": "92ddb92f2f6a0c6ba28955e0b17852d39b468ba95a461464a3cd7e050e2c67ec", "size_in_bytes": 25475}, {"_path": "lib/site-packages/torchaudio/datasets/iemocap.py", "path_type": "hardlink", "sha256": "64c306fc5a5c59c30711b86e4584506945a2fc3a1e823c42ae75720a0e441151", "sha256_in_prefix": "64c306fc5a5c59c30711b86e4584506945a2fc3a1e823c42ae75720a0e441151", "size_in_bytes": 5077}, {"_path": "lib/site-packages/torchaudio/datasets/librilight_limited.py", "path_type": "hardlink", "sha256": "8b06419522952eb5f3859bdaaa3b954463ba733c57e1fa5dcddf3059ee5f7964", "sha256_in_prefix": "8b06419522952eb5f3859bdaaa3b954463ba733c57e1fa5dcddf3059ee5f7964", "size_in_bytes": 4290}, {"_path": "lib/site-packages/torchaudio/datasets/librimix.py", "path_type": "hardlink", "sha256": "027704ebbd403a5d387513ec6a33595be671c48fcfc00dac8c17ed74183843e9", "sha256_in_prefix": "027704ebbd403a5d387513ec6a33595be671c48fcfc00dac8c17ed74183843e9", "size_in_bytes": 5249}, {"_path": "lib/site-packages/torchaudio/datasets/librispeech.py", "path_type": "hardlink", "sha256": "cacefaf48d14cc6d3b5049b267f283c004e1e32734f21154b826b22bcb582068", "sha256_in_prefix": "cacefaf48d14cc6d3b5049b267f283c004e1e32734f21154b826b22bcb582068", "size_in_bytes": 6482}, {"_path": "lib/site-packages/torchaudio/datasets/librispeech_biasing.py", "path_type": "hardlink", "sha256": "2841a995153fc2981bd15aab4feb78da4bed0bb960e2e32cb19728b15bef3e18", "sha256_in_prefix": "2841a995153fc2981bd15aab4feb78da4bed0bb960e2e32cb19728b15bef3e18", "size_in_bytes": 7147}, {"_path": "lib/site-packages/torchaudio/datasets/libritts.py", "path_type": "hardlink", "sha256": "f75129d8cab73b24ab7b6e469e25c12e64734f010f88a98c6aa5e7ce2ae7d316", "sha256_in_prefix": "f75129d8cab73b24ab7b6e469e25c12e64734f010f88a98c6aa5e7ce2ae7d316", "size_in_bytes": 6038}, {"_path": "lib/site-packages/torchaudio/datasets/ljspeech.py", "path_type": "hardlink", "sha256": "974f41481407efa23e2e1624211174bb5f2d4e2fb8cb2b1a178823d864996b1c", "sha256_in_prefix": "974f41481407efa23e2e1624211174bb5f2d4e2fb8cb2b1a178823d864996b1c", "size_in_bytes": 3601}, {"_path": "lib/site-packages/torchaudio/datasets/musdb_hq.py", "path_type": "hardlink", "sha256": "15594ab061011e24f9d32f462ecc27b76405a61d8f8e223ac82cb5331886e9ff", "sha256_in_prefix": "15594ab061011e24f9d32f462ecc27b76405a61d8f8e223ac82cb5331886e9ff", "size_in_bytes": 5214}, {"_path": "lib/site-packages/torchaudio/datasets/quesst14.py", "path_type": "hardlink", "sha256": "df2e87dd3de0efc8e40ea71af23391401395899847d51865b1fb98f0727639c5", "sha256_in_prefix": "df2e87dd3de0efc8e40ea71af23391401395899847d51865b1fb98f0727639c5", "size_in_bytes": 4591}, {"_path": "lib/site-packages/torchaudio/datasets/snips.py", "path_type": "hardlink", "sha256": "9b055ce4ab1b3253d027cedec988239d0e52e04157a10be6d7774ed2b5e926e1", "sha256_in_prefix": "9b055ce4ab1b3253d027cedec988239d0e52e04157a10be6d7774ed2b5e926e1", "size_in_bytes": 5165}, {"_path": "lib/site-packages/torchaudio/datasets/speechcommands.py", "path_type": "hardlink", "sha256": "ff09ab2928847b408e3bbba4d09557ca9066371bb4bab9dc787b8543accc3a4d", "sha256_in_prefix": "ff09ab2928847b408e3bbba4d09557ca9066371bb4bab9dc787b8543accc3a4d", "size_in_bytes": 7664}, {"_path": "lib/site-packages/torchaudio/datasets/tedlium.py", "path_type": "hardlink", "sha256": "51065469e52a9859ed656707f475cea46796eadb02706f356cf8d7dbf096c5b8", "sha256_in_prefix": "51065469e52a9859ed656707f475cea46796eadb02706f356cf8d7dbf096c5b8", "size_in_bytes": 8916}, {"_path": "lib/site-packages/torchaudio/datasets/utils.py", "path_type": "hardlink", "sha256": "9beb0162041bd09c601957ec5697a42850c8ffb3c65c24e66ba376ca67c99748", "sha256_in_prefix": "9beb0162041bd09c601957ec5697a42850c8ffb3c65c24e66ba376ca67c99748", "size_in_bytes": 1743}, {"_path": "lib/site-packages/torchaudio/datasets/vctk.py", "path_type": "hardlink", "sha256": "bcdfd5cf14cbc875b5d48febcdfccc540de1e495bdd7b15a5373429d1fb370bd", "sha256_in_prefix": "bcdfd5cf14cbc875b5d48febcdfccc540de1e495bdd7b15a5373429d1fb370bd", "size_in_bytes": 5842}, {"_path": "lib/site-packages/torchaudio/datasets/voxceleb1.py", "path_type": "hardlink", "sha256": "2656246f260e0055058462d42dede582e7003567ff1bba86ab0e3b623897d883", "sha256_in_prefix": "2656246f260e0055058462d42dede582e7003567ff1bba86ab0e3b623897d883", "size_in_bytes": 12034}, {"_path": "lib/site-packages/torchaudio/datasets/yesno.py", "path_type": "hardlink", "sha256": "0778513546b3bc1f15f12c0e52543396207dbc8f6032497d48497e759e0f11ac", "sha256_in_prefix": "0778513546b3bc1f15f12c0e52543396207dbc8f6032497d48497e759e0f11ac", "size_in_bytes": 3115}, {"_path": "lib/site-packages/torchaudio/functional/__init__.py", "path_type": "hardlink", "sha256": "37094fa168cdadabd7e22b42672f9dccdf8190215d0c7151c2f3e57bc263051a", "sha256_in_prefix": "37094fa168cdadabd7e22b42672f9dccdf8190215d0c7151c2f3e57bc263051a", "size_in_bytes": 2484}, {"_path": "lib/site-packages/torchaudio/functional/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "88ec496e9518c61ccb991992f8faaa000083b7e8683c48d7be43f04b0860eed2", "sha256_in_prefix": "88ec496e9518c61ccb991992f8faaa000083b7e8683c48d7be43f04b0860eed2", "size_in_bytes": 1923}, {"_path": "lib/site-packages/torchaudio/functional/__pycache__/_alignment.cpython-310.pyc", "path_type": "hardlink", "sha256": "fa23e8271bf34256b590fa6b00aa8ed8307fd5b91dccf7b7919595995f7f2865", "sha256_in_prefix": "fa23e8271bf34256b590fa6b00aa8ed8307fd5b91dccf7b7919595995f7f2865", "size_in_bytes": 4674}, {"_path": "lib/site-packages/torchaudio/functional/__pycache__/filtering.cpython-310.pyc", "path_type": "hardlink", "sha256": "d9c2626f2a919f87761c3ad2c608e6d975575254f1ea7c1337769ceb82bb7bb4", "sha256_in_prefix": "d9c2626f2a919f87761c3ad2c608e6d975575254f1ea7c1337769ceb82bb7bb4", "size_in_bytes": 47702}, {"_path": "lib/site-packages/torchaudio/functional/__pycache__/functional.cpython-310.pyc", "path_type": "hardlink", "sha256": "b3e23fb8f4e27e5b2f5e96a7e5334ecf12ed23664cfe2499411e2af1b903da92", "sha256_in_prefix": "b3e23fb8f4e27e5b2f5e96a7e5334ecf12ed23664cfe2499411e2af1b903da92", "size_in_bytes": 77632}, {"_path": "lib/site-packages/torchaudio/functional/_alignment.py", "path_type": "hardlink", "sha256": "e3a1a1b84614a88d6013ed94292bbd048405d50a59e72ba7512f09646649bacb", "sha256_in_prefix": "e3a1a1b84614a88d6013ed94292bbd048405d50a59e72ba7512f09646649bacb", "size_in_bytes": 4823}, {"_path": "lib/site-packages/torchaudio/functional/filtering.py", "path_type": "hardlink", "sha256": "2f73da1a7a7b40a5f02f824408d48084054ff76aeb47c0467dd5e8c9b4ab1c55", "sha256_in_prefix": "2f73da1a7a7b40a5f02f824408d48084054ff76aeb47c0467dd5e8c9b4ab1c55", "size_in_bytes": 63114}, {"_path": "lib/site-packages/torchaudio/functional/functional.py", "path_type": "hardlink", "sha256": "aa3754de420bcbaf7e8d02f7950d843b5cbf7692c389ffe3029f7d8414499cfa", "sha256_in_prefix": "aa3754de420bcbaf7e8d02f7950d843b5cbf7692c389ffe3029f7d8414499cfa", "size_in_bytes": 98541}, {"_path": "lib/site-packages/torchaudio/io/__init__.py", "path_type": "hardlink", "sha256": "e54dcc946546c39bcf4d9a8267eecddb9a157f0b2c03a294b65ba3fbdacd44c3", "sha256_in_prefix": "e54dcc946546c39bcf4d9a8267eecddb9a157f0b2c03a294b65ba3fbdacd44c3", "size_in_bytes": 310}, {"_path": "lib/site-packages/torchaudio/io/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "14c4c43fcf793d7211218ba063c1b8dfc3e991fe16aa5b85f011b7d631048e23", "sha256_in_prefix": "14c4c43fcf793d7211218ba063c1b8dfc3e991fe16aa5b85f011b7d631048e23", "size_in_bytes": 414}, {"_path": "lib/site-packages/torchaudio/io/__pycache__/_effector.cpython-310.pyc", "path_type": "hardlink", "sha256": "e6341166385224be00c139021bb917ff1c987edc0e244b34a5599a3416b8ea79", "sha256_in_prefix": "e6341166385224be00c139021bb917ff1c987edc0e244b34a5599a3416b8ea79", "size_in_bytes": 10752}, {"_path": "lib/site-packages/torchaudio/io/__pycache__/_playback.cpython-310.pyc", "path_type": "hardlink", "sha256": "5aa5e3935db493e94c197268947713b6beb721b6e74594c96498db0e2ee8f596", "sha256_in_prefix": "5aa5e3935db493e94c197268947713b6beb721b6e74594c96498db0e2ee8f596", "size_in_bytes": 2216}, {"_path": "lib/site-packages/torchaudio/io/_effector.py", "path_type": "hardlink", "sha256": "e4a87b6ebf99b8bce6a114955e4e49350f0d93070652263f9abb7b77fd56d5e8", "sha256_in_prefix": "e4a87b6ebf99b8bce6a114955e4e49350f0d93070652263f9abb7b77fd56d5e8", "size_in_bytes": 12217}, {"_path": "lib/site-packages/torchaudio/io/_playback.py", "path_type": "hardlink", "sha256": "5293dbfa6df95d49582f69726d019700902f7e650f4ff2aac788e9600ac8033d", "sha256_in_prefix": "5293dbfa6df95d49582f69726d019700902f7e650f4ff2aac788e9600ac8033d", "size_in_bytes": 2393}, {"_path": "lib/site-packages/torchaudio/kaldi_io.py", "path_type": "hardlink", "sha256": "69cc32b2be9f01257d21c39317401b54f0a8fd5413bb533600e9f54979b718f1", "sha256_in_prefix": "69cc32b2be9f01257d21c39317401b54f0a8fd5413bb533600e9f54979b718f1", "size_in_bytes": 5217}, {"_path": "lib/site-packages/torchaudio/lib/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/site-packages/torchaudio/lib/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "191ecedc461f49293d7e1dddee4ae808b1410c9d78d937bd316525573774e29f", "sha256_in_prefix": "191ecedc461f49293d7e1dddee4ae808b1410c9d78d937bd316525573774e29f", "size_in_bytes": 137}, {"_path": "lib/site-packages/torchaudio/lib/_torchaudio.pyd", "path_type": "hardlink", "sha256": "6303bc93000bc4e7c5218798471d38fc9540d22b11cd555d4705aa828019156a", "sha256_in_prefix": "6303bc93000bc4e7c5218798471d38fc9540d22b11cd555d4705aa828019156a", "size_in_bytes": 748032}, {"_path": "lib/site-packages/torchaudio/lib/libctc_prefix_decoder.pyd", "path_type": "hardlink", "sha256": "266d3ceb4e0c760bc54650a4b722337a35f942f9f46fab2d9f8d5fa130703a28", "sha256_in_prefix": "266d3ceb4e0c760bc54650a4b722337a35f942f9f46fab2d9f8d5fa130703a28", "size_in_bytes": 4780544}, {"_path": "lib/site-packages/torchaudio/lib/libtorchaudio.pyd", "path_type": "hardlink", "sha256": "1bd767db70b188aa8b679c7521ebf75e5df296ff3bedbaae35fdb425d0f738b7", "sha256_in_prefix": "1bd767db70b188aa8b679c7521ebf75e5df296ff3bedbaae35fdb425d0f738b7", "size_in_bytes": 2718208}, {"_path": "lib/site-packages/torchaudio/lib/pybind11_prefixctc.pyd", "path_type": "hardlink", "sha256": "88509351a78733980e75d8b99c6ccda138e6fe3f31cb9f85e120520d049bef5b", "sha256_in_prefix": "88509351a78733980e75d8b99c6ccda138e6fe3f31cb9f85e120520d049bef5b", "size_in_bytes": 640000}, {"_path": "lib/site-packages/torchaudio/models/__init__.py", "path_type": "hardlink", "sha256": "1a2dd442fc63c132d6f707ca945e363b756ea7bd1dd1b936c7ead94bcf404b02", "sha256_in_prefix": "1a2dd442fc63c132d6f707ca945e363b756ea7bd1dd1b936c7ead94bcf404b02", "size_in_bytes": 2080}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "0bda8f4f0ca81be4fbb7dd0c87457c4ce6ab7c8e36c5989a35cf0f4843f54e48", "sha256_in_prefix": "0bda8f4f0ca81be4fbb7dd0c87457c4ce6ab7c8e36c5989a35cf0f4843f54e48", "size_in_bytes": 1698}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/_hdemucs.cpython-310.pyc", "path_type": "hardlink", "sha256": "d3b44329fd0c814885c65d81795dd04ba74ebef9d39f1e734f255c917fb3e4b0", "sha256_in_prefix": "d3b44329fd0c814885c65d81795dd04ba74ebef9d39f1e734f255c917fb3e4b0", "size_in_bytes": 31494}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/conformer.cpython-310.pyc", "path_type": "hardlink", "sha256": "035289f1d4caf6afc6a717cb269932469f24defddfff0c16ad32522419d20229", "sha256_in_prefix": "035289f1d4caf6afc6a717cb269932469f24defddfff0c16ad32522419d20229", "size_in_bytes": 9400}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/conv_tasnet.cpython-310.pyc", "path_type": "hardlink", "sha256": "5811d0ad6ac88a39a0401715d0fadebef82f9ba8cbe71a7c811167820e000154", "sha256_in_prefix": "5811d0ad6ac88a39a0401715d0fadebef82f9ba8cbe71a7c811167820e000154", "size_in_bytes": 10140}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/deepspeech.cpython-310.pyc", "path_type": "hardlink", "sha256": "80a14feb95644747432fb7ba962547d737679af1ce08f52d635b428369cd448a", "sha256_in_prefix": "80a14feb95644747432fb7ba962547d737679af1ce08f52d635b428369cd448a", "size_in_bytes": 2735}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/emformer.cpython-310.pyc", "path_type": "hardlink", "sha256": "33367a04c7cee4923aced5ba25f1a9741cc582462df3d9a3b31b2f27a85f9cac", "sha256_in_prefix": "33367a04c7cee4923aced5ba25f1a9741cc582462df3d9a3b31b2f27a85f9cac", "size_in_bytes": 29305}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/rnnt.cpython-310.pyc", "path_type": "hardlink", "sha256": "196f32e6c891f11ccf0605fedc6819e86234fc57e3c1066761a98d5ecde484e6", "sha256_in_prefix": "196f32e6c891f11ccf0605fedc6819e86234fc57e3c1066761a98d5ecde484e6", "size_in_bytes": 31873}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/rnnt_decoder.cpython-310.pyc", "path_type": "hardlink", "sha256": "90149da6553b4c90153a53e2646ae3ce15b61f6ceb2addd93d9aaa2397a3aa23", "sha256_in_prefix": "90149da6553b4c90153a53e2646ae3ce15b61f6ceb2addd93d9aaa2397a3aa23", "size_in_bytes": 11728}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/tacotron2.cpython-310.pyc", "path_type": "hardlink", "sha256": "f9700479124446f61c7566fffd951810737ed3c1955ebd56d94d22f0614d29e1", "sha256_in_prefix": "f9700479124446f61c7566fffd951810737ed3c1955ebd56d94d22f0614d29e1", "size_in_bytes": 35122}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/wav2letter.cpython-310.pyc", "path_type": "hardlink", "sha256": "080a0a1b5f61a6361326ffc6eeec37cbddd065acff244e4d3331d0a73bc08b1f", "sha256_in_prefix": "080a0a1b5f61a6361326ffc6eeec37cbddd065acff244e4d3331d0a73bc08b1f", "size_in_bytes": 2508}, {"_path": "lib/site-packages/torchaudio/models/__pycache__/wavernn.cpython-310.pyc", "path_type": "hardlink", "sha256": "cd157f992c736a9c22d56e732482885ac03a3c6d870c35286132bdab9cc322ba", "sha256_in_prefix": "cd157f992c736a9c22d56e732482885ac03a3c6d870c35286132bdab9cc322ba", "size_in_bytes": 14623}, {"_path": "lib/site-packages/torchaudio/models/_hdemucs.py", "path_type": "hardlink", "sha256": "8a9023efdeb93cefd6119a90c16d689bd8108fdd1485039e53a1d4dcba6fcf0a", "sha256_in_prefix": "8a9023efdeb93cefd6119a90c16d689bd8108fdd1485039e53a1d4dcba6fcf0a", "size_in_bytes": 39250}, {"_path": "lib/site-packages/torchaudio/models/conformer.py", "path_type": "hardlink", "sha256": "815ace61e2643e555a5fee1e6695733547bfaf793b83563a35a690f0964ffabe", "sha256_in_prefix": "815ace61e2643e555a5fee1e6695733547bfaf793b83563a35a690f0964ffabe", "size_in_bytes": 10361}, {"_path": "lib/site-packages/torchaudio/models/conv_tasnet.py", "path_type": "hardlink", "sha256": "0fb635d2c3b32ded3783281f37527947bdd220790819540e92a290e8d8b7a3fb", "sha256_in_prefix": "0fb635d2c3b32ded3783281f37527947bdd220790819540e92a290e8d8b7a3fb", "size_in_bytes": 12870}, {"_path": "lib/site-packages/torchaudio/models/decoder/__init__.py", "path_type": "hardlink", "sha256": "58c878b9d37c08617e4af80de8905748d323859aa6d5eb7b17232bb24e95e913", "sha256_in_prefix": "58c878b9d37c08617e4af80de8905748d323859aa6d5eb7b17232bb24e95e913", "size_in_bytes": 1252}, {"_path": "lib/site-packages/torchaudio/models/decoder/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "7fd9bd4ec90dd11bb3e627f96cb6e33b0ad686bcf4a10db071e6f70bb56f0176", "sha256_in_prefix": "7fd9bd4ec90dd11bb3e627f96cb6e33b0ad686bcf4a10db071e6f70bb56f0176", "size_in_bytes": 1211}, {"_path": "lib/site-packages/torchaudio/models/decoder/__pycache__/_ctc_decoder.cpython-310.pyc", "path_type": "hardlink", "sha256": "6eb7924682863d7420d03b7f23f5fb1b25d9e4bd088fb6c368f19a716bfa1a9c", "sha256_in_prefix": "6eb7924682863d7420d03b7f23f5fb1b25d9e4bd088fb6c368f19a716bfa1a9c", "size_in_bytes": 19140}, {"_path": "lib/site-packages/torchaudio/models/decoder/__pycache__/_cuda_ctc_decoder.cpython-310.pyc", "path_type": "hardlink", "sha256": "b3b967bcc7203d47aaf9d4e5bcf1d52b6e8c86a48f89367e4d8a7117f418cf57", "sha256_in_prefix": "b3b967bcc7203d47aaf9d4e5bcf1d52b6e8c86a48f89367e4d8a7117f418cf57", "size_in_bytes": 6518}, {"_path": "lib/site-packages/torchaudio/models/decoder/_ctc_decoder.py", "path_type": "hardlink", "sha256": "c28c946830ae68c42a3d343bb8bb9cf7d9713003ac263e40c56c12f617fa24d6", "sha256_in_prefix": "c28c946830ae68c42a3d343bb8bb9cf7d9713003ac263e40c56c12f617fa24d6", "size_in_bytes": 20650}, {"_path": "lib/site-packages/torchaudio/models/decoder/_cuda_ctc_decoder.py", "path_type": "hardlink", "sha256": "0590a301d679d2e9965b5ef59c9607cb6e186790b133c6b625f30878ee12dc13", "sha256_in_prefix": "0590a301d679d2e9965b5ef59c9607cb6e186790b133c6b625f30878ee12dc13", "size_in_bytes": 7373}, {"_path": "lib/site-packages/torchaudio/models/deepspeech.py", "path_type": "hardlink", "sha256": "9d561cdb1c16a453ba82ee4247499ba8b880cc99fc9407c771d70ff768b6da6a", "sha256_in_prefix": "9d561cdb1c16a453ba82ee4247499ba8b880cc99fc9407c771d70ff768b6da6a", "size_in_bytes": 2830}, {"_path": "lib/site-packages/torchaudio/models/emformer.py", "path_type": "hardlink", "sha256": "59b69e65cacf14e38b9f88a0ab0784d007ee17f490669aa0ed73c612197b0bc7", "sha256_in_prefix": "59b69e65cacf14e38b9f88a0ab0784d007ee17f490669aa0ed73c612197b0bc7", "size_in_bytes": 38650}, {"_path": "lib/site-packages/torchaudio/models/rnnt.py", "path_type": "hardlink", "sha256": "3cd26965ddef1fac11abc4c47f85253ed5476ed7bdc0e27e6d1304ba0ea0a74f", "sha256_in_prefix": "3cd26965ddef1fac11abc4c47f85253ed5476ed7bdc0e27e6d1304ba0ea0a74f", "size_in_bytes": 36357}, {"_path": "lib/site-packages/torchaudio/models/rnnt_decoder.py", "path_type": "hardlink", "sha256": "08104c66186ae4181ac74ff7a774990fe3acdd2d4b14707dd684e05440f86e66", "sha256_in_prefix": "08104c66186ae4181ac74ff7a774990fe3acdd2d4b14707dd684e05440f86e66", "size_in_bytes": 13178}, {"_path": "lib/site-packages/torchaudio/models/squim/__init__.py", "path_type": "hardlink", "sha256": "790a31f243ef88eb612ae969cd9bcf2b46bae8d1d6ecccd813868e17bbdafe45", "sha256_in_prefix": "790a31f243ef88eb612ae969cd9bcf2b46bae8d1d6ecccd813868e17bbdafe45", "size_in_bytes": 357}, {"_path": "lib/site-packages/torchaudio/models/squim/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "f2713710276ce1887503d992c48c91f54607cc4e90cee4882cf7151c8ae80f93", "sha256_in_prefix": "f2713710276ce1887503d992c48c91f54607cc4e90cee4882cf7151c8ae80f93", "size_in_bytes": 423}, {"_path": "lib/site-packages/torchaudio/models/squim/__pycache__/objective.cpython-310.pyc", "path_type": "hardlink", "sha256": "00e54e5df72f1c3a8404f7eab892093b9f2c8aeab8f3378007e04c2d735537a3", "sha256_in_prefix": "00e54e5df72f1c3a8404f7eab892093b9f2c8aeab8f3378007e04c2d735537a3", "size_in_bytes": 12066}, {"_path": "lib/site-packages/torchaudio/models/squim/__pycache__/subjective.cpython-310.pyc", "path_type": "hardlink", "sha256": "70cb18ea83bde0e9a4aabade23be7f6103ad97e15b7d5e3ff30e692d36f0ff5f", "sha256_in_prefix": "70cb18ea83bde0e9a4aabade23be7f6103ad97e15b7d5e3ff30e692d36f0ff5f", "size_in_bytes": 6362}, {"_path": "lib/site-packages/torchaudio/models/squim/objective.py", "path_type": "hardlink", "sha256": "d03b22a34710fcd04783bb746056c16a6ca258fa6871f684add76706db6ef1bd", "sha256_in_prefix": "d03b22a34710fcd04783bb746056c16a6ca258fa6871f684add76706db6ef1bd", "size_in_bytes": 12615}, {"_path": "lib/site-packages/torchaudio/models/squim/subjective.py", "path_type": "hardlink", "sha256": "d7f80af4ede7beb8e28a4a4fe3a21db0ccca76e493b7dd642a4940ebdc10aa2c", "sha256_in_prefix": "d7f80af4ede7beb8e28a4a4fe3a21db0ccca76e493b7dd642a4940ebdc10aa2c", "size_in_bytes": 5947}, {"_path": "lib/site-packages/torchaudio/models/tacotron2.py", "path_type": "hardlink", "sha256": "999e652d26bbe68a9cd21824737b089b9fe02be92786d817ddd9a0f7ea0b41aa", "sha256_in_prefix": "999e652d26bbe68a9cd21824737b089b9fe02be92786d817ddd9a0f7ea0b41aa", "size_in_bytes": 46960}, {"_path": "lib/site-packages/torchaudio/models/wav2letter.py", "path_type": "hardlink", "sha256": "a1eb71a47e511b44da75807be483a660eae7ada68fbd295c48da5165bd8513f0", "sha256_in_prefix": "a1eb71a47e511b44da75807be483a660eae7ada68fbd295c48da5165bd8513f0", "size_in_bytes": 3350}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/__init__.py", "path_type": "hardlink", "sha256": "8f915d4057ee229748298c2830ba29e016bbd061a84be94aeb5b54fa8346e708", "sha256_in_prefix": "8f915d4057ee229748298c2830ba29e016bbd061a84be94aeb5b54fa8346e708", "size_in_bytes": 972}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "7ea26aabf9cbfdd3dd02ef5daca13d9acfa47d772d919b706c4848b5d398ad36", "sha256_in_prefix": "7ea26aabf9cbfdd3dd02ef5daca13d9acfa47d772d919b706c4848b5d398ad36", "size_in_bytes": 823}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/__pycache__/components.cpython-310.pyc", "path_type": "hardlink", "sha256": "8291c75d2b1b955c14e4d347e494a10edab8fd92da0bd7a7fcf5f40960b6990f", "sha256_in_prefix": "8291c75d2b1b955c14e4d347e494a10edab8fd92da0bd7a7fcf5f40960b6990f", "size_in_bytes": 41795}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/__pycache__/model.cpython-310.pyc", "path_type": "hardlink", "sha256": "5142c4b2c82eb0ad171ec5ee562bba77a25a33db7ab3def4ef44becfddede78a", "sha256_in_prefix": "5142c4b2c82eb0ad171ec5ee562bba77a25a33db7ab3def4ef44becfddede78a", "size_in_bytes": 46981}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/__pycache__/wavlm_attention.cpython-310.pyc", "path_type": "hardlink", "sha256": "778fd7e70c8b136edb186a2a3086322855212fd6073ad2b766ca194a6eb3357b", "sha256_in_prefix": "778fd7e70c8b136edb186a2a3086322855212fd6073ad2b766ca194a6eb3357b", "size_in_bytes": 8559}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/components.py", "path_type": "hardlink", "sha256": "1339ae19ce6a1d53eb1c21aa6151d3bdd8ea3f6802ac17e71d2a132bd1ac675c", "sha256_in_prefix": "1339ae19ce6a1d53eb1c21aa6151d3bdd8ea3f6802ac17e71d2a132bd1ac675c", "size_in_bytes": 48244}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/model.py", "path_type": "hardlink", "sha256": "90fe902ac1753e395ec9432168f8f2762d29091cb8186500ad1581cdf0c999d1", "sha256_in_prefix": "90fe902ac1753e395ec9432168f8f2762d29091cb8186500ad1581cdf0c999d1", "size_in_bytes": 61671}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/utils/__init__.py", "path_type": "hardlink", "sha256": "d5ea3068e10a45ba7b25a8c536ffebe3b44426a9cc99789dba44bb32bc29ff94", "sha256_in_prefix": "d5ea3068e10a45ba7b25a8c536ffebe3b44426a9cc99789dba44bb32bc29ff94", "size_in_bytes": 188}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/utils/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "ae819e6113044c8ccfa68e63118d52a21a2428897d2931a65e5b9dacfed73517", "sha256_in_prefix": "ae819e6113044c8ccfa68e63118d52a21a2428897d2931a65e5b9dacfed73517", "size_in_bytes": 314}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/utils/__pycache__/import_fairseq.cpython-310.pyc", "path_type": "hardlink", "sha256": "df2912f56b0ef128d7109530110cbdaf73060627a3ceb63814195ae9d2fcd987", "sha256_in_prefix": "df2912f56b0ef128d7109530110cbdaf73060627a3ceb63814195ae9d2fcd987", "size_in_bytes": 7548}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/utils/__pycache__/import_huggingface.cpython-310.pyc", "path_type": "hardlink", "sha256": "a98e831a9e34967255de81ab0ac2cb85e0105d94b34e1762f9b87c0ccb11879a", "sha256_in_prefix": "a98e831a9e34967255de81ab0ac2cb85e0105d94b34e1762f9b87c0ccb11879a", "size_in_bytes": 4939}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/utils/import_fairseq.py", "path_type": "hardlink", "sha256": "b28ed3fa8b43342b1352dcc9454145196c9dd1c696977458fd46c53312780c91", "sha256_in_prefix": "b28ed3fa8b43342b1352dcc9454145196c9dd1c696977458fd46c53312780c91", "size_in_bytes": 9411}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/utils/import_huggingface.py", "path_type": "hardlink", "sha256": "34c2ba62b0080df3b0f23d6d57edd74f0c7f9b06c91ef83c95d4eb016473b483", "sha256_in_prefix": "34c2ba62b0080df3b0f23d6d57edd74f0c7f9b06c91ef83c95d4eb016473b483", "size_in_bytes": 6080}, {"_path": "lib/site-packages/torchaudio/models/wav2vec2/wavlm_attention.py", "path_type": "hardlink", "sha256": "89875ef60aec6ff45a12cf3b148e72932377cf4231d6996aa6c30dbda9008963", "sha256_in_prefix": "89875ef60aec6ff45a12cf3b148e72932377cf4231d6996aa6c30dbda9008963", "size_in_bytes": 11058}, {"_path": "lib/site-packages/torchaudio/models/wavernn.py", "path_type": "hardlink", "sha256": "2d180bdfa8c0e96cc8d4f033058e8fe76a023064933ab69707c7c4824c294b1c", "sha256_in_prefix": "2d180bdfa8c0e96cc8d4f033058e8fe76a023064933ab69707c7c4824c294b1c", "size_in_bytes": 15855}, {"_path": "lib/site-packages/torchaudio/pipelines/__init__.py", "path_type": "hardlink", "sha256": "a0cc0ebbed53fee8099a175aa08e4dac20eb5001aba433a52503bc87e6cb016e", "sha256_in_prefix": "a0cc0ebbed53fee8099a175aa08e4dac20eb5001aba433a52503bc87e6cb016e", "size_in_bytes": 2847}, {"_path": "lib/site-packages/torchaudio/pipelines/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "966df13d6ded621e4b7d69bba5e7eb45b5667243d315b56c9fde9b0068438694", "sha256_in_prefix": "966df13d6ded621e4b7d69bba5e7eb45b5667243d315b56c9fde9b0068438694", "size_in_bytes": 2039}, {"_path": "lib/site-packages/torchaudio/pipelines/__pycache__/_source_separation_pipeline.cpython-310.pyc", "path_type": "hardlink", "sha256": "f52ced1dcf5e683d2ade544d7b0b2a74e26368b114eefd78d83edf396e0bc901", "sha256_in_prefix": "f52ced1dcf5e683d2ade544d7b0b2a74e26368b114eefd78d83edf396e0bc901", "size_in_bytes": 4368}, {"_path": "lib/site-packages/torchaudio/pipelines/__pycache__/_squim_pipeline.cpython-310.pyc", "path_type": "hardlink", "sha256": "7b3bc5535e04a4bc7a01e0f19d719a1fd3fefd8f5233d381437e139ac6c52307", "sha256_in_prefix": "7b3bc5535e04a4bc7a01e0f19d719a1fd3fefd8f5233d381437e139ac6c52307", "size_in_bytes": 7338}, {"_path": "lib/site-packages/torchaudio/pipelines/__pycache__/rnnt_pipeline.cpython-310.pyc", "path_type": "hardlink", "sha256": "938fcca75a92340aa282bfd5a201f9b4660b4e834ad3211b0ba89f12cc2acd4d", "sha256_in_prefix": "938fcca75a92340aa282bfd5a201f9b4660b4e834ad3211b0ba89f12cc2acd4d", "size_in_bytes": 15447}, {"_path": "lib/site-packages/torchaudio/pipelines/_source_separation_pipeline.py", "path_type": "hardlink", "sha256": "5808e2592964ef7552f7ce46a4ac1e0c07e4d9a2b041623a8e718dc181e2462f", "sha256_in_prefix": "5808e2592964ef7552f7ce46a4ac1e0c07e4d9a2b041623a8e718dc181e2462f", "size_in_bytes": 4333}, {"_path": "lib/site-packages/torchaudio/pipelines/_squim_pipeline.py", "path_type": "hardlink", "sha256": "0a168d552430c13f207ba5c40c90a9a6ed96e42cfb360d58f4f90784f955c118", "sha256_in_prefix": "0a168d552430c13f207ba5c40c90a9a6ed96e42cfb360d58f4f90784f955c118", "size_in_bytes": 7346}, {"_path": "lib/site-packages/torchaudio/pipelines/_tts/__init__.py", "path_type": "hardlink", "sha256": "58a739734e9bfccf4cbc4a212598212495802fbbd7bdfc11224772f39502874e", "sha256_in_prefix": "58a739734e9bfccf4cbc4a212598212495802fbbd7bdfc11224772f39502874e", "size_in_bytes": 442}, {"_path": "lib/site-packages/torchaudio/pipelines/_tts/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "05d6147905653a52782a3aa5aeed5f267ba26c9230d24cdf34698e000c6ae11a", "sha256_in_prefix": "05d6147905653a52782a3aa5aeed5f267ba26c9230d24cdf34698e000c6ae11a", "size_in_bytes": 440}, {"_path": "lib/site-packages/torchaudio/pipelines/_tts/__pycache__/impl.cpython-310.pyc", "path_type": "hardlink", "sha256": "6ef30a21ac5a277e5f6d3317a071c0a8484670509a521f5cc99e4dac330cabc8", "sha256_in_prefix": "6ef30a21ac5a277e5f6d3317a071c0a8484670509a521f5cc99e4dac330cabc8", "size_in_bytes": 16168}, {"_path": "lib/site-packages/torchaudio/pipelines/_tts/__pycache__/interface.cpython-310.pyc", "path_type": "hardlink", "sha256": "d9c24745824a218b37b39e7dfa106a3e34b28e355b1492f50e9c029619c09613", "sha256_in_prefix": "d9c24745824a218b37b39e7dfa106a3e34b28e355b1492f50e9c029619c09613", "size_in_bytes": 11156}, {"_path": "lib/site-packages/torchaudio/pipelines/_tts/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "9818da9394e9176e9a38140b60bcd628bc67114cb4472c8dbd3b9dba665afe55", "sha256_in_prefix": "9818da9394e9176e9a38140b60bcd628bc67114cb4472c8dbd3b9dba665afe55", "size_in_bytes": 3791}, {"_path": "lib/site-packages/torchaudio/pipelines/_tts/impl.py", "path_type": "hardlink", "sha256": "c30ad3c9310491ec36d809f307f66495aa46680b2d2525016b0840edb39c1973", "sha256_in_prefix": "c30ad3c9310491ec36d809f307f66495aa46680b2d2525016b0840edb39c1973", "size_in_bytes": 15759}, {"_path": "lib/site-packages/torchaudio/pipelines/_tts/interface.py", "path_type": "hardlink", "sha256": "cb5994d38e3a572da11e90b032a459b75508e195e5f82e2d269f761329701e1d", "sha256_in_prefix": "cb5994d38e3a572da11e90b032a459b75508e195e5f82e2d269f761329701e1d", "size_in_bytes": 10479}, {"_path": "lib/site-packages/torchaudio/pipelines/_tts/utils.py", "path_type": "hardlink", "sha256": "66aa83f935c8eb390abd8b5d0079598a42a5a443b4d28b6f38a2534e35c53862", "sha256_in_prefix": "66aa83f935c8eb390abd8b5d0079598a42a5a443b4d28b6f38a2534e35c53862", "size_in_bytes": 4844}, {"_path": "lib/site-packages/torchaudio/pipelines/_wav2vec2/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/site-packages/torchaudio/pipelines/_wav2vec2/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "734e3d39adaaaf2a71913884657fe346b9d72cfa16cfc0ded795b82d9a5fd123", "sha256_in_prefix": "734e3d39adaaaf2a71913884657fe346b9d72cfa16cfc0ded795b82d9a5fd123", "size_in_bytes": 153}, {"_path": "lib/site-packages/torchaudio/pipelines/_wav2vec2/__pycache__/aligner.cpython-310.pyc", "path_type": "hardlink", "sha256": "02576294c2907f47114ac5dc3fbef65b749dace7a5a70a84b9dd784ff9c0592c", "sha256_in_prefix": "02576294c2907f47114ac5dc3fbef65b749dace7a5a70a84b9dd784ff9c0592c", "size_in_bytes": 4283}, {"_path": "lib/site-packages/torchaudio/pipelines/_wav2vec2/__pycache__/impl.cpython-310.pyc", "path_type": "hardlink", "sha256": "de45dbe01f74cae47b849b64d0095bdec9590ceec3098043b16ec0d4d5be35d5", "sha256_in_prefix": "de45dbe01f74cae47b849b64d0095bdec9590ceec3098043b16ec0d4d5be35d5", "size_in_bytes": 44177}, {"_path": "lib/site-packages/torchaudio/pipelines/_wav2vec2/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "a048e805448910c94e1cab1d9bcee82b3e0885e876137a3fb415202ae622b98b", "sha256_in_prefix": "a048e805448910c94e1cab1d9bcee82b3e0885e876137a3fb415202ae622b98b", "size_in_bytes": 4981}, {"_path": "lib/site-packages/torchaudio/pipelines/_wav2vec2/aligner.py", "path_type": "hardlink", "sha256": "1ce72d84582ea7ded0331f595c29a4bfa8ddc3fcf17514fe7bf4a295712e8cd5", "sha256_in_prefix": "1ce72d84582ea7ded0331f595c29a4bfa8ddc3fcf17514fe7bf4a295712e8cd5", "size_in_bytes": 2796}, {"_path": "lib/site-packages/torchaudio/pipelines/_wav2vec2/impl.py", "path_type": "hardlink", "sha256": "23a86d368e16b792cfc57f59f2b9b16ab144f0164e641505214f53f64d64d8fa", "sha256_in_prefix": "23a86d368e16b792cfc57f59f2b9b16ab144f0164e641505214f53f64d64d8fa", "size_in_bytes": 67260}, {"_path": "lib/site-packages/torchaudio/pipelines/_wav2vec2/utils.py", "path_type": "hardlink", "sha256": "0956b07d799519663c9a3fbfeabe0a3bdd3b06917aed601558710787270521a3", "sha256_in_prefix": "0956b07d799519663c9a3fbfeabe0a3bdd3b06917aed601558710787270521a3", "size_in_bytes": 7317}, {"_path": "lib/site-packages/torchaudio/pipelines/rnnt_pipeline.py", "path_type": "hardlink", "sha256": "4b40cb30f6edfa5a8d05639c8c6e4a3f621f5355ffa13bfde425664a3c582768", "sha256_in_prefix": "4b40cb30f6edfa5a8d05639c8c6e4a3f621f5355ffa13bfde425664a3c582768", "size_in_bytes": 14129}, {"_path": "lib/site-packages/torchaudio/prototype/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/site-packages/torchaudio/prototype/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "b8a445cab937dab475bb725e6ddcc8c5d6e2393f452fd3c8e8fe86df94815fca", "sha256_in_prefix": "b8a445cab937dab475bb725e6ddcc8c5d6e2393f452fd3c8e8fe86df94815fca", "size_in_bytes": 143}, {"_path": "lib/site-packages/torchaudio/prototype/datasets/__init__.py", "path_type": "hardlink", "sha256": "97693d57aba318e8a23d45ed26a313d2c6a8d33214a8a3a0fb6e373c056cecd3", "sha256_in_prefix": "97693d57aba318e8a23d45ed26a313d2c6a8d33214a8a3a0fb6e373c056cecd3", "size_in_bytes": 51}, {"_path": "lib/site-packages/torchaudio/prototype/datasets/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "a3cd9e6bb02184f5aa5279c1d5cb71eda6a5066f9acf8a3cc10ce13aa6ee35d1", "sha256_in_prefix": "a3cd9e6bb02184f5aa5279c1d5cb71eda6a5066f9acf8a3cc10ce13aa6ee35d1", "size_in_bytes": 209}, {"_path": "lib/site-packages/torchaudio/prototype/datasets/__pycache__/musan.cpython-310.pyc", "path_type": "hardlink", "sha256": "f81c37e233dff1f7057ddee8f7b4432a8f055f689e97a7c92443356ceba7a5ae", "sha256_in_prefix": "f81c37e233dff1f7057ddee8f7b4432a8f055f689e97a7c92443356ceba7a5ae", "size_in_bytes": 2746}, {"_path": "lib/site-packages/torchaudio/prototype/datasets/musan.py", "path_type": "hardlink", "sha256": "78dc283da8661c5a9d965985635d03e4bea4a3a6631d023d65dbc927c6fabc35", "sha256_in_prefix": "78dc283da8661c5a9d965985635d03e4bea4a3a6631d023d65dbc927c6fabc35", "size_in_bytes": 2163}, {"_path": "lib/site-packages/torchaudio/prototype/functional/__init__.py", "path_type": "hardlink", "sha256": "0b9b8faab9f073f549f5a8c9a6e76c0409b1b7af912ab1e2ac825add11cb95fb", "sha256_in_prefix": "0b9b8faab9f073f549f5a8c9a6e76c0409b1b7af912ab1e2ac825add11cb95fb", "size_in_bytes": 588}, {"_path": "lib/site-packages/torchaudio/prototype/functional/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "36c57dde154b865e32f23e64d00f5f90f5632c8fe8e625b9f3c220ab7b22f588", "sha256_in_prefix": "36c57dde154b865e32f23e64d00f5f90f5632c8fe8e625b9f3c220ab7b22f588", "size_in_bytes": 584}, {"_path": "lib/site-packages/torchaudio/prototype/functional/__pycache__/_dsp.cpython-310.pyc", "path_type": "hardlink", "sha256": "3b01bfe0a96394ea725a1f43aa82c4bc6fecc6de2261641b4b7386ed86b8c509", "sha256_in_prefix": "3b01bfe0a96394ea725a1f43aa82c4bc6fecc6de2261641b4b7386ed86b8c509", "size_in_bytes": 14808}, {"_path": "lib/site-packages/torchaudio/prototype/functional/__pycache__/_rir.cpython-310.pyc", "path_type": "hardlink", "sha256": "daa420a53409a2918473e1487a05ca9d58499cf7989e87eca70056992f768903", "sha256_in_prefix": "daa420a53409a2918473e1487a05ca9d58499cf7989e87eca70056992f768903", "size_in_bytes": 15276}, {"_path": "lib/site-packages/torchaudio/prototype/functional/__pycache__/functional.cpython-310.pyc", "path_type": "hardlink", "sha256": "d4e6c9a10a3a5c6027aaa0cc6e78d126cc6e24bf7e8febce7376254be3ffd096", "sha256_in_prefix": "d4e6c9a10a3a5c6027aaa0cc6e78d126cc6e24bf7e8febce7376254be3ffd096", "size_in_bytes": 5862}, {"_path": "lib/site-packages/torchaudio/prototype/functional/_dsp.py", "path_type": "hardlink", "sha256": "f268e560158872a0e275f96fbd962d508d48731e61101932404e1712b0dc1cac", "sha256_in_prefix": "f268e560158872a0e275f96fbd962d508d48731e61101932404e1712b0dc1cac", "size_in_bytes": 17071}, {"_path": "lib/site-packages/torchaudio/prototype/functional/_rir.py", "path_type": "hardlink", "sha256": "e7a4e8e794d9f49f334d798611bdcdd7875fe800e9b970167dd7f14427bc3840", "sha256_in_prefix": "e7a4e8e794d9f49f334d798611bdcdd7875fe800e9b970167dd7f14427bc3840", "size_in_bytes": 17634}, {"_path": "lib/site-packages/torchaudio/prototype/functional/functional.py", "path_type": "hardlink", "sha256": "d5a969f35617f31f431c1ac81f3e5183162b5be0c4bcf239f48a3da75f646fae", "sha256_in_prefix": "d5a969f35617f31f431c1ac81f3e5183162b5be0c4bcf239f48a3da75f646fae", "size_in_bytes": 6654}, {"_path": "lib/site-packages/torchaudio/prototype/models/__init__.py", "path_type": "hardlink", "sha256": "62e79ba3087eba45f6c31954fab19cd340558e00a1afff167f8df0a535d32eeb", "sha256_in_prefix": "62e79ba3087eba45f6c31954fab19cd340558e00a1afff167f8df0a535d32eeb", "size_in_bytes": 1290}, {"_path": "lib/site-packages/torchaudio/prototype/models/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "892660f4e312a964b6c1dc6264bace48269cb9b5ce100ba342cb80b70a89c8cd", "sha256_in_prefix": "892660f4e312a964b6c1dc6264bace48269cb9b5ce100ba342cb80b70a89c8cd", "size_in_bytes": 1077}, {"_path": "lib/site-packages/torchaudio/prototype/models/__pycache__/_conformer_wav2vec2.cpython-310.pyc", "path_type": "hardlink", "sha256": "23c95d84363606d3af7f8d0694c857067675b2ad0405837f0e75de1b5b0eadee", "sha256_in_prefix": "23c95d84363606d3af7f8d0694c857067675b2ad0405837f0e75de1b5b0eadee", "size_in_bytes": 25123}, {"_path": "lib/site-packages/torchaudio/prototype/models/__pycache__/_emformer_hubert.cpython-310.pyc", "path_type": "hardlink", "sha256": "27415405340a27f21d8b39f3e9f9c66ef0d7088e262e0d95b6e7c4977872ab8a", "sha256_in_prefix": "27415405340a27f21d8b39f3e9f9c66ef0d7088e262e0d95b6e7c4977872ab8a", "size_in_bytes": 12311}, {"_path": "lib/site-packages/torchaudio/prototype/models/__pycache__/conv_emformer.cpython-310.pyc", "path_type": "hardlink", "sha256": "8a4d03af4dd13178e846015ca0a9920563db68180bc4bf67999059871b6a50a7", "sha256_in_prefix": "8a4d03af4dd13178e846015ca0a9920563db68180bc4bf67999059871b6a50a7", "size_in_bytes": 17412}, {"_path": "lib/site-packages/torchaudio/prototype/models/__pycache__/hifi_gan.cpython-310.pyc", "path_type": "hardlink", "sha256": "9ee4a5e83f2813473930553e964f49460ce6cd93ff611dbe59f2a34605b03d06", "sha256_in_prefix": "9ee4a5e83f2813473930553e964f49460ce6cd93ff611dbe59f2a34605b03d06", "size_in_bytes": 10346}, {"_path": "lib/site-packages/torchaudio/prototype/models/__pycache__/rnnt.cpython-310.pyc", "path_type": "hardlink", "sha256": "070ee77c5bb8906d673ff3bac65b65a9edc5ba42a6b0d334fd46f99a4ed8cb34", "sha256_in_prefix": "070ee77c5bb8906d673ff3bac65b65a9edc5ba42a6b0d334fd46f99a4ed8cb34", "size_in_bytes": 23213}, {"_path": "lib/site-packages/torchaudio/prototype/models/__pycache__/rnnt_decoder.cpython-310.pyc", "path_type": "hardlink", "sha256": "522afdd9dee4b982f3800486ef6c179c2ced16a0375a8ce15278b3c2d60cfcd9", "sha256_in_prefix": "522afdd9dee4b982f3800486ef6c179c2ced16a0375a8ce15278b3c2d60cfcd9", "size_in_bytes": 14113}, {"_path": "lib/site-packages/torchaudio/prototype/models/_conformer_wav2vec2.py", "path_type": "hardlink", "sha256": "f497b9c69f3b893cc3f3ac5443787872cd175d0629f8d2c00beb4781d8a58e3a", "sha256_in_prefix": "f497b9c69f3b893cc3f3ac5443787872cd175d0629f8d2c00beb4781d8a58e3a", "size_in_bytes": 30316}, {"_path": "lib/site-packages/torchaudio/prototype/models/_emformer_hubert.py", "path_type": "hardlink", "sha256": "533fe128e3b32bcba5e6ba41c7fff5e7fa32fe9925a1c97c4af4609a34e0ee12", "sha256_in_prefix": "533fe128e3b32bcba5e6ba41c7fff5e7fa32fe9925a1c97c4af4609a34e0ee12", "size_in_bytes": 13831}, {"_path": "lib/site-packages/torchaudio/prototype/models/conv_emformer.py", "path_type": "hardlink", "sha256": "b71b72a276aa7571ba7e58bdd563759a401ce92145cd46777c5718ef5dcfe553", "sha256_in_prefix": "b71b72a276aa7571ba7e58bdd563759a401ce92145cd46777c5718ef5dcfe553", "size_in_bytes": 23601}, {"_path": "lib/site-packages/torchaudio/prototype/models/hifi_gan.py", "path_type": "hardlink", "sha256": "5fc98dd38c90a736ce2b12a613df271629a5b4e38aa78166b53d88a717383779", "sha256_in_prefix": "5fc98dd38c90a736ce2b12a613df271629a5b4e38aa78166b53d88a717383779", "size_in_bytes": 12816}, {"_path": "lib/site-packages/torchaudio/prototype/models/rnnt.py", "path_type": "hardlink", "sha256": "dfe3b9a585b7e5f7c41bc2b00950d07e11a9f8a8f43ac3f8c23cdd4e4750133a", "sha256_in_prefix": "dfe3b9a585b7e5f7c41bc2b00950d07e11a9f8a8f43ac3f8c23cdd4e4750133a", "size_in_bytes": 31570}, {"_path": "lib/site-packages/torchaudio/prototype/models/rnnt_decoder.py", "path_type": "hardlink", "sha256": "08ef32a353ac222d044129f71914f2b0fa14cd037e6a2356cabaefffceabcdce", "sha256_in_prefix": "08ef32a353ac222d044129f71914f2b0fa14cd037e6a2356cabaefffceabcdce", "size_in_bytes": 16134}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/__init__.py", "path_type": "hardlink", "sha256": "eb1f2adb426166b3d8c7e1ad2692f37aa7ad4b453ac41b7bd109fa72d1a9b941", "sha256_in_prefix": "eb1f2adb426166b3d8c7e1ad2692f37aa7ad4b453ac41b7bd109fa72d1a9b941", "size_in_bytes": 394}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "8de137a68631325a90191a3b42fed1c24174282601d044919c516f2d8dbec3cd", "sha256_in_prefix": "8de137a68631325a90191a3b42fed1c24174282601d044919c516f2d8dbec3cd", "size_in_bytes": 464}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/__pycache__/hifigan_pipeline.cpython-310.pyc", "path_type": "hardlink", "sha256": "29c7eed67387f3cecc2f51d6799068848bf33639d5c7a8e8c390d4db7131e519", "sha256_in_prefix": "29c7eed67387f3cecc2f51d6799068848bf33639d5c7a8e8c390d4db7131e519", "size_in_bytes": 9817}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/__pycache__/rnnt_pipeline.cpython-310.pyc", "path_type": "hardlink", "sha256": "9d642f566efed3210b802f92813534cd5c31b768843a83558992f9b56ea99fb4", "sha256_in_prefix": "9d642f566efed3210b802f92813534cd5c31b768843a83558992f9b56ea99fb4", "size_in_bytes": 2057}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/_vggish/__init__.py", "path_type": "hardlink", "sha256": "a64188ea4d20db55d9620e87f3444ee846af4ce141354c9090539a9ebf1cc2d6", "sha256_in_prefix": "a64188ea4d20db55d9620e87f3444ee846af4ce141354c9090539a9ebf1cc2d6", "size_in_bytes": 92}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/_vggish/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "dbed54ad9b963fc186bd2eeaf8c9b438c245de56b2e9a146401e8f44d0de667e", "sha256_in_prefix": "dbed54ad9b963fc186bd2eeaf8c9b438c245de56b2e9a146401e8f44d0de667e", "size_in_bytes": 260}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/_vggish/__pycache__/_vggish_impl.cpython-310.pyc", "path_type": "hardlink", "sha256": "07ed12cfaed8a29277500895aa2b30a46042a32e5e5246ded1d1bead620d80c9", "sha256_in_prefix": "07ed12cfaed8a29277500895aa2b30a46042a32e5e5246ded1d1bead620d80c9", "size_in_bytes": 5970}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/_vggish/__pycache__/_vggish_pipeline.cpython-310.pyc", "path_type": "hardlink", "sha256": "6274b7497f0a9dc3ff14339c50bd26769d4dfccef48daa68af482a4e4f32b88c", "sha256_in_prefix": "6274b7497f0a9dc3ff14339c50bd26769d4dfccef48daa68af482a4e4f32b88c", "size_in_bytes": 3447}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/_vggish/_vggish_impl.py", "path_type": "hardlink", "sha256": "d9e94c1a977af910852c14b85a4110484ac659534aea215334ccfde4fa53219e", "sha256_in_prefix": "d9e94c1a977af910852c14b85a4110484ac659534aea215334ccfde4fa53219e", "size_in_bytes": 8730}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/_vggish/_vggish_pipeline.py", "path_type": "hardlink", "sha256": "1acb22730926ed67df9e4d71514a053d2526f7fe4d6b4277319012e4e7587c6a", "sha256_in_prefix": "1acb22730926ed67df9e4d71514a053d2526f7fe4d6b4277319012e4e7587c6a", "size_in_bytes": 2795}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/hifigan_pipeline.py", "path_type": "hardlink", "sha256": "f87714df0e4070211c8d5797a4875d54b2667aee76a6c08ee284c83a0c39d160", "sha256_in_prefix": "f87714df0e4070211c8d5797a4875d54b2667aee76a6c08ee284c83a0c39d160", "size_in_bytes": 9882}, {"_path": "lib/site-packages/torchaudio/prototype/pipelines/rnnt_pipeline.py", "path_type": "hardlink", "sha256": "eb379ece81c83e8fbd49cd01d7d705a09ad03326e60cfd4c0e8519ccc16178ea", "sha256_in_prefix": "eb379ece81c83e8fbd49cd01d7d705a09ad03326e60cfd4c0e8519ccc16178ea", "size_in_bytes": 2242}, {"_path": "lib/site-packages/torchaudio/prototype/transforms/__init__.py", "path_type": "hardlink", "sha256": "6be2cd9aea6f510be9a370ab4efa97b9d818f06e9c446235cf2ea3f685924ffa", "sha256_in_prefix": "6be2cd9aea6f510be9a370ab4efa97b9d818f06e9c446235cf2ea3f685924ffa", "size_in_bytes": 234}, {"_path": "lib/site-packages/torchaudio/prototype/transforms/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "7d174078310bf27fe1a187c0150cd79a4f6c3757da5696f5c3c6c221ecad034f", "sha256_in_prefix": "7d174078310bf27fe1a187c0150cd79a4f6c3757da5696f5c3c6c221ecad034f", "size_in_bytes": 321}, {"_path": "lib/site-packages/torchaudio/prototype/transforms/__pycache__/_transforms.cpython-310.pyc", "path_type": "hardlink", "sha256": "9bd6cc6edc383bb168a0d4bd5f08e28669ee0ba83bddee70b1ed418d53ffd6e8", "sha256_in_prefix": "9bd6cc6edc383bb168a0d4bd5f08e28669ee0ba83bddee70b1ed418d53ffd6e8", "size_in_bytes": 17859}, {"_path": "lib/site-packages/torchaudio/prototype/transforms/_transforms.py", "path_type": "hardlink", "sha256": "0d292d5a42fb7a51ab36b3560b367491a07b0cd666a57d20bbff244c9cd791ff", "sha256_in_prefix": "0d292d5a42fb7a51ab36b3560b367491a07b0cd666a57d20bbff244c9cd791ff", "size_in_bytes": 19600}, {"_path": "lib/site-packages/torchaudio/sox_effects/__init__.py", "path_type": "hardlink", "sha256": "35537aac0907c62ce63ac6602e7c63317e6a5dc3e4001ccb13e86f4cc6926c4c", "sha256_in_prefix": "35537aac0907c62ce63ac6602e7c63317e6a5dc3e4001ccb13e86f4cc6926c4c", "size_in_bytes": 272}, {"_path": "lib/site-packages/torchaudio/sox_effects/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "f539951c1753b748587840bc9768ba2dad21447829d09a69a98d390815e021f3", "sha256_in_prefix": "f539951c1753b748587840bc9768ba2dad21447829d09a69a98d390815e021f3", "size_in_bytes": 357}, {"_path": "lib/site-packages/torchaudio/sox_effects/__pycache__/sox_effects.cpython-310.pyc", "path_type": "hardlink", "sha256": "789608a218eb8650e58172d06c8b4ae18206547a1d21c2d64ecf69db171c4427", "sha256_in_prefix": "789608a218eb8650e58172d06c8b4ae18206547a1d21c2d64ecf69db171c4427", "size_in_bytes": 11254}, {"_path": "lib/site-packages/torchaudio/sox_effects/sox_effects.py", "path_type": "hardlink", "sha256": "d4aecd832e44d62d647923225f418965b561ea7f0e347ed309bffad3abea0718", "sha256_in_prefix": "d4aecd832e44d62d647923225f418965b561ea7f0e347ed309bffad3abea0718", "size_in_bytes": 11253}, {"_path": "lib/site-packages/torchaudio/transforms/__init__.py", "path_type": "hardlink", "sha256": "19890f976f46715bbf43351f9e5c354277cdb2a8aa823b67d5999f84000c002a", "sha256_in_prefix": "19890f976f46715bbf43351f9e5c354277cdb2a8aa823b67d5999f84000c002a", "size_in_bytes": 1345}, {"_path": "lib/site-packages/torchaudio/transforms/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "6ea0d93741d501091501e9191bdf44e5d28e06b55a5f595c58f18b6d26c9b24b", "sha256_in_prefix": "6ea0d93741d501091501e9191bdf44e5d28e06b55a5f595c58f18b6d26c9b24b", "size_in_bytes": 1149}, {"_path": "lib/site-packages/torchaudio/transforms/__pycache__/_multi_channel.cpython-310.pyc", "path_type": "hardlink", "sha256": "f29cb832e3100f3bdb6085ca8136e364ddb04d612d0d05b861b5d30f72adce99", "sha256_in_prefix": "f29cb832e3100f3bdb6085ca8136e364ddb04d612d0d05b861b5d30f72adce99", "size_in_bytes": 20557}, {"_path": "lib/site-packages/torchaudio/transforms/__pycache__/_transforms.cpython-310.pyc", "path_type": "hardlink", "sha256": "17153792a251ea4ec3220dae3aa5392af623294529e5800bebcb04d10babab76", "sha256_in_prefix": "17153792a251ea4ec3220dae3aa5392af623294529e5800bebcb04d10babab76", "size_in_bytes": 81243}, {"_path": "lib/site-packages/torchaudio/transforms/_multi_channel.py", "path_type": "hardlink", "sha256": "32eb30edd4eedb91cd8ca78870a1d40eea819a3fc60b67b74da6a4a8971f7d6f", "sha256_in_prefix": "32eb30edd4eedb91cd8ca78870a1d40eea819a3fc60b67b74da6a4a8971f7d6f", "size_in_bytes": 22688}, {"_path": "lib/site-packages/torchaudio/transforms/_transforms.py", "path_type": "hardlink", "sha256": "6056db67c9d5d5f051d041a2352dc63ad14cfab2e4c835295dd3e1f3335a6f8d", "sha256_in_prefix": "6056db67c9d5d5f051d041a2352dc63ad14cfab2e4c835295dd3e1f3335a6f8d", "size_in_bytes": 89009}, {"_path": "lib/site-packages/torchaudio/utils/__init__.py", "path_type": "hardlink", "sha256": "87826fadbe2fcddc73809aa0cc0f93394a8b499da645500b11147c6a6ec196f4", "sha256_in_prefix": "87826fadbe2fcddc73809aa0cc0f93394a8b499da645500b11147c6a6ec196f4", "size_in_bytes": 185}, {"_path": "lib/site-packages/torchaudio/utils/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "d3ee56ff86b2c91edd237e163c0961ccbf69ce19b2908545a80d4cf95a7b5154", "sha256_in_prefix": "d3ee56ff86b2c91edd237e163c0961ccbf69ce19b2908545a80d4cf95a7b5154", "size_in_bytes": 309}, {"_path": "lib/site-packages/torchaudio/utils/__pycache__/download.cpython-310.pyc", "path_type": "hardlink", "sha256": "72505e9d78614a2cf7180fd55bd7b739ec64be23f228bc80c26b7964b6e31a96", "sha256_in_prefix": "72505e9d78614a2cf7180fd55bd7b739ec64be23f228bc80c26b7964b6e31a96", "size_in_bytes": 3213}, {"_path": "lib/site-packages/torchaudio/utils/__pycache__/ffmpeg_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "97f5bb798aceb66ab2a756e85ac837234eb3ff0a1bbfd1ac9647ea24de4daa37", "sha256_in_prefix": "97f5bb798aceb66ab2a756e85ac837234eb3ff0a1bbfd1ac9647ea24de4daa37", "size_in_bytes": 512}, {"_path": "lib/site-packages/torchaudio/utils/__pycache__/sox_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "80f86db39196f2964e6ff8d8ff087881ab32b6dd7e19e23b2dfcbb8aebd5f1b1", "sha256_in_prefix": "80f86db39196f2964e6ff8d8ff087881ab32b6dd7e19e23b2dfcbb8aebd5f1b1", "size_in_bytes": 2990}, {"_path": "lib/site-packages/torchaudio/utils/download.py", "path_type": "hardlink", "sha256": "4253b999ddeed1b5011535a366948131a31e7aa8192a6f4b9b0cdd079229eff7", "sha256_in_prefix": "4253b999ddeed1b5011535a366948131a31e7aa8192a6f4b9b0cdd079229eff7", "size_in_bytes": 2971}, {"_path": "lib/site-packages/torchaudio/utils/ffmpeg_utils.py", "path_type": "hardlink", "sha256": "d6be5c75b873f59098e635bee7f810e46dfad1ad9f13077ef8604532afd3c559", "sha256_in_prefix": "d6be5c75b873f59098e635bee7f810e46dfad1ad9f13077ef8604532afd3c559", "size_in_bytes": 330}, {"_path": "lib/site-packages/torchaudio/utils/sox_utils.py", "path_type": "hardlink", "sha256": "5a9bbd7042f711cb28bcd9d629621ca2c45203f2e63f55ea6d9efff6d8b98a62", "sha256_in_prefix": "5a9bbd7042f711cb28bcd9d629621ca2c45203f2e63f55ea6d9efff6d8b98a62", "size_in_bytes": 2520}, {"_path": "lib/site-packages/torchaudio/version.py", "path_type": "hardlink", "sha256": "236e51067fc62e3b2d49c93f3d181f223746d44d584e232a0e9fe4513ca04ca8", "sha256_in_prefix": "236e51067fc62e3b2d49c93f3d181f223746d44d584e232a0e9fe4513ca04ca8", "size_in_bytes": 81}, {"_path": "lib/site-packages/torio/__init__.py", "path_type": "hardlink", "sha256": "e91cf6f062f8e1a48eb335c97b0be375c9bc169e3b4e0a6134c3edb0805dd9a1", "sha256_in_prefix": "e91cf6f062f8e1a48eb335c97b0be375c9bc169e3b4e0a6134c3edb0805dd9a1", "size_in_bytes": 119}, {"_path": "lib/site-packages/torio/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "1364836b4da0948bf698146050664afe6b77b01fb1520dc2c040826a0088b6a3", "sha256_in_prefix": "1364836b4da0948bf698146050664afe6b77b01fb1520dc2c040826a0088b6a3", "size_in_bytes": 237}, {"_path": "lib/site-packages/torio/_extension/__init__.py", "path_type": "hardlink", "sha256": "f469c588b58f0958936d450d8a8f40b7533400b1aa2ad67d9453ae3d49f549cf", "sha256_in_prefix": "f469c588b58f0958936d450d8a8f40b7533400b1aa2ad67d9453ae3d49f549cf", "size_in_bytes": 326}, {"_path": "lib/site-packages/torio/_extension/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "444a7db29fd3cf72b150049ad76e709c57232ad76f4afd4dbd55efedb5114626", "sha256_in_prefix": "444a7db29fd3cf72b150049ad76e709c57232ad76f4afd4dbd55efedb5114626", "size_in_bytes": 452}, {"_path": "lib/site-packages/torio/_extension/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "01c85f4c93bb1ccaa9af44c88b9826b96cfa85f97da75145ea05a9dc97af6404", "sha256_in_prefix": "01c85f4c93bb1ccaa9af44c88b9826b96cfa85f97da75145ea05a9dc97af6404", "size_in_bytes": 5173}, {"_path": "lib/site-packages/torio/_extension/utils.py", "path_type": "hardlink", "sha256": "a6920604593cebccfb41b7d28dac0752448edf267b30bda31c5804fa3e86ca62", "sha256_in_prefix": "a6920604593cebccfb41b7d28dac0752448edf267b30bda31c5804fa3e86ca62", "size_in_bytes": 5051}, {"_path": "lib/site-packages/torio/io/__init__.py", "path_type": "hardlink", "sha256": "192b7ee03473822b9598d3775b08c300c002ced26276610fe608553a55ba3902", "sha256_in_prefix": "192b7ee03473822b9598d3775b08c300c002ced26276610fe608553a55ba3902", "size_in_bytes": 235}, {"_path": "lib/site-packages/torio/io/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "a34c8e45be5071e833e4633b8f931eb902b3515d2721abc16ba82a453a01e6d4", "sha256_in_prefix": "a34c8e45be5071e833e4633b8f931eb902b3515d2721abc16ba82a453a01e6d4", "size_in_bytes": 329}, {"_path": "lib/site-packages/torio/io/__pycache__/_streaming_media_decoder.cpython-310.pyc", "path_type": "hardlink", "sha256": "5451f61015af4ec295c7b96c9b5ad4edf95560caf15f0d04a37bd12c78d44c5d", "sha256_in_prefix": "5451f61015af4ec295c7b96c9b5ad4edf95560caf15f0d04a37bd12c78d44c5d", "size_in_bytes": 31424}, {"_path": "lib/site-packages/torio/io/__pycache__/_streaming_media_encoder.cpython-310.pyc", "path_type": "hardlink", "sha256": "ea75a8f8affb1bb598773e75eb0a9ccb72859f2915857e62d96ffdace333b454", "sha256_in_prefix": "ea75a8f8affb1bb598773e75eb0a9ccb72859f2915857e62d96ffdace333b454", "size_in_bytes": 19652}, {"_path": "lib/site-packages/torio/io/_streaming_media_decoder.py", "path_type": "hardlink", "sha256": "771d0af0f0f63d963bc916351bf02cfbff3e2f24032dd61f4593d6d649ab260d", "sha256_in_prefix": "771d0af0f0f63d963bc916351bf02cfbff3e2f24032dd61f4593d6d649ab260d", "size_in_bytes": 35354}, {"_path": "lib/site-packages/torio/io/_streaming_media_encoder.py", "path_type": "hardlink", "sha256": "0b8cc86aca2d7fb1a5910aad44adef302b7668de85906e8d2b6294c3465d1c7a", "sha256_in_prefix": "0b8cc86aca2d7fb1a5910aad44adef302b7668de85906e8d2b6294c3465d1c7a", "size_in_bytes": 20224}, {"_path": "lib/site-packages/torio/lib/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/site-packages/torio/lib/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "dbf4df30a1ae5d781c6e8f5599cd85535615e3983499962860f204bd4812768d", "sha256_in_prefix": "dbf4df30a1ae5d781c6e8f5599cd85535615e3983499962860f204bd4812768d", "size_in_bytes": 132}, {"_path": "lib/site-packages/torio/lib/_torio_ffmpeg4.pyd", "path_type": "hardlink", "sha256": "71566c4ff5342ed1fce7eaa6de9a2e962c0143c991c18c73e366accd4a53bed2", "sha256_in_prefix": "71566c4ff5342ed1fce7eaa6de9a2e962c0143c991c18c73e366accd4a53bed2", "size_in_bytes": 1731072}, {"_path": "lib/site-packages/torio/lib/_torio_ffmpeg5.pyd", "path_type": "hardlink", "sha256": "7ad5af4cb991a758f2a41407e72beca826bb9d4071aab0585075540bd24de1e3", "sha256_in_prefix": "7ad5af4cb991a758f2a41407e72beca826bb9d4071aab0585075540bd24de1e3", "size_in_bytes": 1731072}, {"_path": "lib/site-packages/torio/lib/_torio_ffmpeg6.pyd", "path_type": "hardlink", "sha256": "b1098fce7c63bfd48d0d52971e2d08325029dca9571bdcea571b9282a9900e99", "sha256_in_prefix": "b1098fce7c63bfd48d0d52971e2d08325029dca9571bdcea571b9282a9900e99", "size_in_bytes": 1731072}, {"_path": "lib/site-packages/torio/lib/libtorio_ffmpeg4.pyd", "path_type": "hardlink", "sha256": "9e8f2d936e0b707f9a055565c94d32eccf4d5b478dd5590b5e0222f4b21661ad", "sha256_in_prefix": "9e8f2d936e0b707f9a055565c94d32eccf4d5b478dd5590b5e0222f4b21661ad", "size_in_bytes": 1095680}, {"_path": "lib/site-packages/torio/lib/libtorio_ffmpeg5.pyd", "path_type": "hardlink", "sha256": "f34ea11be3466f9f44055986174c6cbfe64bbfa4e116f594218dbc55828ab003", "sha256_in_prefix": "f34ea11be3466f9f44055986174c6cbfe64bbfa4e116f594218dbc55828ab003", "size_in_bytes": 1095680}, {"_path": "lib/site-packages/torio/lib/libtorio_ffmpeg6.pyd", "path_type": "hardlink", "sha256": "60a494e0ca30dfec229d1dc6197b5cd8ba0c8f966071d8a53289956021fada5c", "sha256_in_prefix": "60a494e0ca30dfec229d1dc6197b5cd8ba0c8f966071d8a53289956021fada5c", "size_in_bytes": 1095680}, {"_path": "lib/site-packages/torio/utils/__init__.py", "path_type": "hardlink", "sha256": "b90579f129728a452beb21781c84c0482bee5fefdf9f36de0f1151cc5b9c404e", "sha256_in_prefix": "b90579f129728a452beb21781c84c0482bee5fefdf9f36de0f1151cc5b9c404e", "size_in_bytes": 60}, {"_path": "lib/site-packages/torio/utils/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "d7fd6cf199d35fa0758654c96003a12228947356d379c0f39c2d38657c896c12", "sha256_in_prefix": "d7fd6cf199d35fa0758654c96003a12228947356d379c0f39c2d38657c896c12", "size_in_bytes": 193}, {"_path": "lib/site-packages/torio/utils/__pycache__/ffmpeg_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "2ad16273be2aa64b9dd628b3ee083b8bc3bbb5879675c2aaead3f6d492f4a3b5", "sha256_in_prefix": "2ad16273be2aa64b9dd628b3ee083b8bc3bbb5879675c2aaead3f6d492f4a3b5", "size_in_bytes": 8808}, {"_path": "lib/site-packages/torio/utils/ffmpeg_utils.py", "path_type": "hardlink", "sha256": "dbeed74bb084641d3e33df8bb394e48b8bfb69718989583b5a8b805198eddd72", "sha256_in_prefix": "dbeed74bb084641d3e33df8bb394e48b8bfb69718989583b5a8b805198eddd72", "size_in_bytes": 8273}], "paths_version": 1}, "requested_spec": "torchaudio==2.3.1", "sha256": "83a409943214529d4aa7b986422fc832bb79532fc60216846315d759d7d994f0", "size": 7342684, "subdir": "win-64", "timestamp": 1716989394000, "url": "https://conda.anaconda.org/pytorch/win-64/torchaudio-2.3.1-py310_cu121.tar.bz2", "version": "2.3.1"}