{"build": "py310_cu121", "build_number": 0, "channel": "https://conda.anaconda.org/pytorch/win-64", "constrains": ["cpuonly <0"], "depends": ["libjpeg-turbo", "libpng", "numpy >=1.11", "pillow >=5.3.0,!=8.3.*", "python >=3.10,<3.11.0a0", "pytorch 2.3.1", "pytorch-cuda 12.1.*", "pytorch-mutex 1.0 cuda", "requests", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\torchvision-0.18.1-py310_cu121", "files": ["lib/site-packages/torchvision-0.18.1-py3.10.egg-info/PKG-INFO", "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/SOURCES.txt", "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/dependency_links.txt", "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/not-zip-safe", "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/requires.txt", "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/top_level.txt", "lib/site-packages/torchvision/_C.pyd", "lib/site-packages/torchvision/__init__.py", "lib/site-packages/torchvision/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/__pycache__/_internally_replaced_utils.cpython-310.pyc", "lib/site-packages/torchvision/__pycache__/_meta_registrations.cpython-310.pyc", "lib/site-packages/torchvision/__pycache__/_utils.cpython-310.pyc", "lib/site-packages/torchvision/__pycache__/extension.cpython-310.pyc", "lib/site-packages/torchvision/__pycache__/utils.cpython-310.pyc", "lib/site-packages/torchvision/__pycache__/version.cpython-310.pyc", "lib/site-packages/torchvision/_internally_replaced_utils.py", "lib/site-packages/torchvision/_meta_registrations.py", "lib/site-packages/torchvision/_utils.py", "lib/site-packages/torchvision/datasets/__init__.py", "lib/site-packages/torchvision/datasets/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/_optical_flow.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/_stereo_matching.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/caltech.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/celeba.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/cifar.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/cityscapes.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/clevr.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/coco.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/country211.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/dtd.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/eurosat.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/fakedata.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/fer2013.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/fgvc_aircraft.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/flickr.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/flowers102.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/folder.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/food101.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/gtsrb.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/hmdb51.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/imagenet.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/imagenette.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/inaturalist.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/kinetics.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/kitti.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/lfw.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/lsun.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/mnist.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/moving_mnist.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/omniglot.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/oxford_iiit_pet.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/pcam.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/phototour.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/places365.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/rendered_sst2.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/sbd.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/sbu.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/semeion.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/stanford_cars.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/stl10.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/sun397.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/svhn.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/ucf101.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/usps.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/utils.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/video_utils.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/vision.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/voc.cpython-310.pyc", "lib/site-packages/torchvision/datasets/__pycache__/widerface.cpython-310.pyc", "lib/site-packages/torchvision/datasets/_optical_flow.py", "lib/site-packages/torchvision/datasets/_stereo_matching.py", "lib/site-packages/torchvision/datasets/caltech.py", "lib/site-packages/torchvision/datasets/celeba.py", "lib/site-packages/torchvision/datasets/cifar.py", "lib/site-packages/torchvision/datasets/cityscapes.py", "lib/site-packages/torchvision/datasets/clevr.py", "lib/site-packages/torchvision/datasets/coco.py", "lib/site-packages/torchvision/datasets/country211.py", "lib/site-packages/torchvision/datasets/dtd.py", "lib/site-packages/torchvision/datasets/eurosat.py", "lib/site-packages/torchvision/datasets/fakedata.py", "lib/site-packages/torchvision/datasets/fer2013.py", "lib/site-packages/torchvision/datasets/fgvc_aircraft.py", "lib/site-packages/torchvision/datasets/flickr.py", "lib/site-packages/torchvision/datasets/flowers102.py", "lib/site-packages/torchvision/datasets/folder.py", "lib/site-packages/torchvision/datasets/food101.py", "lib/site-packages/torchvision/datasets/gtsrb.py", "lib/site-packages/torchvision/datasets/hmdb51.py", "lib/site-packages/torchvision/datasets/imagenet.py", "lib/site-packages/torchvision/datasets/imagenette.py", "lib/site-packages/torchvision/datasets/inaturalist.py", "lib/site-packages/torchvision/datasets/kinetics.py", "lib/site-packages/torchvision/datasets/kitti.py", "lib/site-packages/torchvision/datasets/lfw.py", "lib/site-packages/torchvision/datasets/lsun.py", "lib/site-packages/torchvision/datasets/mnist.py", "lib/site-packages/torchvision/datasets/moving_mnist.py", "lib/site-packages/torchvision/datasets/omniglot.py", "lib/site-packages/torchvision/datasets/oxford_iiit_pet.py", "lib/site-packages/torchvision/datasets/pcam.py", "lib/site-packages/torchvision/datasets/phototour.py", "lib/site-packages/torchvision/datasets/places365.py", "lib/site-packages/torchvision/datasets/rendered_sst2.py", "lib/site-packages/torchvision/datasets/samplers/__init__.py", "lib/site-packages/torchvision/datasets/samplers/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/datasets/samplers/__pycache__/clip_sampler.cpython-310.pyc", "lib/site-packages/torchvision/datasets/samplers/clip_sampler.py", "lib/site-packages/torchvision/datasets/sbd.py", "lib/site-packages/torchvision/datasets/sbu.py", "lib/site-packages/torchvision/datasets/semeion.py", "lib/site-packages/torchvision/datasets/stanford_cars.py", "lib/site-packages/torchvision/datasets/stl10.py", "lib/site-packages/torchvision/datasets/sun397.py", "lib/site-packages/torchvision/datasets/svhn.py", "lib/site-packages/torchvision/datasets/ucf101.py", "lib/site-packages/torchvision/datasets/usps.py", "lib/site-packages/torchvision/datasets/utils.py", "lib/site-packages/torchvision/datasets/video_utils.py", "lib/site-packages/torchvision/datasets/vision.py", "lib/site-packages/torchvision/datasets/voc.py", "lib/site-packages/torchvision/datasets/widerface.py", "lib/site-packages/torchvision/extension.py", "lib/site-packages/torchvision/image.pyd", "lib/site-packages/torchvision/io/__init__.py", "lib/site-packages/torchvision/io/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/io/__pycache__/_load_gpu_decoder.cpython-310.pyc", "lib/site-packages/torchvision/io/__pycache__/_video_opt.cpython-310.pyc", "lib/site-packages/torchvision/io/__pycache__/image.cpython-310.pyc", "lib/site-packages/torchvision/io/__pycache__/video.cpython-310.pyc", "lib/site-packages/torchvision/io/__pycache__/video_reader.cpython-310.pyc", "lib/site-packages/torchvision/io/_load_gpu_decoder.py", "lib/site-packages/torchvision/io/_video_opt.py", "lib/site-packages/torchvision/io/image.py", "lib/site-packages/torchvision/io/video.py", "lib/site-packages/torchvision/io/video_reader.py", "lib/site-packages/torchvision/models/__init__.py", "lib/site-packages/torchvision/models/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/_api.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/_meta.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/_utils.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/alexnet.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/convnext.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/densenet.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/efficientnet.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/feature_extraction.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/googlenet.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/inception.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/maxvit.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/mnasnet.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/mobilenet.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/mobilenetv2.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/mobilenetv3.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/regnet.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/resnet.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/shufflenetv2.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/squeezenet.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/swin_transformer.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/vgg.cpython-310.pyc", "lib/site-packages/torchvision/models/__pycache__/vision_transformer.cpython-310.pyc", "lib/site-packages/torchvision/models/_api.py", "lib/site-packages/torchvision/models/_meta.py", "lib/site-packages/torchvision/models/_utils.py", "lib/site-packages/torchvision/models/alexnet.py", "lib/site-packages/torchvision/models/convnext.py", "lib/site-packages/torchvision/models/densenet.py", "lib/site-packages/torchvision/models/detection/__init__.py", "lib/site-packages/torchvision/models/detection/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/_utils.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/anchor_utils.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/backbone_utils.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/faster_rcnn.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/fcos.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/generalized_rcnn.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/image_list.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/keypoint_rcnn.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/mask_rcnn.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/retinanet.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/roi_heads.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/rpn.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/ssd.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/ssdlite.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/__pycache__/transform.cpython-310.pyc", "lib/site-packages/torchvision/models/detection/_utils.py", "lib/site-packages/torchvision/models/detection/anchor_utils.py", "lib/site-packages/torchvision/models/detection/backbone_utils.py", "lib/site-packages/torchvision/models/detection/faster_rcnn.py", "lib/site-packages/torchvision/models/detection/fcos.py", "lib/site-packages/torchvision/models/detection/generalized_rcnn.py", "lib/site-packages/torchvision/models/detection/image_list.py", "lib/site-packages/torchvision/models/detection/keypoint_rcnn.py", "lib/site-packages/torchvision/models/detection/mask_rcnn.py", "lib/site-packages/torchvision/models/detection/retinanet.py", "lib/site-packages/torchvision/models/detection/roi_heads.py", "lib/site-packages/torchvision/models/detection/rpn.py", "lib/site-packages/torchvision/models/detection/ssd.py", "lib/site-packages/torchvision/models/detection/ssdlite.py", "lib/site-packages/torchvision/models/detection/transform.py", "lib/site-packages/torchvision/models/efficientnet.py", "lib/site-packages/torchvision/models/feature_extraction.py", "lib/site-packages/torchvision/models/googlenet.py", "lib/site-packages/torchvision/models/inception.py", "lib/site-packages/torchvision/models/maxvit.py", "lib/site-packages/torchvision/models/mnasnet.py", "lib/site-packages/torchvision/models/mobilenet.py", "lib/site-packages/torchvision/models/mobilenetv2.py", "lib/site-packages/torchvision/models/mobilenetv3.py", "lib/site-packages/torchvision/models/optical_flow/__init__.py", "lib/site-packages/torchvision/models/optical_flow/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/models/optical_flow/__pycache__/_utils.cpython-310.pyc", "lib/site-packages/torchvision/models/optical_flow/__pycache__/raft.cpython-310.pyc", "lib/site-packages/torchvision/models/optical_flow/_utils.py", "lib/site-packages/torchvision/models/optical_flow/raft.py", "lib/site-packages/torchvision/models/quantization/__init__.py", "lib/site-packages/torchvision/models/quantization/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/models/quantization/__pycache__/googlenet.cpython-310.pyc", "lib/site-packages/torchvision/models/quantization/__pycache__/inception.cpython-310.pyc", "lib/site-packages/torchvision/models/quantization/__pycache__/mobilenet.cpython-310.pyc", "lib/site-packages/torchvision/models/quantization/__pycache__/mobilenetv2.cpython-310.pyc", "lib/site-packages/torchvision/models/quantization/__pycache__/mobilenetv3.cpython-310.pyc", "lib/site-packages/torchvision/models/quantization/__pycache__/resnet.cpython-310.pyc", "lib/site-packages/torchvision/models/quantization/__pycache__/shufflenetv2.cpython-310.pyc", "lib/site-packages/torchvision/models/quantization/__pycache__/utils.cpython-310.pyc", "lib/site-packages/torchvision/models/quantization/googlenet.py", "lib/site-packages/torchvision/models/quantization/inception.py", "lib/site-packages/torchvision/models/quantization/mobilenet.py", "lib/site-packages/torchvision/models/quantization/mobilenetv2.py", "lib/site-packages/torchvision/models/quantization/mobilenetv3.py", "lib/site-packages/torchvision/models/quantization/resnet.py", "lib/site-packages/torchvision/models/quantization/shufflenetv2.py", "lib/site-packages/torchvision/models/quantization/utils.py", "lib/site-packages/torchvision/models/regnet.py", "lib/site-packages/torchvision/models/resnet.py", "lib/site-packages/torchvision/models/segmentation/__init__.py", "lib/site-packages/torchvision/models/segmentation/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/models/segmentation/__pycache__/_utils.cpython-310.pyc", "lib/site-packages/torchvision/models/segmentation/__pycache__/deeplabv3.cpython-310.pyc", "lib/site-packages/torchvision/models/segmentation/__pycache__/fcn.cpython-310.pyc", "lib/site-packages/torchvision/models/segmentation/__pycache__/lraspp.cpython-310.pyc", "lib/site-packages/torchvision/models/segmentation/_utils.py", "lib/site-packages/torchvision/models/segmentation/deeplabv3.py", "lib/site-packages/torchvision/models/segmentation/fcn.py", "lib/site-packages/torchvision/models/segmentation/lraspp.py", "lib/site-packages/torchvision/models/shufflenetv2.py", "lib/site-packages/torchvision/models/squeezenet.py", "lib/site-packages/torchvision/models/swin_transformer.py", "lib/site-packages/torchvision/models/vgg.py", "lib/site-packages/torchvision/models/video/__init__.py", "lib/site-packages/torchvision/models/video/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/models/video/__pycache__/mvit.cpython-310.pyc", "lib/site-packages/torchvision/models/video/__pycache__/resnet.cpython-310.pyc", "lib/site-packages/torchvision/models/video/__pycache__/s3d.cpython-310.pyc", "lib/site-packages/torchvision/models/video/__pycache__/swin_transformer.cpython-310.pyc", "lib/site-packages/torchvision/models/video/mvit.py", "lib/site-packages/torchvision/models/video/resnet.py", "lib/site-packages/torchvision/models/video/s3d.py", "lib/site-packages/torchvision/models/video/swin_transformer.py", "lib/site-packages/torchvision/models/vision_transformer.py", "lib/site-packages/torchvision/ops/__init__.py", "lib/site-packages/torchvision/ops/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/_box_convert.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/_register_onnx_ops.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/_utils.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/boxes.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/ciou_loss.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/deform_conv.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/diou_loss.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/drop_block.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/feature_pyramid_network.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/focal_loss.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/giou_loss.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/misc.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/poolers.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/ps_roi_align.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/ps_roi_pool.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/roi_align.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/roi_pool.cpython-310.pyc", "lib/site-packages/torchvision/ops/__pycache__/stochastic_depth.cpython-310.pyc", "lib/site-packages/torchvision/ops/_box_convert.py", "lib/site-packages/torchvision/ops/_register_onnx_ops.py", "lib/site-packages/torchvision/ops/_utils.py", "lib/site-packages/torchvision/ops/boxes.py", "lib/site-packages/torchvision/ops/ciou_loss.py", "lib/site-packages/torchvision/ops/deform_conv.py", "lib/site-packages/torchvision/ops/diou_loss.py", "lib/site-packages/torchvision/ops/drop_block.py", "lib/site-packages/torchvision/ops/feature_pyramid_network.py", "lib/site-packages/torchvision/ops/focal_loss.py", "lib/site-packages/torchvision/ops/giou_loss.py", "lib/site-packages/torchvision/ops/misc.py", "lib/site-packages/torchvision/ops/poolers.py", "lib/site-packages/torchvision/ops/ps_roi_align.py", "lib/site-packages/torchvision/ops/ps_roi_pool.py", "lib/site-packages/torchvision/ops/roi_align.py", "lib/site-packages/torchvision/ops/roi_pool.py", "lib/site-packages/torchvision/ops/stochastic_depth.py", "lib/site-packages/torchvision/transforms/__init__.py", "lib/site-packages/torchvision/transforms/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/transforms/__pycache__/_functional_pil.cpython-310.pyc", "lib/site-packages/torchvision/transforms/__pycache__/_functional_tensor.cpython-310.pyc", "lib/site-packages/torchvision/transforms/__pycache__/_functional_video.cpython-310.pyc", "lib/site-packages/torchvision/transforms/__pycache__/_presets.cpython-310.pyc", "lib/site-packages/torchvision/transforms/__pycache__/_transforms_video.cpython-310.pyc", "lib/site-packages/torchvision/transforms/__pycache__/autoaugment.cpython-310.pyc", "lib/site-packages/torchvision/transforms/__pycache__/functional.cpython-310.pyc", "lib/site-packages/torchvision/transforms/__pycache__/transforms.cpython-310.pyc", "lib/site-packages/torchvision/transforms/_functional_pil.py", "lib/site-packages/torchvision/transforms/_functional_tensor.py", "lib/site-packages/torchvision/transforms/_functional_video.py", "lib/site-packages/torchvision/transforms/_presets.py", "lib/site-packages/torchvision/transforms/_transforms_video.py", "lib/site-packages/torchvision/transforms/autoaugment.py", "lib/site-packages/torchvision/transforms/functional.py", "lib/site-packages/torchvision/transforms/transforms.py", "lib/site-packages/torchvision/transforms/v2/__init__.py", "lib/site-packages/torchvision/transforms/v2/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_augment.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_auto_augment.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_color.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_container.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_deprecated.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_geometry.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_meta.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_misc.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_temporal.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_transform.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_type_conversion.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/__pycache__/_utils.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/_augment.py", "lib/site-packages/torchvision/transforms/v2/_auto_augment.py", "lib/site-packages/torchvision/transforms/v2/_color.py", "lib/site-packages/torchvision/transforms/v2/_container.py", "lib/site-packages/torchvision/transforms/v2/_deprecated.py", "lib/site-packages/torchvision/transforms/v2/_geometry.py", "lib/site-packages/torchvision/transforms/v2/_meta.py", "lib/site-packages/torchvision/transforms/v2/_misc.py", "lib/site-packages/torchvision/transforms/v2/_temporal.py", "lib/site-packages/torchvision/transforms/v2/_transform.py", "lib/site-packages/torchvision/transforms/v2/_type_conversion.py", "lib/site-packages/torchvision/transforms/v2/_utils.py", "lib/site-packages/torchvision/transforms/v2/functional/__init__.py", "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_augment.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_color.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_deprecated.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_geometry.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_meta.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_misc.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_temporal.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_type_conversion.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_utils.cpython-310.pyc", "lib/site-packages/torchvision/transforms/v2/functional/_augment.py", "lib/site-packages/torchvision/transforms/v2/functional/_color.py", "lib/site-packages/torchvision/transforms/v2/functional/_deprecated.py", "lib/site-packages/torchvision/transforms/v2/functional/_geometry.py", "lib/site-packages/torchvision/transforms/v2/functional/_meta.py", "lib/site-packages/torchvision/transforms/v2/functional/_misc.py", "lib/site-packages/torchvision/transforms/v2/functional/_temporal.py", "lib/site-packages/torchvision/transforms/v2/functional/_type_conversion.py", "lib/site-packages/torchvision/transforms/v2/functional/_utils.py", "lib/site-packages/torchvision/tv_tensors/__init__.py", "lib/site-packages/torchvision/tv_tensors/__pycache__/__init__.cpython-310.pyc", "lib/site-packages/torchvision/tv_tensors/__pycache__/_bounding_boxes.cpython-310.pyc", "lib/site-packages/torchvision/tv_tensors/__pycache__/_dataset_wrapper.cpython-310.pyc", "lib/site-packages/torchvision/tv_tensors/__pycache__/_image.cpython-310.pyc", "lib/site-packages/torchvision/tv_tensors/__pycache__/_mask.cpython-310.pyc", "lib/site-packages/torchvision/tv_tensors/__pycache__/_torch_function_helpers.cpython-310.pyc", "lib/site-packages/torchvision/tv_tensors/__pycache__/_tv_tensor.cpython-310.pyc", "lib/site-packages/torchvision/tv_tensors/__pycache__/_video.cpython-310.pyc", "lib/site-packages/torchvision/tv_tensors/_bounding_boxes.py", "lib/site-packages/torchvision/tv_tensors/_dataset_wrapper.py", "lib/site-packages/torchvision/tv_tensors/_image.py", "lib/site-packages/torchvision/tv_tensors/_mask.py", "lib/site-packages/torchvision/tv_tensors/_torch_function_helpers.py", "lib/site-packages/torchvision/tv_tensors/_tv_tensor.py", "lib/site-packages/torchvision/tv_tensors/_video.py", "lib/site-packages/torchvision/utils.py", "lib/site-packages/torchvision/version.py"], "fn": "torchvision-0.18.1-py310_cu121.tar.bz2", "license": "BSD", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\torchvision-0.18.1-py310_cu121", "type": 1}, "md5": "ca7fa2b7df8f846f5732a81e620f736d", "name": "torchvision", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\torchvision-0.18.1-py310_cu121.tar.bz2", "paths_data": {"paths": [{"_path": "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "009f5968b637b08478e1697bec578d0bca5a7ee0d050ed497441185ceaf5238a", "sha256_in_prefix": "009f5968b637b08478e1697bec578d0bca5a7ee0d050ed497441185ceaf5238a", "size_in_bytes": 6774}, {"_path": "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "3f69895c24eaa194e0009a651426f124b22f1c51428ee554e7474912fd4b96c1", "sha256_in_prefix": "3f69895c24eaa194e0009a651426f124b22f1c51428ee554e7474912fd4b96c1", "size_in_bytes": 12240}, {"_path": "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/not-zip-safe", "path_type": "hardlink", "sha256": "7eb70257593da06f682a3ddda54a9d260d4fc514f645237f5ca74b08f8da61a6", "sha256_in_prefix": "7eb70257593da06f682a3ddda54a9d260d4fc514f645237f5ca74b08f8da61a6", "size_in_bytes": 2}, {"_path": "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/requires.txt", "path_type": "hardlink", "sha256": "742b662c533cb54f7147d285e0a63dbe5cdccf36c611c8cab8b1c4b74008da5c", "sha256_in_prefix": "742b662c533cb54f7147d285e0a63dbe5cdccf36c611c8cab8b1c4b74008da5c", "size_in_bytes": 56}, {"_path": "lib/site-packages/torchvision-0.18.1-py3.10.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "b9c259a1a96e056f411984f84ee084eb3a1963f26e48fdf4c1b0e1f88469c545", "sha256_in_prefix": "b9c259a1a96e056f411984f84ee084eb3a1963f26e48fdf4c1b0e1f88469c545", "size_in_bytes": 12}, {"_path": "lib/site-packages/torchvision/_C.pyd", "path_type": "hardlink", "sha256": "1083161f8639e0874adc864e48993ccbe6deed271415b981a635cf952be77470", "sha256_in_prefix": "1083161f8639e0874adc864e48993ccbe6deed271415b981a635cf952be77470", "size_in_bytes": 6168576}, {"_path": "lib/site-packages/torchvision/__init__.py", "path_type": "hardlink", "sha256": "187e404da374664cfd2e1d8501f8ce7eab4f4746207a0429c6675149c6be54aa", "sha256_in_prefix": "187e404da374664cfd2e1d8501f8ce7eab4f4746207a0429c6675149c6be54aa", "size_in_bytes": 3471}, {"_path": "lib/site-packages/torchvision/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "239f5bf48aea13c77fba3f06104d9bfc67afac3d5a617f7d61ea6cadd8dd0a57", "sha256_in_prefix": "239f5bf48aea13c77fba3f06104d9bfc67afac3d5a617f7d61ea6cadd8dd0a57", "size_in_bytes": 3437}, {"_path": "lib/site-packages/torchvision/__pycache__/_internally_replaced_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "4ca64b12f3ff2b3214cfc65e04ebb939459d0c81bb66bba55e20753c36301579", "sha256_in_prefix": "4ca64b12f3ff2b3214cfc65e04ebb939459d0c81bb66bba55e20753c36301579", "size_in_bytes": 1477}, {"_path": "lib/site-packages/torchvision/__pycache__/_meta_registrations.cpython-310.pyc", "path_type": "hardlink", "sha256": "16dfe57abbe24e4414e78b0dc42a13fca31097f0567b92c301e187071f702d5e", "sha256_in_prefix": "16dfe57abbe24e4414e78b0dc42a13fca31097f0567b92c301e187071f702d5e", "size_in_bytes": 6755}, {"_path": "lib/site-packages/torchvision/__pycache__/_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "a1c7103f6dd9c1f9b75aa0e388d1a89e2ff0d91ad500c3658248d49fa7742673", "sha256_in_prefix": "a1c7103f6dd9c1f9b75aa0e388d1a89e2ff0d91ad500c3658248d49fa7742673", "size_in_bytes": 1411}, {"_path": "lib/site-packages/torchvision/__pycache__/extension.cpython-310.pyc", "path_type": "hardlink", "sha256": "5cb3b17cccbd5122fe40be02bfc7b914ba40917f202b154bdbc7e95658cd9401", "sha256_in_prefix": "5cb3b17cccbd5122fe40be02bfc7b914ba40917f202b154bdbc7e95658cd9401", "size_in_bytes": 2570}, {"_path": "lib/site-packages/torchvision/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "8d3578b8c30f05724ec29644cb71cbb2869ef9e66647e1303375a7471737c414", "sha256_in_prefix": "8d3578b8c30f05724ec29644cb71cbb2869ef9e66647e1303375a7471737c414", "size_in_bytes": 22007}, {"_path": "lib/site-packages/torchvision/__pycache__/version.cpython-310.pyc", "path_type": "hardlink", "sha256": "f650ea7ed7900f30f91e86413ade406f37e9c40203f9078c6c3dd65943995166", "sha256_in_prefix": "f650ea7ed7900f30f91e86413ade406f37e9c40203f9078c6c3dd65943995166", "size_in_bytes": 318}, {"_path": "lib/site-packages/torchvision/_internally_replaced_utils.py", "path_type": "hardlink", "sha256": "8e7107d948842900947f506ad9f2133cac7183e67248938d215a2788487c8466", "sha256_in_prefix": "8e7107d948842900947f506ad9f2133cac7183e67248938d215a2788487c8466", "size_in_bytes": 1439}, {"_path": "lib/site-packages/torchvision/_meta_registrations.py", "path_type": "hardlink", "sha256": "7efba53bdf1105c114879a303546ca140dbdc3d58ac24b1c2bf033aaee6f8f0e", "sha256_in_prefix": "7efba53bdf1105c114879a303546ca140dbdc3d58ac24b1c2bf033aaee6f8f0e", "size_in_bytes": 7437}, {"_path": "lib/site-packages/torchvision/_utils.py", "path_type": "hardlink", "sha256": "91c4a7e8fdd58efe503e01c736636ee7d321f8d2dbe4c0e5631bc390d256ace3", "sha256_in_prefix": "91c4a7e8fdd58efe503e01c736636ee7d321f8d2dbe4c0e5631bc390d256ace3", "size_in_bytes": 966}, {"_path": "lib/site-packages/torchvision/datasets/__init__.py", "path_type": "hardlink", "sha256": "22c45737579a4f213b59bebb06d05a0e066402e917a9889b95bce2278943e9a2", "sha256_in_prefix": "22c45737579a4f213b59bebb06d05a0e066402e917a9889b95bce2278943e9a2", "size_in_bytes": 3733}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "543e4c50253614eb47ce4920b2a889bedff73309dd044a9bb5269bc4973c03fd", "sha256_in_prefix": "543e4c50253614eb47ce4920b2a889bedff73309dd044a9bb5269bc4973c03fd", "size_in_bytes": 3285}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/_optical_flow.cpython-310.pyc", "path_type": "hardlink", "sha256": "dd869badb5e2b338dfdc9d9341c1252630a79429effe14cdb0e243df7ea024e5", "sha256_in_prefix": "dd869badb5e2b338dfdc9d9341c1252630a79429effe14cdb0e243df7ea024e5", "size_in_bytes": 17699}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/_stereo_matching.cpython-310.pyc", "path_type": "hardlink", "sha256": "636109543eb842e7736058640169811bd8dfcee86168e688b08afb980eaf0b1b", "sha256_in_prefix": "636109543eb842e7736058640169811bd8dfcee86168e688b08afb980eaf0b1b", "size_in_bytes": 39903}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/caltech.cpython-310.pyc", "path_type": "hardlink", "sha256": "41bd5912a9b52144618ddb4bca9d2c8d4f19ddc8381808a032b7dfd23f04c5a9", "sha256_in_prefix": "41bd5912a9b52144618ddb4bca9d2c8d4f19ddc8381808a032b7dfd23f04c5a9", "size_in_bytes": 8283}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/celeba.cpython-310.pyc", "path_type": "hardlink", "sha256": "90a50028c46f7c5804a813b05cb02d265eab4d7e2b1d699895251bff7ac72b07", "sha256_in_prefix": "90a50028c46f7c5804a813b05cb02d265eab4d7e2b1d699895251bff7ac72b07", "size_in_bytes": 7322}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/cifar.cpython-310.pyc", "path_type": "hardlink", "sha256": "f3698eaff8cf4eaca562554f0f6f2a9bb14bea891bdcda324b96bdedcdc9ae2f", "sha256_in_prefix": "f3698eaff8cf4eaca562554f0f6f2a9bb14bea891bdcda324b96bdedcdc9ae2f", "size_in_bytes": 5911}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/cityscapes.cpython-310.pyc", "path_type": "hardlink", "sha256": "7f4ab6227caf87675729ad39e2c3425329def8fbeb1fcf34bbd2c078f2224586", "sha256_in_prefix": "7f4ab6227caf87675729ad39e2c3425329def8fbeb1fcf34bbd2c078f2224586", "size_in_bytes": 8591}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/clevr.cpython-310.pyc", "path_type": "hardlink", "sha256": "53d41fd5501a88ee2acfee35ed49005d12bdc7c0447605ee3d469061d762cb71", "sha256_in_prefix": "53d41fd5501a88ee2acfee35ed49005d12bdc7c0447605ee3d469061d762cb71", "size_in_bytes": 4155}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/coco.cpython-310.pyc", "path_type": "hardlink", "sha256": "38431881a8cf90c461ac9fc337e810682eda06c0e08b37ca33610a609af2471b", "sha256_in_prefix": "38431881a8cf90c461ac9fc337e810682eda06c0e08b37ca33610a609af2471b", "size_in_bytes": 5205}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/country211.cpython-310.pyc", "path_type": "hardlink", "sha256": "fa95664f40552f5436d38ffe8ca9ec2b155dd41f02b3a0fe357cd1e814ef59c3", "sha256_in_prefix": "fa95664f40552f5436d38ffe8ca9ec2b155dd41f02b3a0fe357cd1e814ef59c3", "size_in_bytes": 2812}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/dtd.cpython-310.pyc", "path_type": "hardlink", "sha256": "0b0ea1451e0f2f8fce68824f4a683a9364484bb3e72cd910de3a9255daf26bad", "sha256_in_prefix": "0b0ea1451e0f2f8fce68824f4a683a9364484bb3e72cd910de3a9255daf26bad", "size_in_bytes": 4388}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/eurosat.cpython-310.pyc", "path_type": "hardlink", "sha256": "43940e21c4f0f889019dc5a0236b9819dd3d936cfcbdd76bcd8b9b9bd6920599", "sha256_in_prefix": "43940e21c4f0f889019dc5a0236b9819dd3d936cfcbdd76bcd8b9b9bd6920599", "size_in_bytes": 2518}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/fakedata.cpython-310.pyc", "path_type": "hardlink", "sha256": "f0033fef2f31bee4b51ac5cb9a1e83c703d67cc644eb3d22d7259c65919be686", "sha256_in_prefix": "f0033fef2f31bee4b51ac5cb9a1e83c703d67cc644eb3d22d7259c65919be686", "size_in_bytes": 2639}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/fer2013.cpython-310.pyc", "path_type": "hardlink", "sha256": "fbaa15ac883cc5000f3ab135842c49d9f855c0e10c564ee0f58b6b659f91bb9e", "sha256_in_prefix": "fbaa15ac883cc5000f3ab135842c49d9f855c0e10c564ee0f58b6b659f91bb9e", "size_in_bytes": 3340}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/fgvc_aircraft.cpython-310.pyc", "path_type": "hardlink", "sha256": "873f14d3a593586ddbcf7c0b67f46efaf695ca3d748dce43b281d2b943a8f563", "sha256_in_prefix": "873f14d3a593586ddbcf7c0b67f46efaf695ca3d748dce43b281d2b943a8f563", "size_in_bytes": 4889}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/flickr.cpython-310.pyc", "path_type": "hardlink", "sha256": "41a5359d5e8a3eed8e86bbb474f7ad7813871a7eb10a1835ee0df6c4ebf52281", "sha256_in_prefix": "41a5359d5e8a3eed8e86bbb474f7ad7813871a7eb10a1835ee0df6c4ebf52281", "size_in_bytes": 5497}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/flowers102.cpython-310.pyc", "path_type": "hardlink", "sha256": "bee7088c95e551bbe3d8509fd7aab00ca8120bee74b388e6d280ae5f2987bf0d", "sha256_in_prefix": "bee7088c95e551bbe3d8509fd7aab00ca8120bee74b388e6d280ae5f2987bf0d", "size_in_bytes": 4627}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/folder.cpython-310.pyc", "path_type": "hardlink", "sha256": "c34a018462601ce078c6d5aeb764768228b2f2572d3e19a2fb209caaadd911e3", "sha256_in_prefix": "c34a018462601ce078c6d5aeb764768228b2f2572d3e19a2fb209caaadd911e3", "size_in_bytes": 12243}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/food101.cpython-310.pyc", "path_type": "hardlink", "sha256": "82da6d0a1be0173afa3ae4857af8cf3d0ab7563b3d0dff4d6a44534d717931a0", "sha256_in_prefix": "82da6d0a1be0173afa3ae4857af8cf3d0ab7563b3d0dff4d6a44534d717931a0", "size_in_bytes": 4396}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/gtsrb.cpython-310.pyc", "path_type": "hardlink", "sha256": "fbfc66b35daabf78f202239c246200718fd01f5e19e17870ce0f24fe24dbff13", "sha256_in_prefix": "fbfc66b35daabf78f202239c246200718fd01f5e19e17870ce0f24fe24dbff13", "size_in_bytes": 3789}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/hmdb51.cpython-310.pyc", "path_type": "hardlink", "sha256": "40d08211b19d57e706cf5e92aa6dd84119cd550fa700957dd4d84c47961f6ddd", "sha256_in_prefix": "40d08211b19d57e706cf5e92aa6dd84119cd550fa700957dd4d84c47961f6ddd", "size_in_bytes": 5764}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/imagenet.cpython-310.pyc", "path_type": "hardlink", "sha256": "f88872f02b63985db343a171cc3375f10dc838efaa0516263241f395528bc9ec", "sha256_in_prefix": "f88872f02b63985db343a171cc3375f10dc838efaa0516263241f395528bc9ec", "size_in_bytes": 10184}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/imagenette.cpython-310.pyc", "path_type": "hardlink", "sha256": "68ac0141cb165f78c2ba83be70754f6c53d5c2d904b63795b9ac54e306435760", "sha256_in_prefix": "68ac0141cb165f78c2ba83be70754f6c53d5c2d904b63795b9ac54e306435760", "size_in_bytes": 4849}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/inaturalist.cpython-310.pyc", "path_type": "hardlink", "sha256": "b904bd54479c00196edc8080a6b80ab4ec28a1a11f67fb97f14ebe392b8bc6af", "sha256_in_prefix": "b904bd54479c00196edc8080a6b80ab4ec28a1a11f67fb97f14ebe392b8bc6af", "size_in_bytes": 8596}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/kinetics.cpython-310.pyc", "path_type": "hardlink", "sha256": "41afd86cf5d953487da550d46634073d0119266fdd635009880ac66e457b5eab", "sha256_in_prefix": "41afd86cf5d953487da550d46634073d0119266fdd635009880ac66e457b5eab", "size_in_bytes": 9828}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/kitti.cpython-310.pyc", "path_type": "hardlink", "sha256": "c6e2c2bc6a9969fbb15daae09270c18bb05e8bc8d89e6068df0754f3c9735582", "sha256_in_prefix": "c6e2c2bc6a9969fbb15daae09270c18bb05e8bc8d89e6068df0754f3c9735582", "size_in_bytes": 5956}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/lfw.cpython-310.pyc", "path_type": "hardlink", "sha256": "fd6ac4ce84515471ff8a27b6b54a488e7a689439737b8bb7b1c1519f20a90a08", "sha256_in_prefix": "fd6ac4ce84515471ff8a27b6b54a488e7a689439737b8bb7b1c1519f20a90a08", "size_in_bytes": 10690}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/lsun.cpython-310.pyc", "path_type": "hardlink", "sha256": "f6142c086be2ca53ee3d70d147dcc6b3f1db3acf076a5cc60b909f656e93ba47", "sha256_in_prefix": "f6142c086be2ca53ee3d70d147dcc6b3f1db3acf076a5cc60b909f656e93ba47", "size_in_bytes": 5904}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/mnist.cpython-310.pyc", "path_type": "hardlink", "sha256": "7e57fcbf39456f34080efbc391316610baaaf415950262564ba6b672d59c4acc", "sha256_in_prefix": "7e57fcbf39456f34080efbc391316610baaaf415950262564ba6b672d59c4acc", "size_in_bytes": 21357}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/moving_mnist.cpython-310.pyc", "path_type": "hardlink", "sha256": "16e93ca07bf7f769edaa8cf456b9628b176bdab5d7ffada5750bb80d62960063", "sha256_in_prefix": "16e93ca07bf7f769edaa8cf456b9628b176bdab5d7ffada5750bb80d62960063", "size_in_bytes": 3967}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/omniglot.cpython-310.pyc", "path_type": "hardlink", "sha256": "1f16bb537eec9af5ce2a195d821d0066f9663386569f8a828dadae7e60e437bf", "sha256_in_prefix": "1f16bb537eec9af5ce2a195d821d0066f9663386569f8a828dadae7e60e437bf", "size_in_bytes": 4758}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/oxford_iiit_pet.cpython-310.pyc", "path_type": "hardlink", "sha256": "e6dc48016a01a9c2b6bdd3ce59e2d8aab8e4c6ed03b9d2503968aa59e456de84", "sha256_in_prefix": "e6dc48016a01a9c2b6bdd3ce59e2d8aab8e4c6ed03b9d2503968aa59e456de84", "size_in_bytes": 5657}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/pcam.cpython-310.pyc", "path_type": "hardlink", "sha256": "2ce580eb8818c324f5ba9789c59404c972525847ecccc7571563ff61b5c463ba", "sha256_in_prefix": "2ce580eb8818c324f5ba9789c59404c972525847ecccc7571563ff61b5c463ba", "size_in_bytes": 4979}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/phototour.cpython-310.pyc", "path_type": "hardlink", "sha256": "3c053fec3afa07a544a906dbb35908d5003471ff8db544f0a2e164d40f6cf8b2", "sha256_in_prefix": "3c053fec3afa07a544a906dbb35908d5003471ff8db544f0a2e164d40f6cf8b2", "size_in_bytes": 7923}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/places365.cpython-310.pyc", "path_type": "hardlink", "sha256": "3c5342c73b3ac01db78eed273bd0d6b2d00e17ab5c4b0f6ad96ae505c62002de", "sha256_in_prefix": "3c5342c73b3ac01db78eed273bd0d6b2d00e17ab5c4b0f6ad96ae505c62002de", "size_in_bytes": 7955}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/rendered_sst2.cpython-310.pyc", "path_type": "hardlink", "sha256": "d76712dea42bfcf4cb5f22cd5fe506bab1578af33739864638a9a259d8b01cb8", "sha256_in_prefix": "d76712dea42bfcf4cb5f22cd5fe506bab1578af33739864638a9a259d8b01cb8", "size_in_bytes": 4058}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/sbd.cpython-310.pyc", "path_type": "hardlink", "sha256": "b351d37ca7490c070994199ad3284bdb7a067436ccab50fdf4751e06536af8e3", "sha256_in_prefix": "b351d37ca7490c070994199ad3284bdb7a067436ccab50fdf4751e06536af8e3", "size_in_bytes": 6037}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/sbu.cpython-310.pyc", "path_type": "hardlink", "sha256": "cffeb6ee0e42f4075ad66a1bc51bf6e5ef689cd4bd906342153150226f9a232d", "sha256_in_prefix": "cffeb6ee0e42f4075ad66a1bc51bf6e5ef689cd4bd906342153150226f9a232d", "size_in_bytes": 3950}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/semeion.cpython-310.pyc", "path_type": "hardlink", "sha256": "f98330aa2bba83b2d65f4239c8fdbf5094e4d9d3c536c5966779c7061b62f61f", "sha256_in_prefix": "f98330aa2bba83b2d65f4239c8fdbf5094e4d9d3c536c5966779c7061b62f61f", "size_in_bytes": 3389}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/stanford_cars.cpython-310.pyc", "path_type": "hardlink", "sha256": "6416878e9af2c370a5f799bb2e556eedef2e95c0a20b41d74997a8a3652f16c3", "sha256_in_prefix": "6416878e9af2c370a5f799bb2e556eedef2e95c0a20b41d74997a8a3652f16c3", "size_in_bytes": 4726}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/stl10.cpython-310.pyc", "path_type": "hardlink", "sha256": "195fc51ef0c8877590062690aca429211892e84789efbfc3008f3e328bb9a99b", "sha256_in_prefix": "195fc51ef0c8877590062690aca429211892e84789efbfc3008f3e328bb9a99b", "size_in_bytes": 6638}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/sun397.cpython-310.pyc", "path_type": "hardlink", "sha256": "31260a47fd3282f6a240c1f38c08fd1305565013b9ebae6cc34c403b9ebeaef8", "sha256_in_prefix": "31260a47fd3282f6a240c1f38c08fd1305565013b9ebae6cc34c403b9ebeaef8", "size_in_bytes": 3447}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/svhn.cpython-310.pyc", "path_type": "hardlink", "sha256": "4ee75eba97b832e0cf113fc07eb536afddb8796f4c3ea16b9680adad622a8bdb", "sha256_in_prefix": "4ee75eba97b832e0cf113fc07eb536afddb8796f4c3ea16b9680adad622a8bdb", "size_in_bytes": 4424}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/ucf101.cpython-310.pyc", "path_type": "hardlink", "sha256": "129a58fe26782ee4d684c614ecfa98b28e318db05d709093e1e08075b194f590", "sha256_in_prefix": "129a58fe26782ee4d684c614ecfa98b28e318db05d709093e1e08075b194f590", "size_in_bytes": 5914}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/usps.cpython-310.pyc", "path_type": "hardlink", "sha256": "cb4de40ba71e3a6dfaa97cf2fb16d51e05b1a34830ba148a3e61b6b755265785", "sha256_in_prefix": "cb4de40ba71e3a6dfaa97cf2fb16d51e05b1a34830ba148a3e61b6b755265785", "size_in_bytes": 4065}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "f175319f619308a0238995bce7c0dcfa311b196902bc160ca52a92b272dca95a", "sha256_in_prefix": "f175319f619308a0238995bce7c0dcfa311b196902bc160ca52a92b272dca95a", "size_in_bytes": 14780}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/video_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "0a75105696e8cf15d4c62a80b372278213fc076f0455d08d7a0e7e31c582405b", "sha256_in_prefix": "0a75105696e8cf15d4c62a80b372278213fc076f0455d08d7a0e7e31c582405b", "size_in_bytes": 13751}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/vision.cpython-310.pyc", "path_type": "hardlink", "sha256": "b104f636d72bb5becdf40207d785b460d616193670598c27fd451f1c38342b35", "sha256_in_prefix": "b104f636d72bb5becdf40207d785b460d616193670598c27fd451f1c38342b35", "size_in_bytes": 4928}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/voc.cpython-310.pyc", "path_type": "hardlink", "sha256": "3d11c148064eb7529fb347375ea0fcd652303abdc3727d64a40c1b6076c68caf", "sha256_in_prefix": "3d11c148064eb7529fb347375ea0fcd652303abdc3727d64a40c1b6076c68caf", "size_in_bytes": 8927}, {"_path": "lib/site-packages/torchvision/datasets/__pycache__/widerface.cpython-310.pyc", "path_type": "hardlink", "sha256": "2b6a6963702d9958a77c01ea975c2fe4449e4331657ddf602c3c23353d78775a", "sha256_in_prefix": "2b6a6963702d9958a77c01ea975c2fe4449e4331657ddf602c3c23353d78775a", "size_in_bytes": 6952}, {"_path": "lib/site-packages/torchvision/datasets/_optical_flow.py", "path_type": "hardlink", "sha256": "7b103549f80735dbc4c5e387ac2a0c9d051f842a3eedcd0ff7b924a989b67511", "sha256_in_prefix": "7b103549f80735dbc4c5e387ac2a0c9d051f842a3eedcd0ff7b924a989b67511", "size_in_bytes": 20141}, {"_path": "lib/site-packages/torchvision/datasets/_stereo_matching.py", "path_type": "hardlink", "sha256": "b88dc4cefc4981c6c8f7bb17922ab051dfd469f66ba4be7b40c7d0ee42b2e180", "sha256_in_prefix": "b88dc4cefc4981c6c8f7bb17922ab051dfd469f66ba4be7b40c7d0ee42b2e180", "size_in_bytes": 50307}, {"_path": "lib/site-packages/torchvision/datasets/caltech.py", "path_type": "hardlink", "sha256": "e659800c8d0a426c067000693266a2bebdaa6393683a07381e10c5f79db9b3a6", "sha256_in_prefix": "e659800c8d0a426c067000693266a2bebdaa6393683a07381e10c5f79db9b3a6", "size_in_bytes": 9175}, {"_path": "lib/site-packages/torchvision/datasets/celeba.py", "path_type": "hardlink", "sha256": "8444bec47b6a81b4da830494b0e9c0aef299f98ab05b0d0a368f70bc26ec504a", "sha256_in_prefix": "8444bec47b6a81b4da830494b0e9c0aef299f98ab05b0d0a368f70bc26ec504a", "size_in_bytes": 8664}, {"_path": "lib/site-packages/torchvision/datasets/cifar.py", "path_type": "hardlink", "sha256": "d87564a917567a4b3abc73cd954d4aef4ace0e6be0200c0e0eee5107f99e443b", "sha256_in_prefix": "d87564a917567a4b3abc73cd954d4aef4ace0e6be0200c0e0eee5107f99e443b", "size_in_bytes": 6018}, {"_path": "lib/site-packages/torchvision/datasets/cityscapes.py", "path_type": "hardlink", "sha256": "f1a026cb9d1e2202ed96a8bcb3b7e2cfa92abac32e75322fd04793f232e9b4ac", "sha256_in_prefix": "f1a026cb9d1e2202ed96a8bcb3b7e2cfa92abac32e75322fd04793f232e9b4ac", "size_in_bytes": 10515}, {"_path": "lib/site-packages/torchvision/datasets/clevr.py", "path_type": "hardlink", "sha256": "be58963920eca3bc115f936d7f059238ae2c2fe87403450feed72d0227bda37a", "sha256_in_prefix": "be58963920eca3bc115f936d7f059238ae2c2fe87403450feed72d0227bda37a", "size_in_bytes": 3548}, {"_path": "lib/site-packages/torchvision/datasets/coco.py", "path_type": "hardlink", "sha256": "7df30c583e2510496030f6f557debb3935354624add8c3e4960877877bc63a34", "sha256_in_prefix": "7df30c583e2510496030f6f557debb3935354624add8c3e4960877877bc63a34", "size_in_bytes": 4289}, {"_path": "lib/site-packages/torchvision/datasets/country211.py", "path_type": "hardlink", "sha256": "fe229cf40681a069bdbd64ddf59d886dd078843d8a8f32a8f57b1af1e0ef95f9", "sha256_in_prefix": "fe229cf40681a069bdbd64ddf59d886dd078843d8a8f32a8f57b1af1e0ef95f9", "size_in_bytes": 2494}, {"_path": "lib/site-packages/torchvision/datasets/dtd.py", "path_type": "hardlink", "sha256": "c42c6b8773eb9f3e937740f6ce9d0b63836ea6a50df6b8a14e46f583c23b988b", "sha256_in_prefix": "c42c6b8773eb9f3e937740f6ce9d0b63836ea6a50df6b8a14e46f583c23b988b", "size_in_bytes": 4119}, {"_path": "lib/site-packages/torchvision/datasets/eurosat.py", "path_type": "hardlink", "sha256": "85a19e2d4ca92452a8750240502dccc7209d77f9c0447358fe75a96d8d80e98e", "sha256_in_prefix": "85a19e2d4ca92452a8750240502dccc7209d77f9c0447358fe75a96d8d80e98e", "size_in_bytes": 2172}, {"_path": "lib/site-packages/torchvision/datasets/fakedata.py", "path_type": "hardlink", "sha256": "06a19588fc8845cbdd04da4686c3e5fe2b2bc71393b6514bc8a9cbc7305640ff", "sha256_in_prefix": "06a19588fc8845cbdd04da4686c3e5fe2b2bc71393b6514bc8a9cbc7305640ff", "size_in_bytes": 2514}, {"_path": "lib/site-packages/torchvision/datasets/fer2013.py", "path_type": "hardlink", "sha256": "3b13f01102c31eeab70ef9741e30c314f2324ddb20a6ecfdac8a0694f31c95b4", "sha256_in_prefix": "3b13f01102c31eeab70ef9741e30c314f2324ddb20a6ecfdac8a0694f31c95b4", "size_in_bytes": 2881}, {"_path": "lib/site-packages/torchvision/datasets/fgvc_aircraft.py", "path_type": "hardlink", "sha256": "225904d38f75f0967ca1ff1bc1f48fb6f7e461a7eb04850651ba60de2b1f428b", "sha256_in_prefix": "225904d38f75f0967ca1ff1bc1f48fb6f7e461a7eb04850651ba60de2b1f428b", "size_in_bytes": 4741}, {"_path": "lib/site-packages/torchvision/datasets/flickr.py", "path_type": "hardlink", "sha256": "911cefe87262cfc4c36bbfd247d81cd774b59ffe25a02cf98a47bb5215784b72", "sha256_in_prefix": "911cefe87262cfc4c36bbfd247d81cd774b59ffe25a02cf98a47bb5215784b72", "size_in_bytes": 5598}, {"_path": "lib/site-packages/torchvision/datasets/flowers102.py", "path_type": "hardlink", "sha256": "a6406aa83a8e23811ffce07927cfcff2cd1daaafd895d63592443485cdd2d0da", "sha256_in_prefix": "a6406aa83a8e23811ffce07927cfcff2cd1daaafd895d63592443485cdd2d0da", "size_in_bytes": 4755}, {"_path": "lib/site-packages/torchvision/datasets/folder.py", "path_type": "hardlink", "sha256": "c52f697f7e38af924ac6d35831d593bc247d34d8311967d4e0ec39c4988b4ef7", "sha256_in_prefix": "c52f697f7e38af924ac6d35831d593bc247d34d8311967d4e0ec39c4988b4ef7", "size_in_bytes": 13243}, {"_path": "lib/site-packages/torchvision/datasets/food101.py", "path_type": "hardlink", "sha256": "8e158a18caaa9083031eafdd4287103a06161dd67aa582accbd70307b30f888d", "sha256_in_prefix": "8e158a18caaa9083031eafdd4287103a06161dd67aa582accbd70307b30f888d", "size_in_bytes": 3845}, {"_path": "lib/site-packages/torchvision/datasets/gtsrb.py", "path_type": "hardlink", "sha256": "4d531eb09de3cd5935b60e1e709ca2ba7e654343ca87b94c6562e465b9156d46", "sha256_in_prefix": "4d531eb09de3cd5935b60e1e709ca2ba7e654343ca87b94c6562e465b9156d46", "size_in_bytes": 3888}, {"_path": "lib/site-packages/torchvision/datasets/hmdb51.py", "path_type": "hardlink", "sha256": "9c07cbd9399930d02ec6fea13755f78cd3fe0e604417996ce589e31e8eb84c8b", "sha256_in_prefix": "9c07cbd9399930d02ec6fea13755f78cd3fe0e604417996ce589e31e8eb84c8b", "size_in_bytes": 6123}, {"_path": "lib/site-packages/torchvision/datasets/imagenet.py", "path_type": "hardlink", "sha256": "7eda99dea7d106e2fe7c00171633702b337dd8a753fef4be3d4a8896715431f0", "sha256_in_prefix": "7eda99dea7d106e2fe7c00171633702b337dd8a753fef4be3d4a8896715431f0", "size_in_bytes": 8910}, {"_path": "lib/site-packages/torchvision/datasets/imagenette.py", "path_type": "hardlink", "sha256": "d49c4a891e48b590ae399a284a2772da66b7dec8143346055c8d6f6c7497e25d", "sha256_in_prefix": "d49c4a891e48b590ae399a284a2772da66b7dec8143346055c8d6f6c7497e25d", "size_in_bytes": 4560}, {"_path": "lib/site-packages/torchvision/datasets/inaturalist.py", "path_type": "hardlink", "sha256": "4560f7a87e7fe35d66e93a6789e52e8551ae59ffc8cf9a88bd60a4b970952404", "sha256_in_prefix": "4560f7a87e7fe35d66e93a6789e52e8551ae59ffc8cf9a88bd60a4b970952404", "size_in_bytes": 10403}, {"_path": "lib/site-packages/torchvision/datasets/kinetics.py", "path_type": "hardlink", "sha256": "48c8272c9eaa6aa99d5faabd1b216048f7d98a3e0f746fce3d72caa15f8fb85d", "sha256_in_prefix": "48c8272c9eaa6aa99d5faabd1b216048f7d98a3e0f746fce3d72caa15f8fb85d", "size_in_bytes": 10638}, {"_path": "lib/site-packages/torchvision/datasets/kitti.py", "path_type": "hardlink", "sha256": "ab4a2d49a9d4ca04c5ab83f64f7c4a7edd46bfe04f417c3ec4d34c7ce9833b37", "sha256_in_prefix": "ab4a2d49a9d4ca04c5ab83f64f7c4a7edd46bfe04f417c3ec4d34c7ce9833b37", "size_in_bytes": 5795}, {"_path": "lib/site-packages/torchvision/datasets/lfw.py", "path_type": "hardlink", "sha256": "dd759dc44b750fe33ecc6e29b2fabc64252f291d64b1043489721fa3cd25a6b0", "sha256_in_prefix": "dd759dc44b750fe33ecc6e29b2fabc64252f291d64b1043489721fa3cd25a6b0", "size_in_bytes": 10816}, {"_path": "lib/site-packages/torchvision/datasets/lsun.py", "path_type": "hardlink", "sha256": "a4e3d62c29d0058de56d4cc2591fd8b1184565e101daa168016c67b904ff97ac", "sha256_in_prefix": "a4e3d62c29d0058de56d4cc2591fd8b1184565e101daa168016c67b904ff97ac", "size_in_bytes": 5896}, {"_path": "lib/site-packages/torchvision/datasets/mnist.py", "path_type": "hardlink", "sha256": "2b2932f9ec7e0071393577b32f3a9bd4aa7dcd30396ea4bb988d0549fa5885f2", "sha256_in_prefix": "2b2932f9ec7e0071393577b32f3a9bd4aa7dcd30396ea4bb988d0549fa5885f2", "size_in_bytes": 22277}, {"_path": "lib/site-packages/torchvision/datasets/moving_mnist.py", "path_type": "hardlink", "sha256": "8ae5fec8d9a48bb6f2a7135f202a1b89f66169db492859931af15de3424fac8c", "sha256_in_prefix": "8ae5fec8d9a48bb6f2a7135f202a1b89f66169db492859931af15de3424fac8c", "size_in_bytes": 3740}, {"_path": "lib/site-packages/torchvision/datasets/omniglot.py", "path_type": "hardlink", "sha256": "cac673b7dbd0ede0f68a4a9018812f7d155c92eb20d89465e1df2b4021743c45", "sha256_in_prefix": "cac673b7dbd0ede0f68a4a9018812f7d155c92eb20d89465e1df2b4021743c45", "size_in_bytes": 4254}, {"_path": "lib/site-packages/torchvision/datasets/oxford_iiit_pet.py", "path_type": "hardlink", "sha256": "69e4d8f6cfb60fab9e5ff0b0a690008211e59137502c18323a8cff9114b32d69", "sha256_in_prefix": "69e4d8f6cfb60fab9e5ff0b0a690008211e59137502c18323a8cff9114b32d69", "size_in_bytes": 5215}, {"_path": "lib/site-packages/torchvision/datasets/pcam.py", "path_type": "hardlink", "sha256": "f2d59b25cc68ee52984d3a08485a4bebf3926f179812a53642c698e8e6234693", "sha256_in_prefix": "f2d59b25cc68ee52984d3a08485a4bebf3926f179812a53642c698e8e6234693", "size_in_bytes": 5419}, {"_path": "lib/site-packages/torchvision/datasets/phototour.py", "path_type": "hardlink", "sha256": "c8443f5f56c325003eef4235c7b3d3cbb17e9829fa52b5bbd2e6d4fb3dc053c8", "sha256_in_prefix": "c8443f5f56c325003eef4235c7b3d3cbb17e9829fa52b5bbd2e6d4fb3dc053c8", "size_in_bytes": 8271}, {"_path": "lib/site-packages/torchvision/datasets/places365.py", "path_type": "hardlink", "sha256": "8b3349afa134991deda081b8509b3cd8675cd4587f6b91492956bca95a5ecd3e", "sha256_in_prefix": "8b3349afa134991deda081b8509b3cd8675cd4587f6b91492956bca95a5ecd3e", "size_in_bytes": 7430}, {"_path": "lib/site-packages/torchvision/datasets/rendered_sst2.py", "path_type": "hardlink", "sha256": "fe0ba1bb7d39a00b71087f0a715e2b5303f909406e7050987f1fbe96906344af", "sha256_in_prefix": "fe0ba1bb7d39a00b71087f0a715e2b5303f909406e7050987f1fbe96906344af", "size_in_bytes": 3683}, {"_path": "lib/site-packages/torchvision/datasets/samplers/__init__.py", "path_type": "hardlink", "sha256": "c97f170f787e5704c2a2a17d21d42672e19b959c6f5dbd8422a73994fa975ed6", "sha256_in_prefix": "c97f170f787e5704c2a2a17d21d42672e19b959c6f5dbd8422a73994fa975ed6", "size_in_bytes": 164}, {"_path": "lib/site-packages/torchvision/datasets/samplers/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "b28c97275ac7a03def803b996fa3aea01e638ed87bc6fb839dff91fcc11d4207", "sha256_in_prefix": "b28c97275ac7a03def803b996fa3aea01e638ed87bc6fb839dff91fcc11d4207", "size_in_bytes": 296}, {"_path": "lib/site-packages/torchvision/datasets/samplers/__pycache__/clip_sampler.cpython-310.pyc", "path_type": "hardlink", "sha256": "73be1bde8b67eb049e466112488c3e5d1836132eb067fcac3511fb97d069ec7c", "sha256_in_prefix": "73be1bde8b67eb049e466112488c3e5d1836132eb067fcac3511fb97d069ec7c", "size_in_bytes": 6194}, {"_path": "lib/site-packages/torchvision/datasets/samplers/clip_sampler.py", "path_type": "hardlink", "sha256": "111a7d7343b764640d47794b5ef6575a37c99176b26329bc6e161f3a6fcc34a2", "sha256_in_prefix": "111a7d7343b764640d47794b5ef6575a37c99176b26329bc6e161f3a6fcc34a2", "size_in_bytes": 6416}, {"_path": "lib/site-packages/torchvision/datasets/sbd.py", "path_type": "hardlink", "sha256": "ea0b605378a9f53146b9a701a4a346ad525ca5eda5df53d70fa2d0391161d01b", "sha256_in_prefix": "ea0b605378a9f53146b9a701a4a346ad525ca5eda5df53d70fa2d0391161d01b", "size_in_bytes": 5388}, {"_path": "lib/site-packages/torchvision/datasets/sbu.py", "path_type": "hardlink", "sha256": "19602e5b3092dfdc241fbeff3f2dc42aed9e8fe4e27c36b11b28aea7aed12c5c", "sha256_in_prefix": "19602e5b3092dfdc241fbeff3f2dc42aed9e8fe4e27c36b11b28aea7aed12c5c", "size_in_bytes": 4253}, {"_path": "lib/site-packages/torchvision/datasets/semeion.py", "path_type": "hardlink", "sha256": "0cbcdbefa8a19a382d0333619d5d099ed4e3c2e580d6c7f4784705063f682482", "sha256_in_prefix": "0cbcdbefa8a19a382d0333619d5d099ed4e3c2e580d6c7f4784705063f682482", "size_in_bytes": 3240}, {"_path": "lib/site-packages/torchvision/datasets/stanford_cars.py", "path_type": "hardlink", "sha256": "444cbda008d560de632124422d00fc7a0d06301c7731951b61e17be9e402748f", "sha256_in_prefix": "444cbda008d560de632124422d00fc7a0d06301c7731951b61e17be9e402748f", "size_in_bytes": 4626}, {"_path": "lib/site-packages/torchvision/datasets/stl10.py", "path_type": "hardlink", "sha256": "30671f1e1f6f53320fad3b607180ca8dec0c8f0e2bd75bc92c91a284b3fe8514", "sha256_in_prefix": "30671f1e1f6f53320fad3b607180ca8dec0c8f0e2bd75bc92c91a284b3fe8514", "size_in_bytes": 7468}, {"_path": "lib/site-packages/torchvision/datasets/sun397.py", "path_type": "hardlink", "sha256": "c886eb287e827ee27797cca3406b1909a91018f71b2313466fa87ae1b4020d4e", "sha256_in_prefix": "c886eb287e827ee27797cca3406b1909a91018f71b2313466fa87ae1b4020d4e", "size_in_bytes": 2859}, {"_path": "lib/site-packages/torchvision/datasets/svhn.py", "path_type": "hardlink", "sha256": "9cffbad8ac4b90b0fa9fce25c39960c9b947c911560b93588812e81a07880f96", "sha256_in_prefix": "9cffbad8ac4b90b0fa9fce25c39960c9b947c911560b93588812e81a07880f96", "size_in_bytes": 4958}, {"_path": "lib/site-packages/torchvision/datasets/ucf101.py", "path_type": "hardlink", "sha256": "cccf4463a0a5a9dbede393d9c8bd1d66f9e52e7fb9a32083eaff76e1b10d94f1", "sha256_in_prefix": "cccf4463a0a5a9dbede393d9c8bd1d66f9e52e7fb9a32083eaff76e1b10d94f1", "size_in_bytes": 5664}, {"_path": "lib/site-packages/torchvision/datasets/usps.py", "path_type": "hardlink", "sha256": "bdaced30f535f289ceffbb565d458d43442c3cf628c4607eddaed8880de962bb", "sha256_in_prefix": "bdaced30f535f289ceffbb565d458d43442c3cf628c4607eddaed8880de962bb", "size_in_bytes": 3596}, {"_path": "lib/site-packages/torchvision/datasets/utils.py", "path_type": "hardlink", "sha256": "45f4f4d77065ba46ca22fa7d2c6f032fff85758b1c1a489e188e6f0bdb6e927e", "sha256_in_prefix": "45f4f4d77065ba46ca22fa7d2c6f032fff85758b1c1a489e188e6f0bdb6e927e", "size_in_bytes": 16804}, {"_path": "lib/site-packages/torchvision/datasets/video_utils.py", "path_type": "hardlink", "sha256": "8146b373d82fe30805b89d4de3b7b67385a2f3a1d6851f4ed3d1dcc15ada78b0", "sha256_in_prefix": "8146b373d82fe30805b89d4de3b7b67385a2f3a1d6851f4ed3d1dcc15ada78b0", "size_in_bytes": 17632}, {"_path": "lib/site-packages/torchvision/datasets/vision.py", "path_type": "hardlink", "sha256": "e4debdc6216c35d8aa8d5ccff903f7f8fb6cc0350db0a49ecb2fc5cd74c3b7fe", "sha256_in_prefix": "e4debdc6216c35d8aa8d5ccff903f7f8fb6cc0350db0a49ecb2fc5cd74c3b7fe", "size_in_bytes": 4360}, {"_path": "lib/site-packages/torchvision/datasets/voc.py", "path_type": "hardlink", "sha256": "ec68735725378966c1cc5c9bd73763fbdc710922e640276284f3bfa3ce5276a0", "sha256_in_prefix": "ec68735725378966c1cc5c9bd73763fbdc710922e640276284f3bfa3ce5276a0", "size_in_bytes": 9059}, {"_path": "lib/site-packages/torchvision/datasets/widerface.py", "path_type": "hardlink", "sha256": "506bc8f7b9ec042989846d0a28dc4ec0cf6290b181b9a6a65128e5cade23c33a", "sha256_in_prefix": "506bc8f7b9ec042989846d0a28dc4ec0cf6290b181b9a6a65128e5cade23c33a", "size_in_bytes": 8494}, {"_path": "lib/site-packages/torchvision/extension.py", "path_type": "hardlink", "sha256": "d00e1e7d0e95f1195070c32d0e92bbe152011e90efe2272392439cf84a243c7c", "sha256_in_prefix": "d00e1e7d0e95f1195070c32d0e92bbe152011e90efe2272392439cf84a243c7c", "size_in_bytes": 3233}, {"_path": "lib/site-packages/torchvision/image.pyd", "path_type": "hardlink", "sha256": "5061e3a3bdae7b88d455617f334b3c3c7c13d2f7b90edfb9c0628227ced3416d", "sha256_in_prefix": "5061e3a3bdae7b88d455617f334b3c3c7c13d2f7b90edfb9c0628227ced3416d", "size_in_bytes": 165888}, {"_path": "lib/site-packages/torchvision/io/__init__.py", "path_type": "hardlink", "sha256": "99de753cca836c263c5afa3dac0da297b66b67cec6b214eaf1f258df184d54e0", "sha256_in_prefix": "99de753cca836c263c5afa3dac0da297b66b67cec6b214eaf1f258df184d54e0", "size_in_bytes": 1547}, {"_path": "lib/site-packages/torchvision/io/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "001bdce2f2dc197081ae27210c69163ac5ac4d1b51f430d4357a08ef5f350635", "sha256_in_prefix": "001bdce2f2dc197081ae27210c69163ac5ac4d1b51f430d4357a08ef5f350635", "size_in_bytes": 1320}, {"_path": "lib/site-packages/torchvision/io/__pycache__/_load_gpu_decoder.cpython-310.pyc", "path_type": "hardlink", "sha256": "205d3790399be50daed050604d50d29e252208f68e2c1e6e8dd59c395b45794e", "sha256_in_prefix": "205d3790399be50daed050604d50d29e252208f68e2c1e6e8dd59c395b45794e", "size_in_bytes": 306}, {"_path": "lib/site-packages/torchvision/io/__pycache__/_video_opt.cpython-310.pyc", "path_type": "hardlink", "sha256": "92fb7cd16a683ca41277ccfc53b2511af32fc342ced0539e041ff1d6f5901d8e", "sha256_in_prefix": "92fb7cd16a683ca41277ccfc53b2511af32fc342ced0539e041ff1d6f5901d8e", "size_in_bytes": 15760}, {"_path": "lib/site-packages/torchvision/io/__pycache__/image.cpython-310.pyc", "path_type": "hardlink", "sha256": "6316191fac04ee3a2ec62afe444a5b966bb6af3e2a023d1558fe9708e12bf301", "sha256_in_prefix": "6316191fac04ee3a2ec62afe444a5b966bb6af3e2a023d1558fe9708e12bf301", "size_in_bytes": 10607}, {"_path": "lib/site-packages/torchvision/io/__pycache__/video.cpython-310.pyc", "path_type": "hardlink", "sha256": "f0257a91b600237b891468ffb42467ec9bcc07a6a24948c892bb63c0eec59477", "sha256_in_prefix": "f0257a91b600237b891468ffb42467ec9bcc07a6a24948c892bb63c0eec59477", "size_in_bytes": 11579}, {"_path": "lib/site-packages/torchvision/io/__pycache__/video_reader.cpython-310.pyc", "path_type": "hardlink", "sha256": "93f4fea0097c013c6bfd04f07ba449a3f4a9b4494cc0aa8ad9fc9922a3c12f96", "sha256_in_prefix": "93f4fea0097c013c6bfd04f07ba449a3f4a9b4494cc0aa8ad9fc9922a3c12f96", "size_in_bytes": 10010}, {"_path": "lib/site-packages/torchvision/io/_load_gpu_decoder.py", "path_type": "hardlink", "sha256": "07798f2d77ab2585ea887bfd14eda5691ac64652245e28bb0ac0f427c27f4b05", "sha256_in_prefix": "07798f2d77ab2585ea887bfd14eda5691ac64652245e28bb0ac0f427c27f4b05", "size_in_bytes": 182}, {"_path": "lib/site-packages/torchvision/io/_video_opt.py", "path_type": "hardlink", "sha256": "9f93cbe215c29ce55a2c39527cbb56ccff322b8ca9c166f2bd085b487aced0fb", "sha256_in_prefix": "9f93cbe215c29ce55a2c39527cbb56ccff322b8ca9c166f2bd085b487aced0fb", "size_in_bytes": 20902}, {"_path": "lib/site-packages/torchvision/io/image.py", "path_type": "hardlink", "sha256": "718a9c2dd1a6711b2704a61f395bee106ad8206abd6d7848229212dd7bb24966", "sha256_in_prefix": "718a9c2dd1a6711b2704a61f395bee106ad8206abd6d7848229212dd7bb24966", "size_in_bytes": 11120}, {"_path": "lib/site-packages/torchvision/io/video.py", "path_type": "hardlink", "sha256": "b5c0ca9f1db3fc05c080153908282d84d18bda53010e8313faf5e77d6f2acb31", "sha256_in_prefix": "b5c0ca9f1db3fc05c080153908282d84d18bda53010e8313faf5e77d6f2acb31", "size_in_bytes": 16089}, {"_path": "lib/site-packages/torchvision/io/video_reader.py", "path_type": "hardlink", "sha256": "461b8af8a72eb650ff0723e4343fed1c7fd0548c03b1da962f3f82d0a25cd043", "sha256_in_prefix": "461b8af8a72eb650ff0723e4343fed1c7fd0548c03b1da962f3f82d0a25cd043", "size_in_bytes": 11642}, {"_path": "lib/site-packages/torchvision/models/__init__.py", "path_type": "hardlink", "sha256": "e9095325fbe329c526309bf049aa5650d1576dfd95a35e5d551701b8d49a624a", "sha256_in_prefix": "e9095325fbe329c526309bf049aa5650d1576dfd95a35e5d551701b8d49a624a", "size_in_bytes": 888}, {"_path": "lib/site-packages/torchvision/models/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "93a3efea84dcddf9ad551ff25a4f0a47f6ac551229a0ce1c166deba0a9662c9f", "sha256_in_prefix": "93a3efea84dcddf9ad551ff25a4f0a47f6ac551229a0ce1c166deba0a9662c9f", "size_in_bytes": 780}, {"_path": "lib/site-packages/torchvision/models/__pycache__/_api.cpython-310.pyc", "path_type": "hardlink", "sha256": "6c2e3c5f2e56a3c8be25767aa58151228fc4c036990218e2296ac7c6e858b3a7", "sha256_in_prefix": "6c2e3c5f2e56a3c8be25767aa58151228fc4c036990218e2296ac7c6e858b3a7", "size_in_bytes": 9519}, {"_path": "lib/site-packages/torchvision/models/__pycache__/_meta.cpython-310.pyc", "path_type": "hardlink", "sha256": "052887489fa5228172b4621a7ba0e77d950e79aa339e85eaefa5717e05ed7d2f", "sha256_in_prefix": "052887489fa5228172b4621a7ba0e77d950e79aa339e85eaefa5717e05ed7d2f", "size_in_bytes": 19408}, {"_path": "lib/site-packages/torchvision/models/__pycache__/_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "af93777d3370f07eb19dee46f3f01af6bfafcdf7ecb2543401810e568f9ef905", "sha256_in_prefix": "af93777d3370f07eb19dee46f3f01af6bfafcdf7ecb2543401810e568f9ef905", "size_in_bytes": 9637}, {"_path": "lib/site-packages/torchvision/models/__pycache__/alexnet.cpython-310.pyc", "path_type": "hardlink", "sha256": "87da8844f0e8b1c421d2c6905a42fedefaffd70f56bae23076b09a94b9e9b957", "sha256_in_prefix": "87da8844f0e8b1c421d2c6905a42fedefaffd70f56bae23076b09a94b9e9b957", "size_in_bytes": 4360}, {"_path": "lib/site-packages/torchvision/models/__pycache__/convnext.cpython-310.pyc", "path_type": "hardlink", "sha256": "a22dd8b904ca2da35c777a0decbf12759f7f152a577c35e71fb7a50e11806fbf", "sha256_in_prefix": "a22dd8b904ca2da35c777a0decbf12759f7f152a577c35e71fb7a50e11806fbf", "size_in_bytes": 12519}, {"_path": "lib/site-packages/torchvision/models/__pycache__/densenet.cpython-310.pyc", "path_type": "hardlink", "sha256": "60a326339eed09fefd19dc6f6fcde606198837794ac424cc11ad1c91a19037de", "sha256_in_prefix": "60a326339eed09fefd19dc6f6fcde606198837794ac424cc11ad1c91a19037de", "size_in_bytes": 14172}, {"_path": "lib/site-packages/torchvision/models/__pycache__/efficientnet.cpython-310.pyc", "path_type": "hardlink", "sha256": "8e381bb8f95195566fa596028e30933012f95379cd8a8e62b62f2f5d82d561e6", "sha256_in_prefix": "8e381bb8f95195566fa596028e30933012f95379cd8a8e62b62f2f5d82d561e6", "size_in_bytes": 29180}, {"_path": "lib/site-packages/torchvision/models/__pycache__/feature_extraction.cpython-310.pyc", "path_type": "hardlink", "sha256": "e6af4aedddf22449d4f45088475589d0aa97491fe8c80b4b3be81f9b87bbf92f", "sha256_in_prefix": "e6af4aedddf22449d4f45088475589d0aa97491fe8c80b4b3be81f9b87bbf92f", "size_in_bytes": 20899}, {"_path": "lib/site-packages/torchvision/models/__pycache__/googlenet.cpython-310.pyc", "path_type": "hardlink", "sha256": "1c9c54968e785a07b4373021bd5fcb1a9268260cf3ce72f9a31a520388d2eb3d", "sha256_in_prefix": "1c9c54968e785a07b4373021bd5fcb1a9268260cf3ce72f9a31a520388d2eb3d", "size_in_bytes": 10242}, {"_path": "lib/site-packages/torchvision/models/__pycache__/inception.cpython-310.pyc", "path_type": "hardlink", "sha256": "0a04e6f7282a98c493ce235c857faf9e53555aee2e82a10bdee013bc1c49447b", "sha256_in_prefix": "0a04e6f7282a98c493ce235c857faf9e53555aee2e82a10bdee013bc1c49447b", "size_in_bytes": 14390}, {"_path": "lib/site-packages/torchvision/models/__pycache__/maxvit.cpython-310.pyc", "path_type": "hardlink", "sha256": "acf0fdebeb58426d6a0513fd332e1812f7ac0cadab83a7753e5f26210610f544", "sha256_in_prefix": "acf0fdebeb58426d6a0513fd332e1812f7ac0cadab83a7753e5f26210610f544", "size_in_bytes": 24536}, {"_path": "lib/site-packages/torchvision/models/__pycache__/mnasnet.cpython-310.pyc", "path_type": "hardlink", "sha256": "b06b2b3d9928b7b1e30f9e41b08275d57f6959b0bcf9a74e4224af32da0ab2a8", "sha256_in_prefix": "b06b2b3d9928b7b1e30f9e41b08275d57f6959b0bcf9a74e4224af32da0ab2a8", "size_in_bytes": 13675}, {"_path": "lib/site-packages/torchvision/models/__pycache__/mobilenet.cpython-310.pyc", "path_type": "hardlink", "sha256": "4c1308b2aa2f712d19e74755967869a5c17d982f367d26075fdce1dd3e59414f", "sha256_in_prefix": "4c1308b2aa2f712d19e74755967869a5c17d982f367d26075fdce1dd3e59414f", "size_in_bytes": 265}, {"_path": "lib/site-packages/torchvision/models/__pycache__/mobilenetv2.cpython-310.pyc", "path_type": "hardlink", "sha256": "bb9563d7bbcb75ebb0a7a1935de830671cc278efe318cf9ce209418e6da019ba", "sha256_in_prefix": "bb9563d7bbcb75ebb0a7a1935de830671cc278efe318cf9ce209418e6da019ba", "size_in_bytes": 7747}, {"_path": "lib/site-packages/torchvision/models/__pycache__/mobilenetv3.cpython-310.pyc", "path_type": "hardlink", "sha256": "3f55a2a0fb9f7f82f250333d1a653c3356888f6c6258b5d40908e098fa234b8d", "sha256_in_prefix": "3f55a2a0fb9f7f82f250333d1a653c3356888f6c6258b5d40908e098fa234b8d", "size_in_bytes": 12016}, {"_path": "lib/site-packages/torchvision/models/__pycache__/regnet.cpython-310.pyc", "path_type": "hardlink", "sha256": "4515f739d7f03162afc294b4eef43f9b899a0c03124f901306828aa9e01f3839", "sha256_in_prefix": "4515f739d7f03162afc294b4eef43f9b899a0c03124f901306828aa9e01f3839", "size_in_bytes": 37288}, {"_path": "lib/site-packages/torchvision/models/__pycache__/resnet.cpython-310.pyc", "path_type": "hardlink", "sha256": "a8a64b116f5cd5361fc0ad08b5b2376d8e52361ec9b316d2153da80bb191ad11", "sha256_in_prefix": "a8a64b116f5cd5361fc0ad08b5b2376d8e52361ec9b316d2153da80bb191ad11", "size_in_bytes": 25262}, {"_path": "lib/site-packages/torchvision/models/__pycache__/shufflenetv2.cpython-310.pyc", "path_type": "hardlink", "sha256": "512465d20545a14ea3f0b2593551eac41a5ebe3b9d35de9e974f05b1b1f93640", "sha256_in_prefix": "512465d20545a14ea3f0b2593551eac41a5ebe3b9d35de9e974f05b1b1f93640", "size_in_bytes": 12343}, {"_path": "lib/site-packages/torchvision/models/__pycache__/squeezenet.cpython-310.pyc", "path_type": "hardlink", "sha256": "fad35cb87933470e5d54188e19af7950010ea31b36c772cde2374a15cf074498", "sha256_in_prefix": "fad35cb87933470e5d54188e19af7950010ea31b36c772cde2374a15cf074498", "size_in_bytes": 7335}, {"_path": "lib/site-packages/torchvision/models/__pycache__/swin_transformer.cpython-310.pyc", "path_type": "hardlink", "sha256": "5a3f6338656761faf7401f87e3d1238904077a6f06ada8049e180cbc938c9d27", "sha256_in_prefix": "5a3f6338656761faf7401f87e3d1238904077a6f06ada8049e180cbc938c9d27", "size_in_bytes": 28838}, {"_path": "lib/site-packages/torchvision/models/__pycache__/vgg.cpython-310.pyc", "path_type": "hardlink", "sha256": "7599f6581e80ac0075cb9ca49a270d7c8342df534959bc0a2a84f1a6afbbd0f9", "sha256_in_prefix": "7599f6581e80ac0075cb9ca49a270d7c8342df534959bc0a2a84f1a6afbbd0f9", "size_in_bytes": 15384}, {"_path": "lib/site-packages/torchvision/models/__pycache__/vision_transformer.cpython-310.pyc", "path_type": "hardlink", "sha256": "fdc5b121e965a40a28054373a8f0019a9b7e2582761c2187cbfc107bc97ef1ca", "sha256_in_prefix": "fdc5b121e965a40a28054373a8f0019a9b7e2582761c2187cbfc107bc97ef1ca", "size_in_bytes": 21099}, {"_path": "lib/site-packages/torchvision/models/_api.py", "path_type": "hardlink", "sha256": "449baba6994afeaeed79e5259ea94cb934f1028aea20484472e432772873177b", "sha256_in_prefix": "449baba6994afeaeed79e5259ea94cb934f1028aea20484472e432772873177b", "size_in_bytes": 10331}, {"_path": "lib/site-packages/torchvision/models/_meta.py", "path_type": "hardlink", "sha256": "d8d488202a2ae0c0f33d9734d03946253807382c1305239b4937914e1dc4d2d4", "sha256_in_prefix": "d8d488202a2ae0c0f33d9734d03946253807382c1305239b4937914e1dc4d2d4", "size_in_bytes": 30429}, {"_path": "lib/site-packages/torchvision/models/_utils.py", "path_type": "hardlink", "sha256": "5fbcddb84f747e47a4f038ee933c8434e719d22a29d374742c8c42fc5b806b39", "sha256_in_prefix": "5fbcddb84f747e47a4f038ee933c8434e719d22a29d374742c8c42fc5b806b39", "size_in_bytes": 11149}, {"_path": "lib/site-packages/torchvision/models/alexnet.py", "path_type": "hardlink", "sha256": "5dc95d3f652e3a474e51f7718ad192f2a1f32c7efc372ed50b34f32ac6962135", "sha256_in_prefix": "5dc95d3f652e3a474e51f7718ad192f2a1f32c7efc372ed50b34f32ac6962135", "size_in_bytes": 4607}, {"_path": "lib/site-packages/torchvision/models/convnext.py", "path_type": "hardlink", "sha256": "98c2f75c02c6bd019edda6249b3e9d6c2ed6bacee7b595abec85628568cb57c3", "sha256_in_prefix": "98c2f75c02c6bd019edda6249b3e9d6c2ed6bacee7b595abec85628568cb57c3", "size_in_bytes": 15740}, {"_path": "lib/site-packages/torchvision/models/densenet.py", "path_type": "hardlink", "sha256": "5b1bde723f1bd3d532d858caba7ed96da8290e61e597c2f673719b33cd3622fb", "sha256_in_prefix": "5b1bde723f1bd3d532d858caba7ed96da8290e61e597c2f673719b33cd3622fb", "size_in_bytes": 17273}, {"_path": "lib/site-packages/torchvision/models/detection/__init__.py", "path_type": "hardlink", "sha256": "0f872cdf7f19e01427e53817d882ae242f530f6aedc36b2f50366500b47e9702", "sha256_in_prefix": "0f872cdf7f19e01427e53817d882ae242f530f6aedc36b2f50366500b47e9702", "size_in_bytes": 175}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "f13308446e1887a976720b8bfabd012193bdd1bbcc2661676ca39c3fec0b5885", "sha256_in_prefix": "f13308446e1887a976720b8bfabd012193bdd1bbcc2661676ca39c3fec0b5885", "size_in_bytes": 296}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "83c5ed3cc7f4a1e974b27805be31aa23cc03c3e064fb59d41f34efed7a3084f9", "sha256_in_prefix": "83c5ed3cc7f4a1e974b27805be31aa23cc03c3e064fb59d41f34efed7a3084f9", "size_in_bytes": 18271}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/anchor_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "2bd5b95df7b0f97fa3d9dbff36fa04a668203abb578c004e6262dc4afc96c66e", "sha256_in_prefix": "2bd5b95df7b0f97fa3d9dbff36fa04a668203abb578c004e6262dc4afc96c66e", "size_in_bytes": 10602}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/backbone_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "e25812c8853215698c37fadab8c54bca48de6c3a57946842f87555b41d4b1683", "sha256_in_prefix": "e25812c8853215698c37fadab8c54bca48de6c3a57946842f87555b41d4b1683", "size_in_bytes": 9296}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/faster_rcnn.cpython-310.pyc", "path_type": "hardlink", "sha256": "ea00be43fef20c05e1c7bba7b6de7ff0b757cf5824d1bf6dceff6077d6e8f004", "sha256_in_prefix": "ea00be43fef20c05e1c7bba7b6de7ff0b757cf5824d1bf6dceff6077d6e8f004", "size_in_bytes": 30155}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/fcos.cpython-310.pyc", "path_type": "hardlink", "sha256": "ed9a23eede76046cbc10b3c6d072a895674ef9d3400b5c10188d78ff7f2b7b84", "sha256_in_prefix": "ed9a23eede76046cbc10b3c6d072a895674ef9d3400b5c10188d78ff7f2b7b84", "size_in_bytes": 25770}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/generalized_rcnn.cpython-310.pyc", "path_type": "hardlink", "sha256": "4023daa1841a3f8868bab3d47b6d59aae45359c0f438506e6d51fd2127ccecb7", "sha256_in_prefix": "4023daa1841a3f8868bab3d47b6d59aae45359c0f438506e6d51fd2127ccecb7", "size_in_bytes": 3757}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/image_list.cpython-310.pyc", "path_type": "hardlink", "sha256": "bd99e4ee16f438502a03e13d3d0b00291b9cb875b595fc24a1bbc2fb03c69039", "sha256_in_prefix": "bd99e4ee16f438502a03e13d3d0b00291b9cb875b595fc24a1bbc2fb03c69039", "size_in_bytes": 1220}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/keypoint_rcnn.cpython-310.pyc", "path_type": "hardlink", "sha256": "638de2fd163db49b346cf3ec87c23c948e552758866b0fb95bddf10e54a5f9d3", "sha256_in_prefix": "638de2fd163db49b346cf3ec87c23c948e552758866b0fb95bddf10e54a5f9d3", "size_in_bytes": 19476}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/mask_rcnn.cpython-310.pyc", "path_type": "hardlink", "sha256": "b91914991533d0afd858f98aacdc6e98be08e64dcf483d4ad775e760b96c6b0e", "sha256_in_prefix": "b91914991533d0afd858f98aacdc6e98be08e64dcf483d4ad775e760b96c6b0e", "size_in_bytes": 22577}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/retinanet.cpython-310.pyc", "path_type": "hardlink", "sha256": "6a2c4dc325fdf830cf2a2f59ee367979dd77b8438b3b9eccda7a0a42e5e9083d", "sha256_in_prefix": "6a2c4dc325fdf830cf2a2f59ee367979dd77b8438b3b9eccda7a0a42e5e9083d", "size_in_bytes": 27637}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/roi_heads.cpython-310.pyc", "path_type": "hardlink", "sha256": "fc181a35dfccc54953f1e80bb7d9a6bbd21c134ed0c1027e0c09420ec3cc432a", "sha256_in_prefix": "fc181a35dfccc54953f1e80bb7d9a6bbd21c134ed0c1027e0c09420ec3cc432a", "size_in_bytes": 22114}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/rpn.cpython-310.pyc", "path_type": "hardlink", "sha256": "7aa45c40ecaef1c5ddda3789d09f4999d6e11a5c8bee998bb6d54f4e1d232985", "sha256_in_prefix": "7aa45c40ecaef1c5ddda3789d09f4999d6e11a5c8bee998bb6d54f4e1d232985", "size_in_bytes": 11817}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/ssd.cpython-310.pyc", "path_type": "hardlink", "sha256": "4f383935492b377a9603965febbd36a0c77f6e135c55178cafc3637bd7476114", "sha256_in_prefix": "4f383935492b377a9603965febbd36a0c77f6e135c55178cafc3637bd7476114", "size_in_bytes": 22092}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/ssdlite.cpython-310.pyc", "path_type": "hardlink", "sha256": "9a4c3d86497d1c7520b07fed7a409c2361fed6e6e10f1969accabb1fb5e3bc0c", "sha256_in_prefix": "9a4c3d86497d1c7520b07fed7a409c2361fed6e6e10f1969accabb1fb5e3bc0c", "size_in_bytes": 11359}, {"_path": "lib/site-packages/torchvision/models/detection/__pycache__/transform.cpython-310.pyc", "path_type": "hardlink", "sha256": "53205c1861b9a3bc2d64e3d9df9985710f28e19787e80cd975cca6cf850d3b8c", "sha256_in_prefix": "53205c1861b9a3bc2d64e3d9df9985710f28e19787e80cd975cca6cf850d3b8c", "size_in_bytes": 9962}, {"_path": "lib/site-packages/torchvision/models/detection/_utils.py", "path_type": "hardlink", "sha256": "9bd6e8c2a8ee62247d03b1c8ef02402bf92016b51894a4ae917384c2e52d4690", "sha256_in_prefix": "9bd6e8c2a8ee62247d03b1c8ef02402bf92016b51894a4ae917384c2e52d4690", "size_in_bytes": 22667}, {"_path": "lib/site-packages/torchvision/models/detection/anchor_utils.py", "path_type": "hardlink", "sha256": "4d029638a0c500bb13998c3f04c1880e54fd98d421ba49a09cd745b918cde3dc", "sha256_in_prefix": "4d029638a0c500bb13998c3f04c1880e54fd98d421ba49a09cd745b918cde3dc", "size_in_bytes": 12127}, {"_path": "lib/site-packages/torchvision/models/detection/backbone_utils.py", "path_type": "hardlink", "sha256": "041295c42c8063d43c25838b437a006d62717239f91c06cf7807036fee49a150", "sha256_in_prefix": "041295c42c8063d43c25838b437a006d62717239f91c06cf7807036fee49a150", "size_in_bytes": 10792}, {"_path": "lib/site-packages/torchvision/models/detection/faster_rcnn.py", "path_type": "hardlink", "sha256": "0aa3852fc776d3aaa012f96626b823bdc417319991e6ee0483f028984ce5616c", "sha256_in_prefix": "0aa3852fc776d3aaa012f96626b823bdc417319991e6ee0483f028984ce5616c", "size_in_bytes": 37576}, {"_path": "lib/site-packages/torchvision/models/detection/fcos.py", "path_type": "hardlink", "sha256": "f1f7eb98eb365996459540b3a0e5793621a40d9ecadb80ae268cef5f46e13568", "sha256_in_prefix": "f1f7eb98eb365996459540b3a0e5793621a40d9ecadb80ae268cef5f46e13568", "size_in_bytes": 34761}, {"_path": "lib/site-packages/torchvision/models/detection/generalized_rcnn.py", "path_type": "hardlink", "sha256": "9cb563da8f32afa056d4c375daedf442e15bbedb1d96511b5256cd1fe74ef86d", "sha256_in_prefix": "9cb563da8f32afa056d4c375daedf442e15bbedb1d96511b5256cd1fe74ef86d", "size_in_bytes": 4861}, {"_path": "lib/site-packages/torchvision/models/detection/image_list.py", "path_type": "hardlink", "sha256": "233163c4868c77215ab352073c1800b812b789824e7abb791f31aead8262b4d9", "sha256_in_prefix": "233163c4868c77215ab352073c1800b812b789824e7abb791f31aead8262b4d9", "size_in_bytes": 808}, {"_path": "lib/site-packages/torchvision/models/detection/keypoint_rcnn.py", "path_type": "hardlink", "sha256": "5c2670dafd2295c618907b7ada920ef52514874dc1f1414215378b70cb177fe5", "sha256_in_prefix": "5c2670dafd2295c618907b7ada920ef52514874dc1f1414215378b70cb177fe5", "size_in_bytes": 22198}, {"_path": "lib/site-packages/torchvision/models/detection/mask_rcnn.py", "path_type": "hardlink", "sha256": "3b5fa3bf279f4be694e891ac501da583320113c4e1b7e46c02969b1c38efc90d", "sha256_in_prefix": "3b5fa3bf279f4be694e891ac501da583320113c4e1b7e46c02969b1c38efc90d", "size_in_bytes": 27054}, {"_path": "lib/site-packages/torchvision/models/detection/retinanet.py", "path_type": "hardlink", "sha256": "12b77dab7f0385418273b34b27128648151a7e011405ed5f039cda1040b09b68", "sha256_in_prefix": "12b77dab7f0385418273b34b27128648151a7e011405ed5f039cda1040b09b68", "size_in_bytes": 37954}, {"_path": "lib/site-packages/torchvision/models/detection/roi_heads.py", "path_type": "hardlink", "sha256": "7ff2dd7baf491ee80601a8865a1eae809f565134fc7fb227c4faf2fcc6718196", "sha256_in_prefix": "7ff2dd7baf491ee80601a8865a1eae809f565134fc7fb227c4faf2fcc6718196", "size_in_bytes": 34698}, {"_path": "lib/site-packages/torchvision/models/detection/rpn.py", "path_type": "hardlink", "sha256": "cf87b3be0d574bdb8dabfde32185f32119ec021b860a5679009e4fd0d0f388df", "sha256_in_prefix": "cf87b3be0d574bdb8dabfde32185f32119ec021b860a5679009e4fd0d0f388df", "size_in_bytes": 16226}, {"_path": "lib/site-packages/torchvision/models/detection/ssd.py", "path_type": "hardlink", "sha256": "432596ec88a1a4d1b8fd259e568d7e5f927d90326b6ac77bf78f801c8703c506", "sha256_in_prefix": "432596ec88a1a4d1b8fd259e568d7e5f927d90326b6ac77bf78f801c8703c506", "size_in_bytes": 29661}, {"_path": "lib/site-packages/torchvision/models/detection/ssdlite.py", "path_type": "hardlink", "sha256": "0d190dd3b2cfed64cab7a4395883ab690477ed49f1ed722207bd1476198309c1", "sha256_in_prefix": "0d190dd3b2cfed64cab7a4395883ab690477ed49f1ed722207bd1476198309c1", "size_in_bytes": 13550}, {"_path": "lib/site-packages/torchvision/models/detection/transform.py", "path_type": "hardlink", "sha256": "828bb498618fccb9dc57664c1e44d3930e943b47d4a098a2e83af63743cd03f6", "sha256_in_prefix": "828bb498618fccb9dc57664c1e44d3930e943b47d4a098a2e83af63743cd03f6", "size_in_bytes": 12508}, {"_path": "lib/site-packages/torchvision/models/efficientnet.py", "path_type": "hardlink", "sha256": "b56e81a6c04fae884de9bd68d7fb074a2912a2a0eea504607604444e26237006", "sha256_in_prefix": "b56e81a6c04fae884de9bd68d7fb074a2912a2a0eea504607604444e26237006", "size_in_bytes": 44221}, {"_path": "lib/site-packages/torchvision/models/feature_extraction.py", "path_type": "hardlink", "sha256": "b93043d0e6dce360529b460153b55c48a251ec70de63a0dae673a2b86059b15f", "sha256_in_prefix": "b93043d0e6dce360529b460153b55c48a251ec70de63a0dae673a2b86059b15f", "size_in_bytes": 26140}, {"_path": "lib/site-packages/torchvision/models/googlenet.py", "path_type": "hardlink", "sha256": "02d5dc90d5ca7265a10f2a28cd5b2f6f754f23ea1d976b69b62633eca5d4df00", "sha256_in_prefix": "02d5dc90d5ca7265a10f2a28cd5b2f6f754f23ea1d976b69b62633eca5d4df00", "size_in_bytes": 13151}, {"_path": "lib/site-packages/torchvision/models/inception.py", "path_type": "hardlink", "sha256": "97db6eb703bb28d55ae677dd975ff97feea527343e352382cd73c0ac320b7800", "sha256_in_prefix": "97db6eb703bb28d55ae677dd975ff97feea527343e352382cd73c0ac320b7800", "size_in_bytes": 19329}, {"_path": "lib/site-packages/torchvision/models/maxvit.py", "path_type": "hardlink", "sha256": "9bc3df87b31871800dc70c6201a53da559b379698e0768d3a4b4a2b948ec1d92", "sha256_in_prefix": "9bc3df87b31871800dc70c6201a53da559b379698e0768d3a4b4a2b948ec1d92", "size_in_bytes": 32886}, {"_path": "lib/site-packages/torchvision/models/mnasnet.py", "path_type": "hardlink", "sha256": "3d3485e037216c582d39c85df648283ca09c7ca1fa342e167c76b96efe23679f", "sha256_in_prefix": "3d3485e037216c582d39c85df648283ca09c7ca1fa342e167c76b96efe23679f", "size_in_bytes": 18008}, {"_path": "lib/site-packages/torchvision/models/mobilenet.py", "path_type": "hardlink", "sha256": "6a5ac427092d99755c0a9854f21d84009657d1874a7dccf8b4910e746d815c1f", "sha256_in_prefix": "6a5ac427092d99755c0a9854f21d84009657d1874a7dccf8b4910e746d815c1f", "size_in_bytes": 217}, {"_path": "lib/site-packages/torchvision/models/mobilenetv2.py", "path_type": "hardlink", "sha256": "7958a5db7c8fe1ff830548632adef799aa3ce33122179634d701cdc21f0709d3", "sha256_in_prefix": "7958a5db7c8fe1ff830548632adef799aa3ce33122179634d701cdc21f0709d3", "size_in_bytes": 9970}, {"_path": "lib/site-packages/torchvision/models/mobilenetv3.py", "path_type": "hardlink", "sha256": "835525d5192884f1e15dfb1e720dabd16ecd56512055473b5ffb93d5bc71ad34", "sha256_in_prefix": "835525d5192884f1e15dfb1e720dabd16ecd56512055473b5ffb93d5bc71ad34", "size_in_bytes": 16702}, {"_path": "lib/site-packages/torchvision/models/optical_flow/__init__.py", "path_type": "hardlink", "sha256": "bae44501dbdc0e86dd01b636566c4467bfc22c7fdfe7e2519024ae251923e116", "sha256_in_prefix": "bae44501dbdc0e86dd01b636566c4467bfc22c7fdfe7e2519024ae251923e116", "size_in_bytes": 21}, {"_path": "lib/site-packages/torchvision/models/optical_flow/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "6581c57351455afc850013214bf0eada3ebe85c22e10056469fe31cfd3a4f110", "sha256_in_prefix": "6581c57351455afc850013214bf0eada3ebe85c22e10056469fe31cfd3a4f110", "size_in_bytes": 175}, {"_path": "lib/site-packages/torchvision/models/optical_flow/__pycache__/_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "67ffa23fea7ccef85097f2325c9a6432276e9a6add62eea81175b99da01af235", "sha256_in_prefix": "67ffa23fea7ccef85097f2325c9a6432276e9a6add62eea81175b99da01af235", "size_in_bytes": 2131}, {"_path": "lib/site-packages/torchvision/models/optical_flow/__pycache__/raft.cpython-310.pyc", "path_type": "hardlink", "sha256": "6f0c0a7f0547f1649f624de4789e7fa6792e69b3352b11e25fdbe8c58acc7350", "sha256_in_prefix": "6f0c0a7f0547f1649f624de4789e7fa6792e69b3352b11e25fdbe8c58acc7350", "size_in_bytes": 28335}, {"_path": "lib/site-packages/torchvision/models/optical_flow/_utils.py", "path_type": "hardlink", "sha256": "3d172e53e201e842f784038b8b20b9abe341ce5bc829f85217f04ca651dbcdf6", "sha256_in_prefix": "3d172e53e201e842f784038b8b20b9abe341ce5bc829f85217f04ca651dbcdf6", "size_in_bytes": 2125}, {"_path": "lib/site-packages/torchvision/models/optical_flow/raft.py", "path_type": "hardlink", "sha256": "a3dc09de3647f445b0265edf41879649d7973cc298399d19c26fa185c7aab959", "sha256_in_prefix": "a3dc09de3647f445b0265edf41879649d7973cc298399d19c26fa185c7aab959", "size_in_bytes": 40942}, {"_path": "lib/site-packages/torchvision/models/quantization/__init__.py", "path_type": "hardlink", "sha256": "60e26662a5904df3f93f6ca9b5e64d29038c5b85440765872656284a549a2f56", "sha256_in_prefix": "60e26662a5904df3f93f6ca9b5e64d29038c5b85440765872656284a549a2f56", "size_in_bytes": 130}, {"_path": "lib/site-packages/torchvision/models/quantization/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "498fb4071212a240f28451711366e3a52ade1481b0c524054d6eb046b3149072", "sha256_in_prefix": "498fb4071212a240f28451711366e3a52ade1481b0c524054d6eb046b3149072", "size_in_bytes": 264}, {"_path": "lib/site-packages/torchvision/models/quantization/__pycache__/googlenet.cpython-310.pyc", "path_type": "hardlink", "sha256": "2020a6f5270df2e9de30a669e11ba60ebd6fcb90363a72bdb9c3368e533691cf", "sha256_in_prefix": "2020a6f5270df2e9de30a669e11ba60ebd6fcb90363a72bdb9c3368e533691cf", "size_in_bytes": 8038}, {"_path": "lib/site-packages/torchvision/models/quantization/__pycache__/inception.cpython-310.pyc", "path_type": "hardlink", "sha256": "01da7cf150b5a398441af54c557f506028ec91348f5892cfb1faacd6bc585b08", "sha256_in_prefix": "01da7cf150b5a398441af54c557f506028ec91348f5892cfb1faacd6bc585b08", "size_in_bytes": 10381}, {"_path": "lib/site-packages/torchvision/models/quantization/__pycache__/mobilenet.cpython-310.pyc", "path_type": "hardlink", "sha256": "ea5614deca617b71009816c5187ccb1b20896a46b05f8d1e405cbbc4673ca6c6", "sha256_in_prefix": "ea5614deca617b71009816c5187ccb1b20896a46b05f8d1e405cbbc4673ca6c6", "size_in_bytes": 278}, {"_path": "lib/site-packages/torchvision/models/quantization/__pycache__/mobilenetv2.cpython-310.pyc", "path_type": "hardlink", "sha256": "4c0df66c4098fcbec1b34a0c8a387b15566a643dc20c5afff6fac3fba709b2eb", "sha256_in_prefix": "4c0df66c4098fcbec1b34a0c8a387b15566a643dc20c5afff6fac3fba709b2eb", "size_in_bytes": 6159}, {"_path": "lib/site-packages/torchvision/models/quantization/__pycache__/mobilenetv3.cpython-310.pyc", "path_type": "hardlink", "sha256": "7d037e7467fd22fcd8e74db424d83f5211a02ccf9bdc229713833d2930001d7b", "sha256_in_prefix": "7d037e7467fd22fcd8e74db424d83f5211a02ccf9bdc229713833d2930001d7b", "size_in_bytes": 8553}, {"_path": "lib/site-packages/torchvision/models/quantization/__pycache__/resnet.cpython-310.pyc", "path_type": "hardlink", "sha256": "0a77ea837384d6a96fc763a426303d830667b27e2587a0f3a40be59028661566", "sha256_in_prefix": "0a77ea837384d6a96fc763a426303d830667b27e2587a0f3a40be59028661566", "size_in_bytes": 15046}, {"_path": "lib/site-packages/torchvision/models/quantization/__pycache__/shufflenetv2.cpython-310.pyc", "path_type": "hardlink", "sha256": "c92997a0df56c536155ddc91b31b2782e7795a1ca2c41131d9940299f14215f6", "sha256_in_prefix": "c92997a0df56c536155ddc91b31b2782e7795a1ca2c41131d9940299f14215f6", "size_in_bytes": 14471}, {"_path": "lib/site-packages/torchvision/models/quantization/__pycache__/utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "83680ca67245b9488cd5b0d69430ded22bea4ce2e23b03807d59830a54d210f8", "sha256_in_prefix": "83680ca67245b9488cd5b0d69430ded22bea4ce2e23b03807d59830a54d210f8", "size_in_bytes": 1701}, {"_path": "lib/site-packages/torchvision/models/quantization/googlenet.py", "path_type": "hardlink", "sha256": "3fdd9c69ca0a553578703a1a36445ebcbc7575a187e4390f7d4c0f0ccb8e5ced", "sha256_in_prefix": "3fdd9c69ca0a553578703a1a36445ebcbc7575a187e4390f7d4c0f0ccb8e5ced", "size_in_bytes": 8290}, {"_path": "lib/site-packages/torchvision/models/quantization/inception.py", "path_type": "hardlink", "sha256": "4d7cf6851a52be1eb360fdfdf0cb174d840c9ea6209d89eae70c86eb1af93619", "sha256_in_prefix": "4d7cf6851a52be1eb360fdfdf0cb174d840c9ea6209d89eae70c86eb1af93619", "size_in_bytes": 11088}, {"_path": "lib/site-packages/torchvision/models/quantization/mobilenet.py", "path_type": "hardlink", "sha256": "6a5ac427092d99755c0a9854f21d84009657d1874a7dccf8b4910e746d815c1f", "sha256_in_prefix": "6a5ac427092d99755c0a9854f21d84009657d1874a7dccf8b4910e746d815c1f", "size_in_bytes": 217}, {"_path": "lib/site-packages/torchvision/models/quantization/mobilenetv2.py", "path_type": "hardlink", "sha256": "836cf71cf434317142b8c5794e92cf45d3bdf66df00bfdddd2e9146a75da247a", "sha256_in_prefix": "836cf71cf434317142b8c5794e92cf45d3bdf66df00bfdddd2e9146a75da247a", "size_in_bytes": 6037}, {"_path": "lib/site-packages/torchvision/models/quantization/mobilenetv3.py", "path_type": "hardlink", "sha256": "97d8350302a2537e572aea1b5e8f943dee4ca91282d6480def18165a29b8aabe", "sha256_in_prefix": "97d8350302a2537e572aea1b5e8f943dee4ca91282d6480def18165a29b8aabe", "size_in_bytes": 9467}, {"_path": "lib/site-packages/torchvision/models/quantization/resnet.py", "path_type": "hardlink", "sha256": "6b3be7d6fc1e6cfdb6aabc9818d8052ccbe21a31e4957397ef17780b6720814a", "sha256_in_prefix": "6b3be7d6fc1e6cfdb6aabc9818d8052ccbe21a31e4957397ef17780b6720814a", "size_in_bytes": 18423}, {"_path": "lib/site-packages/torchvision/models/quantization/shufflenetv2.py", "path_type": "hardlink", "sha256": "ee4f4c2d12f38cfdef91ef9ed008bf72703e8f5e4a190ab9c8372cfc42d7520f", "sha256_in_prefix": "ee4f4c2d12f38cfdef91ef9ed008bf72703e8f5e4a190ab9c8372cfc42d7520f", "size_in_bytes": 17311}, {"_path": "lib/site-packages/torchvision/models/quantization/utils.py", "path_type": "hardlink", "sha256": "223f3c97ab68c8ef0c422d70e75d89b7ec90d90f612bbe7e67648e8c8cd2e99c", "sha256_in_prefix": "223f3c97ab68c8ef0c422d70e75d89b7ec90d90f612bbe7e67648e8c8cd2e99c", "size_in_bytes": 2109}, {"_path": "lib/site-packages/torchvision/models/regnet.py", "path_type": "hardlink", "sha256": "35bb00dd13bb91aee4f5fc18c95679c2fbed254ba15eac97efa68622822e8c61", "sha256_in_prefix": "35bb00dd13bb91aee4f5fc18c95679c2fbed254ba15eac97efa68622822e8c61", "size_in_bytes": 65124}, {"_path": "lib/site-packages/torchvision/models/resnet.py", "path_type": "hardlink", "sha256": "01759697b5e5912429502b2ff2509a59e0ee61aabe2b238b5e605ed11f2bbf9f", "sha256_in_prefix": "01759697b5e5912429502b2ff2509a59e0ee61aabe2b238b5e605ed11f2bbf9f", "size_in_bytes": 39917}, {"_path": "lib/site-packages/torchvision/models/segmentation/__init__.py", "path_type": "hardlink", "sha256": "4cb2f64929aa134f072e2bffcb2216c88cabbdfdb168eb198b49ae0effd8e557", "sha256_in_prefix": "4cb2f64929aa134f072e2bffcb2216c88cabbdfdb168eb198b49ae0effd8e557", "size_in_bytes": 69}, {"_path": "lib/site-packages/torchvision/models/segmentation/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "e163d9473083747b165279549db4bf6edfb7e554ae6e063383f4a8727d865eb5", "sha256_in_prefix": "e163d9473083747b165279549db4bf6edfb7e554ae6e063383f4a8727d865eb5", "size_in_bytes": 213}, {"_path": "lib/site-packages/torchvision/models/segmentation/__pycache__/_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "6658fcec91358db9d5c1c1e12a3719c978d6c664bb4055aab630b2c0b39f7a9e", "sha256_in_prefix": "6658fcec91358db9d5c1c1e12a3719c978d6c664bb4055aab630b2c0b39f7a9e", "size_in_bytes": 1413}, {"_path": "lib/site-packages/torchvision/models/segmentation/__pycache__/deeplabv3.cpython-310.pyc", "path_type": "hardlink", "sha256": "4a05e344f6489d21da14a0aad047577e735373279e29faf8b72688b74de8200e", "sha256_in_prefix": "4a05e344f6489d21da14a0aad047577e735373279e29faf8b72688b74de8200e", "size_in_bytes": 12272}, {"_path": "lib/site-packages/torchvision/models/segmentation/__pycache__/fcn.cpython-310.pyc", "path_type": "hardlink", "sha256": "06d20dc453c3f12e0ffaff28d49ab4f02f6ff7f162254804f65db3ac0fb8313b", "sha256_in_prefix": "06d20dc453c3f12e0ffaff28d49ab4f02f6ff7f162254804f65db3ac0fb8313b", "size_in_bytes": 7624}, {"_path": "lib/site-packages/torchvision/models/segmentation/__pycache__/lraspp.cpython-310.pyc", "path_type": "hardlink", "sha256": "220b1d9980695fce6feb1a975879497d9a1d78da9a58d313c8df6b764803689a", "sha256_in_prefix": "220b1d9980695fce6feb1a975879497d9a1d78da9a58d313c8df6b764803689a", "size_in_bytes": 7270}, {"_path": "lib/site-packages/torchvision/models/segmentation/_utils.py", "path_type": "hardlink", "sha256": "c857b205ae7f3f2bf5510ff637ae1731181362cc627f0cc377e5513febe62063", "sha256_in_prefix": "c857b205ae7f3f2bf5510ff637ae1731181362cc627f0cc377e5513febe62063", "size_in_bytes": 1234}, {"_path": "lib/site-packages/torchvision/models/segmentation/deeplabv3.py", "path_type": "hardlink", "sha256": "324998126d5d17869f4046179857f12c072a8a8893e6e58a60649709c708621e", "sha256_in_prefix": "324998126d5d17869f4046179857f12c072a8a8893e6e58a60649709c708621e", "size_in_bytes": 15405}, {"_path": "lib/site-packages/torchvision/models/segmentation/fcn.py", "path_type": "hardlink", "sha256": "990d568b84bd8f91ba3906cd722bb137055b27a7bd9a24f32168fa9941792700", "sha256_in_prefix": "990d568b84bd8f91ba3906cd722bb137055b27a7bd9a24f32168fa9941792700", "size_in_bytes": 9205}, {"_path": "lib/site-packages/torchvision/models/segmentation/lraspp.py", "path_type": "hardlink", "sha256": "cb1fdbdcf26c1f97b41774ea8860e11276d70864dd357da2231b0abcd7a7334b", "sha256_in_prefix": "cb1fdbdcf26c1f97b41774ea8860e11276d70864dd357da2231b0abcd7a7334b", "size_in_bytes": 7821}, {"_path": "lib/site-packages/torchvision/models/shufflenetv2.py", "path_type": "hardlink", "sha256": "5441ac4cd35376a76ef26ec8eb6cd0b892bfe429044fed18e22c58049f90042b", "sha256_in_prefix": "5441ac4cd35376a76ef26ec8eb6cd0b892bfe429044fed18e22c58049f90042b", "size_in_bytes": 15852}, {"_path": "lib/site-packages/torchvision/models/squeezenet.py", "path_type": "hardlink", "sha256": "0e16be722df9d0a535e43d0b4bd374ee4c3a32536ebac5074819c2f3712bcbf1", "sha256_in_prefix": "0e16be722df9d0a535e43d0b4bd374ee4c3a32536ebac5074819c2f3712bcbf1", "size_in_bytes": 8986}, {"_path": "lib/site-packages/torchvision/models/swin_transformer.py", "path_type": "hardlink", "sha256": "f90f4a775ab3b03eef2f7bb5dfb4383281d24f0cc0e9015cc1e685d330969949", "sha256_in_prefix": "f90f4a775ab3b03eef2f7bb5dfb4383281d24f0cc0e9015cc1e685d330969949", "size_in_bytes": 40370}, {"_path": "lib/site-packages/torchvision/models/vgg.py", "path_type": "hardlink", "sha256": "42df6be6c16863ea0f7703f80deeb58ea4957bd5d464b5cae3bafdc950d0df73", "sha256_in_prefix": "42df6be6c16863ea0f7703f80deeb58ea4957bd5d464b5cae3bafdc950d0df73", "size_in_bytes": 19736}, {"_path": "lib/site-packages/torchvision/models/video/__init__.py", "path_type": "hardlink", "sha256": "c471d1e5cea43f470c0d7724ed79d7ab5f6224bfd47a6731837382d3472a13a0", "sha256_in_prefix": "c471d1e5cea43f470c0d7724ed79d7ab5f6224bfd47a6731837382d3472a13a0", "size_in_bytes": 97}, {"_path": "lib/site-packages/torchvision/models/video/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "b66b69f6f28ccf37134b00bc155dd5445bdfe3ff93aeb5fefcd007be571f4219", "sha256_in_prefix": "b66b69f6f28ccf37134b00bc155dd5445bdfe3ff93aeb5fefcd007be571f4219", "size_in_bytes": 229}, {"_path": "lib/site-packages/torchvision/models/video/__pycache__/mvit.cpython-310.pyc", "path_type": "hardlink", "sha256": "ae1fbffe44641e65d2fd2c1f5b03e9ec1083dc0249c973946f2d4dcf06b3b6cc", "sha256_in_prefix": "ae1fbffe44641e65d2fd2c1f5b03e9ec1083dc0249c973946f2d4dcf06b3b6cc", "size_in_bytes": 22141}, {"_path": "lib/site-packages/torchvision/models/video/__pycache__/resnet.cpython-310.pyc", "path_type": "hardlink", "sha256": "c5d611d7c7b3588a8210221920443920906cc435e5e6113ec074bc8b7d2456fa", "sha256_in_prefix": "c5d611d7c7b3588a8210221920443920906cc435e5e6113ec074bc8b7d2456fa", "size_in_bytes": 14255}, {"_path": "lib/site-packages/torchvision/models/video/__pycache__/s3d.cpython-310.pyc", "path_type": "hardlink", "sha256": "e929b0e91c4b9cc5848cf2d997ddafe5bb3d3e089c27fb67d6ee03e8cd3bdc0a", "sha256_in_prefix": "e929b0e91c4b9cc5848cf2d997ddafe5bb3d3e089c27fb67d6ee03e8cd3bdc0a", "size_in_bytes": 6653}, {"_path": "lib/site-packages/torchvision/models/video/__pycache__/swin_transformer.cpython-310.pyc", "path_type": "hardlink", "sha256": "d0d547a8baa515ca3ec64803ecc98edd398abd38b74d976ed8891845fcf687ae", "sha256_in_prefix": "d0d547a8baa515ca3ec64803ecc98edd398abd38b74d976ed8891845fcf687ae", "size_in_bytes": 20150}, {"_path": "lib/site-packages/torchvision/models/video/mvit.py", "path_type": "hardlink", "sha256": "c482b89c23ae2565d08e85fc373728bb0cf24e4224705ba0df28aed1ae7e0e4f", "sha256_in_prefix": "c482b89c23ae2565d08e85fc373728bb0cf24e4224705ba0df28aed1ae7e0e4f", "size_in_bytes": 33895}, {"_path": "lib/site-packages/torchvision/models/video/resnet.py", "path_type": "hardlink", "sha256": "24e3fb1437d439f4103fe8c4614bf34883abf4869ec65d8bde2521fabceba7dd", "sha256_in_prefix": "24e3fb1437d439f4103fe8c4614bf34883abf4869ec65d8bde2521fabceba7dd", "size_in_bytes": 17274}, {"_path": "lib/site-packages/torchvision/models/video/s3d.py", "path_type": "hardlink", "sha256": "467fa2ca93f5de3ac44c06a9d50778358ea4929603b925e31a410a6433b17a62", "sha256_in_prefix": "467fa2ca93f5de3ac44c06a9d50778358ea4929603b925e31a410a6433b17a62", "size_in_bytes": 8034}, {"_path": "lib/site-packages/torchvision/models/video/swin_transformer.py", "path_type": "hardlink", "sha256": "33be0fdafe2554a33ace0c287859ff9e3a69641da5fa0023b8656fcf31122a95", "sha256_in_prefix": "33be0fdafe2554a33ace0c287859ff9e3a69641da5fa0023b8656fcf31122a95", "size_in_bytes": 28431}, {"_path": "lib/site-packages/torchvision/models/vision_transformer.py", "path_type": "hardlink", "sha256": "184fbff9d9452503d39ce35ee2aaf360e629fb06af3ceac53c2a3d92b337f558", "sha256_in_prefix": "184fbff9d9452503d39ce35ee2aaf360e629fb06af3ceac53c2a3d92b337f558", "size_in_bytes": 33000}, {"_path": "lib/site-packages/torchvision/ops/__init__.py", "path_type": "hardlink", "sha256": "ef089b1b1705d491c3be248db3d3bd3f095cf1d84c4857d9a34c335634c59c06", "sha256_in_prefix": "ef089b1b1705d491c3be248db3d3bd3f095cf1d84c4857d9a34c335634c59c06", "size_in_bytes": 2001}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "35e54369caf2aaf3c91aaab95a42b6344e1b518199397ce11a17e8c8e118ede2", "sha256_in_prefix": "35e54369caf2aaf3c91aaab95a42b6344e1b518199397ce11a17e8c8e118ede2", "size_in_bytes": 1675}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/_box_convert.cpython-310.pyc", "path_type": "hardlink", "sha256": "178298fa101919f31ca9492a6b008d2b0a4c3759e89f8091025652ccaff07838", "sha256_in_prefix": "178298fa101919f31ca9492a6b008d2b0a4c3759e89f8091025652ccaff07838", "size_in_bytes": 2625}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/_register_onnx_ops.cpython-310.pyc", "path_type": "hardlink", "sha256": "df363a443b5842ee4a0e951ca75fe74141777efe63278ffc820c725ad277c6a8", "sha256_in_prefix": "df363a443b5842ee4a0e951ca75fe74141777efe63278ffc820c725ad277c6a8", "size_in_bytes": 3415}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "69aee29b31fd92bffcdcd773dd42871c7125e07fbc9fe1460b0c2aa8460d1724", "sha256_in_prefix": "69aee29b31fd92bffcdcd773dd42871c7125e07fbc9fe1460b0c2aa8460d1724", "size_in_bytes": 3672}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/boxes.cpython-310.pyc", "path_type": "hardlink", "sha256": "3a95649ad83b00b032c717a91ea2b5831e3925e0439ac9713f1bcee176516a18", "sha256_in_prefix": "3a95649ad83b00b032c717a91ea2b5831e3925e0439ac9713f1bcee176516a18", "size_in_bytes": 14285}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/ciou_loss.cpython-310.pyc", "path_type": "hardlink", "sha256": "6091af0c08364666985699272d43b93a155a0d54b45b7a56f7ad9d13c11f9e30", "sha256_in_prefix": "6091af0c08364666985699272d43b93a155a0d54b45b7a56f7ad9d13c11f9e30", "size_in_bytes": 2601}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/deform_conv.cpython-310.pyc", "path_type": "hardlink", "sha256": "e92a889991313ed19f338f12defbe184702f140cdce6da5d27e83ef03be9da44", "sha256_in_prefix": "e92a889991313ed19f338f12defbe184702f140cdce6da5d27e83ef03be9da44", "size_in_bytes": 6585}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/diou_loss.cpython-310.pyc", "path_type": "hardlink", "sha256": "c46cc61e5735e2c095967f783b6634777d366684ec9b79bf3b2ca99075d7c338", "sha256_in_prefix": "c46cc61e5735e2c095967f783b6634777d366684ec9b79bf3b2ca99075d7c338", "size_in_bytes": 2867}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/drop_block.cpython-310.pyc", "path_type": "hardlink", "sha256": "e54ee786df20d13b228dac55969c7542b53d5ec38ee45c50012517192e769eea", "sha256_in_prefix": "e54ee786df20d13b228dac55969c7542b53d5ec38ee45c50012517192e769eea", "size_in_bytes": 5650}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/feature_pyramid_network.cpython-310.pyc", "path_type": "hardlink", "sha256": "6386c61429d230212c49861b16258061c7920fa52b7a8ab3cd93a06fc57ccd0c", "sha256_in_prefix": "6386c61429d230212c49861b16258061c7920fa52b7a8ab3cd93a06fc57ccd0c", "size_in_bytes": 8225}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/focal_loss.cpython-310.pyc", "path_type": "hardlink", "sha256": "365bcbb82c10a1aa72392915c2d2a07b0bea95de4a164ece1dec1d2ea1dc2c59", "sha256_in_prefix": "365bcbb82c10a1aa72392915c2d2a07b0bea95de4a164ece1dec1d2ea1dc2c59", "size_in_bytes": 2144}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/giou_loss.cpython-310.pyc", "path_type": "hardlink", "sha256": "ec93d3547b9f683087c315fe230ca96071c0ab012404360f0560273a3d3e6124", "sha256_in_prefix": "ec93d3547b9f683087c315fe230ca96071c0ab012404360f0560273a3d3e6124", "size_in_bytes": 2490}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/misc.cpython-310.pyc", "path_type": "hardlink", "sha256": "d21739e2298ed1de8c3cde28ed3e059250b94e4afede6ea2fd7b521e606a0e4c", "sha256_in_prefix": "d21739e2298ed1de8c3cde28ed3e059250b94e4afede6ea2fd7b521e606a0e4c", "size_in_bytes": 12913}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/poolers.cpython-310.pyc", "path_type": "hardlink", "sha256": "9eae458e2b787755fd6e928781bd1ebe27ffc2ec586bfe82fedc34f1f2ead83b", "sha256_in_prefix": "9eae458e2b787755fd6e928781bd1ebe27ffc2ec586bfe82fedc34f1f2ead83b", "size_in_bytes": 10212}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/ps_roi_align.cpython-310.pyc", "path_type": "hardlink", "sha256": "8a202f0e59fb6389a7646b1eb36e4e86dfb4c7d5a47c5e77cd8f1b240370c0b0", "sha256_in_prefix": "8a202f0e59fb6389a7646b1eb36e4e86dfb4c7d5a47c5e77cd8f1b240370c0b0", "size_in_bytes": 3911}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/ps_roi_pool.cpython-310.pyc", "path_type": "hardlink", "sha256": "281aa01c06b54cbd3b9224376a9efde569dabc21458e3853512fcd30933dd445", "sha256_in_prefix": "281aa01c06b54cbd3b9224376a9efde569dabc21458e3853512fcd30933dd445", "size_in_bytes": 3324}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/roi_align.cpython-310.pyc", "path_type": "hardlink", "sha256": "1a66fb61bda62a720b4fd87018537d5742b11616b5e885007b9e591b4fe8a2dd", "sha256_in_prefix": "1a66fb61bda62a720b4fd87018537d5742b11616b5e885007b9e591b4fe8a2dd", "size_in_bytes": 7836}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/roi_pool.cpython-310.pyc", "path_type": "hardlink", "sha256": "63aa351f8a73d11eb1c2f1f4cdee664c58134b9f99abe1626bd0e42ec0b635c2", "sha256_in_prefix": "63aa351f8a73d11eb1c2f1f4cdee664c58134b9f99abe1626bd0e42ec0b635c2", "size_in_bytes": 3394}, {"_path": "lib/site-packages/torchvision/ops/__pycache__/stochastic_depth.cpython-310.pyc", "path_type": "hardlink", "sha256": "0af146112936b169e1d6b57f030f8827d03804fe7eb2cbaf3c34cc82e30cd683", "sha256_in_prefix": "0af146112936b169e1d6b57f030f8827d03804fe7eb2cbaf3c34cc82e30cd683", "size_in_bytes": 2692}, {"_path": "lib/site-packages/torchvision/ops/_box_convert.py", "path_type": "hardlink", "sha256": "82517ab2e94bcdac3f286dfac20d021d6b7479feb6067923a246ea42705432c5", "sha256_in_prefix": "82517ab2e94bcdac3f286dfac20d021d6b7479feb6067923a246ea42705432c5", "size_in_bytes": 2490}, {"_path": "lib/site-packages/torchvision/ops/_register_onnx_ops.py", "path_type": "hardlink", "sha256": "838339169ee7ff9653cc841c517bc472ddd816550c3cd5524107c13fe2747904", "sha256_in_prefix": "838339169ee7ff9653cc841c517bc472ddd816550c3cd5524107c13fe2747904", "size_in_bytes": 4288}, {"_path": "lib/site-packages/torchvision/ops/_utils.py", "path_type": "hardlink", "sha256": "c45acb9cb84a0c7886d93377f6d51663e3096cb10f91ae9d91a55524500defef", "sha256_in_prefix": "c45acb9cb84a0c7886d93377f6d51663e3096cb10f91ae9d91a55524500defef", "size_in_bytes": 3736}, {"_path": "lib/site-packages/torchvision/ops/boxes.py", "path_type": "hardlink", "sha256": "2efad6e4f8f82b94d1140a63ef4cd4c7dc6e299d12262c60bc070f664d1bc140", "sha256_in_prefix": "2efad6e4f8f82b94d1140a63ef4cd4c7dc6e299d12262c60bc070f664d1bc140", "size_in_bytes": 16796}, {"_path": "lib/site-packages/torchvision/ops/ciou_loss.py", "path_type": "hardlink", "sha256": "4339bcf42f367a15fe62f6013cb3d13e16c965daf78ad8b3c6ec6b4fb30b8bda", "sha256_in_prefix": "4339bcf42f367a15fe62f6013cb3d13e16c965daf78ad8b3c6ec6b4fb30b8bda", "size_in_bytes": 2834}, {"_path": "lib/site-packages/torchvision/ops/deform_conv.py", "path_type": "hardlink", "sha256": "0ee228b050cadedb18e5194753b99ecf9c75a74f0841a8bd586d78cf74b48335", "sha256_in_prefix": "0ee228b050cadedb18e5194753b99ecf9c75a74f0841a8bd586d78cf74b48335", "size_in_bytes": 7185}, {"_path": "lib/site-packages/torchvision/ops/diou_loss.py", "path_type": "hardlink", "sha256": "e8879b5a531873fd989db1b7ea78835e0335eafc5a8d22917e2bac12e509c294", "sha256_in_prefix": "e8879b5a531873fd989db1b7ea78835e0335eafc5a8d22917e2bac12e509c294", "size_in_bytes": 3456}, {"_path": "lib/site-packages/torchvision/ops/drop_block.py", "path_type": "hardlink", "sha256": "6642333356f7bf9fd4ef3d1e6afcda36937b201ab437864dc30bd602b8aca708", "sha256_in_prefix": "6642333356f7bf9fd4ef3d1e6afcda36937b201ab437864dc30bd602b8aca708", "size_in_bytes": 6010}, {"_path": "lib/site-packages/torchvision/ops/feature_pyramid_network.py", "path_type": "hardlink", "sha256": "26db399b35095f711aadc01053930351ed1ae52827e588d4b2d696d894298041", "sha256_in_prefix": "26db399b35095f711aadc01053930351ed1ae52827e588d4b2d696d894298041", "size_in_bytes": 8952}, {"_path": "lib/site-packages/torchvision/ops/focal_loss.py", "path_type": "hardlink", "sha256": "952e45aa02c5b832a5a66d2c939575560200e8b14074951741a3e2df99c40e85", "sha256_in_prefix": "952e45aa02c5b832a5a66d2c939575560200e8b14074951741a3e2df99c40e85", "size_in_bytes": 2319}, {"_path": "lib/site-packages/torchvision/ops/giou_loss.py", "path_type": "hardlink", "sha256": "c41fd19444b6f24fc0ea21f656a5803c1424893ff3904c1db51103191fe75493", "sha256_in_prefix": "c41fd19444b6f24fc0ea21f656a5803c1424893ff3904c1db51103191fe75493", "size_in_bytes": 2772}, {"_path": "lib/site-packages/torchvision/ops/misc.py", "path_type": "hardlink", "sha256": "9e242728fba27d0cd55c05a79dfe93915b207adab27ad8d9ea5ab7c22dadb19c", "sha256_in_prefix": "9e242728fba27d0cd55c05a79dfe93915b207adab27ad8d9ea5ab7c22dadb19c", "size_in_bytes": 13892}, {"_path": "lib/site-packages/torchvision/ops/poolers.py", "path_type": "hardlink", "sha256": "b1f81c656876748a3d518e37ec29e9007771a8f421baf8cd3d8ce120a94020f1", "sha256_in_prefix": "b1f81c656876748a3d518e37ec29e9007771a8f421baf8cd3d8ce120a94020f1", "size_in_bytes": 12247}, {"_path": "lib/site-packages/torchvision/ops/ps_roi_align.py", "path_type": "hardlink", "sha256": "ebf9269c4eb3ff701967537686b4bfb8addc6eecea6618dd336ac2e7499f614a", "sha256_in_prefix": "ebf9269c4eb3ff701967537686b4bfb8addc6eecea6618dd336ac2e7499f614a", "size_in_bytes": 3715}, {"_path": "lib/site-packages/torchvision/ops/ps_roi_pool.py", "path_type": "hardlink", "sha256": "d89ae3270cd5b4478483405e6e40a719fab8c440f03020fe5e1f75e66bcd7322", "sha256_in_prefix": "d89ae3270cd5b4478483405e6e40a719fab8c440f03020fe5e1f75e66bcd7322", "size_in_bytes": 2940}, {"_path": "lib/site-packages/torchvision/ops/roi_align.py", "path_type": "hardlink", "sha256": "c42c20382a867e7d5662727d92b23f721f7f8b7df6830f184ee6f54303b96fce", "sha256_in_prefix": "c42c20382a867e7d5662727d92b23f721f7f8b7df6830f184ee6f54303b96fce", "size_in_bytes": 11030}, {"_path": "lib/site-packages/torchvision/ops/roi_pool.py", "path_type": "hardlink", "sha256": "70deeb48241fa54cc44ef3fc4a33c3975c9f5e36f183d23eb579c76ef00ac9af", "sha256_in_prefix": "70deeb48241fa54cc44ef3fc4a33c3975c9f5e36f183d23eb579c76ef00ac9af", "size_in_bytes": 3015}, {"_path": "lib/site-packages/torchvision/ops/stochastic_depth.py", "path_type": "hardlink", "sha256": "f53e19bbf05a7a629969f4a6470acf095af969a187f2d9b396c40064efb5ffe6", "sha256_in_prefix": "f53e19bbf05a7a629969f4a6470acf095af969a187f2d9b396c40064efb5ffe6", "size_in_bytes": 2302}, {"_path": "lib/site-packages/torchvision/transforms/__init__.py", "path_type": "hardlink", "sha256": "5823574c951b27587b61a37d51fac14afb7ef924f63c0578b0b2026d34be2f90", "sha256_in_prefix": "5823574c951b27587b61a37d51fac14afb7ef924f63c0578b0b2026d34be2f90", "size_in_bytes": 55}, {"_path": "lib/site-packages/torchvision/transforms/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "d051500a92c7c1934685feef177f30a743d51783b6e84c3d615085af1048ea65", "sha256_in_prefix": "d051500a92c7c1934685feef177f30a743d51783b6e84c3d615085af1048ea65", "size_in_bytes": 195}, {"_path": "lib/site-packages/torchvision/transforms/__pycache__/_functional_pil.cpython-310.pyc", "path_type": "hardlink", "sha256": "f72734683b7077ce1e7105f3fd75dc7dfd56208c7225d9fc3da4f4406e839d06", "sha256_in_prefix": "f72734683b7077ce1e7105f3fd75dc7dfd56208c7225d9fc3da4f4406e839d06", "size_in_bytes": 9675}, {"_path": "lib/site-packages/torchvision/transforms/__pycache__/_functional_tensor.cpython-310.pyc", "path_type": "hardlink", "sha256": "d470365181715e8a0dfcaeae28d79588a9cf9824fc7dac654db598119184495e", "sha256_in_prefix": "d470365181715e8a0dfcaeae28d79588a9cf9824fc7dac654db598119184495e", "size_in_bytes": 24730}, {"_path": "lib/site-packages/torchvision/transforms/__pycache__/_functional_video.cpython-310.pyc", "path_type": "hardlink", "sha256": "2338698bd555acbf7af0eccbe0796fb0969afa3950feb34eb6f09b9c86c622c8", "sha256_in_prefix": "2338698bd555acbf7af0eccbe0796fb0969afa3950feb34eb6f09b9c86c622c8", "size_in_bytes": 4023}, {"_path": "lib/site-packages/torchvision/transforms/__pycache__/_presets.cpython-310.pyc", "path_type": "hardlink", "sha256": "394ff65904a8a2005aedde7ed3e1755cf68572bf6c08c187d13bfbc61969aa75", "sha256_in_prefix": "394ff65904a8a2005aedde7ed3e1755cf68572bf6c08c187d13bfbc61969aa75", "size_in_bytes": 7635}, {"_path": "lib/site-packages/torchvision/transforms/__pycache__/_transforms_video.cpython-310.pyc", "path_type": "hardlink", "sha256": "9b0237949e4331d24f287758b767208196ce41d2957c361aa92da5179be21da7", "sha256_in_prefix": "9b0237949e4331d24f287758b767208196ce41d2957c361aa92da5179be21da7", "size_in_bytes": 6411}, {"_path": "lib/site-packages/torchvision/transforms/__pycache__/autoaugment.cpython-310.pyc", "path_type": "hardlink", "sha256": "b0ff93d28554ce8246a89de291d2ec84231b89df9af767e348398c4c0350cfc2", "sha256_in_prefix": "b0ff93d28554ce8246a89de291d2ec84231b89df9af767e348398c4c0350cfc2", "size_in_bytes": 20084}, {"_path": "lib/site-packages/torchvision/transforms/__pycache__/functional.cpython-310.pyc", "path_type": "hardlink", "sha256": "6fde8ef9654b5e4bb02d432aebe723dadb921aa3c9bbe95ca38797f1a0c7b56c", "sha256_in_prefix": "6fde8ef9654b5e4bb02d432aebe723dadb921aa3c9bbe95ca38797f1a0c7b56c", "size_in_bytes": 55517}, {"_path": "lib/site-packages/torchvision/transforms/__pycache__/transforms.cpython-310.pyc", "path_type": "hardlink", "sha256": "7b6036fb0debd4ab8413a8f073760eeda8faff2bd9ae767a8162563353f829a9", "sha256_in_prefix": "7b6036fb0debd4ab8413a8f073760eeda8faff2bd9ae767a8162563353f829a9", "size_in_bytes": 82850}, {"_path": "lib/site-packages/torchvision/transforms/_functional_pil.py", "path_type": "hardlink", "sha256": "50489a12560bb985e4351fff3bf75b2adb3604a06c6f6f118f9d11305811c628", "sha256_in_prefix": "50489a12560bb985e4351fff3bf75b2adb3604a06c6f6f118f9d11305811c628", "size_in_bytes": 12505}, {"_path": "lib/site-packages/torchvision/transforms/_functional_tensor.py", "path_type": "hardlink", "sha256": "8692f2f710b038db9bc6819fcd78aa36cd27121815683291b8c72a0314a2d3dd", "sha256_in_prefix": "8692f2f710b038db9bc6819fcd78aa36cd27121815683291b8c72a0314a2d3dd", "size_in_bytes": 34794}, {"_path": "lib/site-packages/torchvision/transforms/_functional_video.py", "path_type": "hardlink", "sha256": "73805b522dd8d8bbec928cc5772eb5f6988b05de5a72c8f1828818017998e4ff", "sha256_in_prefix": "73805b522dd8d8bbec928cc5772eb5f6988b05de5a72c8f1828818017998e4ff", "size_in_bytes": 3971}, {"_path": "lib/site-packages/torchvision/transforms/_presets.py", "path_type": "hardlink", "sha256": "515c5c84d81d3cbfb88951e33b17079ddca092dc382bbea187110af24b35ce5c", "sha256_in_prefix": "515c5c84d81d3cbfb88951e33b17079ddca092dc382bbea187110af24b35ce5c", "size_in_bytes": 8700}, {"_path": "lib/site-packages/torchvision/transforms/_transforms_video.py", "path_type": "hardlink", "sha256": "b9bda0093e442e22bdd7c06af8fa7a9b3856ac0671963edb95fa6403c0e16f5a", "sha256_in_prefix": "b9bda0093e442e22bdd7c06af8fa7a9b3856ac0671963edb95fa6403c0e16f5a", "size_in_bytes": 5124}, {"_path": "lib/site-packages/torchvision/transforms/autoaugment.py", "path_type": "hardlink", "sha256": "503f140654f875608841a343503401b5cd28b0c1c73d096e2ebeec79926be1c6", "sha256_in_prefix": "503f140654f875608841a343503401b5cd28b0c1c73d096e2ebeec79926be1c6", "size_in_bytes": 28858}, {"_path": "lib/site-packages/torchvision/transforms/functional.py", "path_type": "hardlink", "sha256": "362a0282408071e3b502ae3f9e0607137c94559e9e0ec87ae83faf78cea01d4d", "sha256_in_prefix": "362a0282408071e3b502ae3f9e0607137c94559e9e0ec87ae83faf78cea01d4d", "size_in_bytes": 68953}, {"_path": "lib/site-packages/torchvision/transforms/transforms.py", "path_type": "hardlink", "sha256": "35e169a3ceb159fda1f2f333fef5e96ea57d3f346844e129e8694dc70f684d94", "sha256_in_prefix": "35e169a3ceb159fda1f2f333fef5e96ea57d3f346844e129e8694dc70f684d94", "size_in_bytes": 87710}, {"_path": "lib/site-packages/torchvision/transforms/v2/__init__.py", "path_type": "hardlink", "sha256": "01f6e41e2eb2428121adb7cdca61f6cfcb19a6ace711cc196443d640d14374e3", "sha256_in_prefix": "01f6e41e2eb2428121adb7cdca61f6cfcb19a6ace711cc196443d640d14374e3", "size_in_bytes": 1492}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "aa29b12c87f9bc267198d9801421098ee206e5ff30a9c2197d3c8ceec6fa5301", "sha256_in_prefix": "aa29b12c87f9bc267198d9801421098ee206e5ff30a9c2197d3c8ceec6fa5301", "size_in_bytes": 1928}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_augment.cpython-310.pyc", "path_type": "hardlink", "sha256": "6e731fe369ceb8bf2547689d93c80874e3b2022c2c0e1aab91ad7ff878ba9430", "sha256_in_prefix": "6e731fe369ceb8bf2547689d93c80874e3b2022c2c0e1aab91ad7ff878ba9430", "size_in_bytes": 14887}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_auto_augment.cpython-310.pyc", "path_type": "hardlink", "sha256": "a32cdfe43e5c95c565ec957a2e47c450dccdb0ae6150734b5ea90296c671b21e", "sha256_in_prefix": "a32cdfe43e5c95c565ec957a2e47c450dccdb0ae6150734b5ea90296c671b21e", "size_in_bytes": 24056}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_color.cpython-310.pyc", "path_type": "hardlink", "sha256": "b83151c69dfe6decb540972e15c82d8aca73fc7f319c31dfe2a0021de1771a15", "sha256_in_prefix": "b83151c69dfe6decb540972e15c82d8aca73fc7f319c31dfe2a0021de1771a15", "size_in_bytes": 18443}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_container.cpython-310.pyc", "path_type": "hardlink", "sha256": "cbd55585bddfb24f50bdf299046b402aa23ec423f94837773a8798288b2342d7", "sha256_in_prefix": "cbd55585bddfb24f50bdf299046b402aa23ec423f94837773a8798288b2342d7", "size_in_bytes": 6791}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_deprecated.cpython-310.pyc", "path_type": "hardlink", "sha256": "ad936ab4b7c625cbf2d27bb6a9c1175121ebf1b83ca2d6a93ed4094a834558fe", "sha256_in_prefix": "ad936ab4b7c625cbf2d27bb6a9c1175121ebf1b83ca2d6a93ed4094a834558fe", "size_in_bytes": 2480}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_geometry.cpython-310.pyc", "path_type": "hardlink", "sha256": "c30d69ecea23f302b8b0850ab63d9e8893bc84cbcd4c47fe338182eb54a17af6", "sha256_in_prefix": "c30d69ecea23f302b8b0850ab63d9e8893bc84cbcd4c47fe338182eb54a17af6", "size_in_bytes": 61890}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_meta.cpython-310.pyc", "path_type": "hardlink", "sha256": "395cfd3032bd9993c199602f42fd0c9b74388de63f61090a88a56c83886e8c6e", "sha256_in_prefix": "395cfd3032bd9993c199602f42fd0c9b74388de63f61090a88a56c83886e8c6e", "size_in_bytes": 2012}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_misc.cpython-310.pyc", "path_type": "hardlink", "sha256": "507024a0e04b54c4e38f555738023a34d6d5c64b1128fa76a8f5622c743eaa61", "sha256_in_prefix": "507024a0e04b54c4e38f555738023a34d6d5c64b1128fa76a8f5622c743eaa61", "size_in_bytes": 17790}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_temporal.cpython-310.pyc", "path_type": "hardlink", "sha256": "65a4859d0633815f96242824d5b8b47f19e21fa8b37480fcf4653681a4f50ed6", "sha256_in_prefix": "65a4859d0633815f96242824d5b8b47f19e21fa8b37480fcf4653681a4f50ed6", "size_in_bytes": 1469}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_transform.cpython-310.pyc", "path_type": "hardlink", "sha256": "d36a47e7a000088ae120e4842cb1e88f19004eb48fd8d312b60d63baa0fe9fa5", "sha256_in_prefix": "d36a47e7a000088ae120e4842cb1e88f19004eb48fd8d312b60d63baa0fe9fa5", "size_in_bytes": 5973}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_type_conversion.cpython-310.pyc", "path_type": "hardlink", "sha256": "a4e941dcbae2fa12da020273b1220375410cc71d28bd0afb046ba6830c7eef00", "sha256_in_prefix": "a4e941dcbae2fa12da020273b1220375410cc71d28bd0afb046ba6830c7eef00", "size_in_bytes": 3936}, {"_path": "lib/site-packages/torchvision/transforms/v2/__pycache__/_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "3e6b07a8cf29bf7463699d29d90ce0b4949d94074b77ed653cb3be8779c26a27", "sha256_in_prefix": "3e6b07a8cf29bf7463699d29d90ce0b4949d94074b77ed653cb3be8779c26a27", "size_in_bytes": 8811}, {"_path": "lib/site-packages/torchvision/transforms/v2/_augment.py", "path_type": "hardlink", "sha256": "2d6ae0170f705d736c617a03db1c63581fbf880263317aed64306fd8cd5a9874", "sha256_in_prefix": "2d6ae0170f705d736c617a03db1c63581fbf880263317aed64306fd8cd5a9874", "size_in_bytes": 15625}, {"_path": "lib/site-packages/torchvision/transforms/v2/_auto_augment.py", "path_type": "hardlink", "sha256": "fa93359e10a169853ad4a2b806783c511a389b4a55603be95ac0c016243b3148", "sha256_in_prefix": "fa93359e10a169853ad4a2b806783c511a389b4a55603be95ac0c016243b3148", "size_in_bytes": 32407}, {"_path": "lib/site-packages/torchvision/transforms/v2/_color.py", "path_type": "hardlink", "sha256": "a45b1582dde8e7dcbca77fc6c1e54d81e951db97169ef5c0b534b222fad902c1", "sha256_in_prefix": "a45b1582dde8e7dcbca77fc6c1e54d81e951db97169ef5c0b534b222fad902c1", "size_in_bytes": 17366}, {"_path": "lib/site-packages/torchvision/transforms/v2/_container.py", "path_type": "hardlink", "sha256": "13ef13bd317fa81a82e9a2e794ae669e9dd5dbba103b1872d57ecf65f4000f9c", "sha256_in_prefix": "13ef13bd317fa81a82e9a2e794ae669e9dd5dbba103b1872d57ecf65f4000f9c", "size_in_bytes": 6229}, {"_path": "lib/site-packages/torchvision/transforms/v2/_deprecated.py", "path_type": "hardlink", "sha256": "f684a4ef01db6086a3022ce0dfba0e1857160b91880b012e48718039319f1a41", "sha256_in_prefix": "f684a4ef01db6086a3022ce0dfba0e1857160b91880b012e48718039319f1a41", "size_in_bytes": 1997}, {"_path": "lib/site-packages/torchvision/transforms/v2/_geometry.py", "path_type": "hardlink", "sha256": "2599d64c94a2670cd3788f441fce2ed7ae36c695709d79899374198dbe5abd6d", "sha256_in_prefix": "2599d64c94a2670cd3788f441fce2ed7ae36c695709d79899374198dbe5abd6d", "size_in_bytes": 68331}, {"_path": "lib/site-packages/torchvision/transforms/v2/_meta.py", "path_type": "hardlink", "sha256": "23fe533ffc86a3fbc7a71ef128360c22b973ba2bac0a3717457eb347c5b31145", "sha256_in_prefix": "23fe533ffc86a3fbc7a71ef128360c22b973ba2bac0a3717457eb347c5b31145", "size_in_bytes": 1441}, {"_path": "lib/site-packages/torchvision/transforms/v2/_misc.py", "path_type": "hardlink", "sha256": "60308eeddf7a81da9d3b8b77317df0dd91b900c98c4b536b2f9ac5996cb59fc8", "sha256_in_prefix": "60308eeddf7a81da9d3b8b77317df0dd91b900c98c4b536b2f9ac5996cb59fc8", "size_in_bytes": 18076}, {"_path": "lib/site-packages/torchvision/transforms/v2/_temporal.py", "path_type": "hardlink", "sha256": "16616dc2796ecd1cc9397e90f9cda1c5e3d05b4487029ebb72d2ec64b5adf053", "sha256_in_prefix": "16616dc2796ecd1cc9397e90f9cda1c5e3d05b4487029ebb72d2ec64b5adf053", "size_in_bytes": 932}, {"_path": "lib/site-packages/torchvision/transforms/v2/_transform.py", "path_type": "hardlink", "sha256": "8b72967d77058e2a0bdc55798b1d577ad2b570b1f2c3786eb485b077fc0d9fdc", "sha256_in_prefix": "8b72967d77058e2a0bdc55798b1d577ad2b570b1f2c3786eb485b077fc0d9fdc", "size_in_bytes": 8652}, {"_path": "lib/site-packages/torchvision/transforms/v2/_type_conversion.py", "path_type": "hardlink", "sha256": "cd6c186522e6fbb324ff44eb4a82431d7f4a7a5f9fcb2dfac0695861f684beec", "sha256_in_prefix": "cd6c186522e6fbb324ff44eb4a82431d7f4a7a5f9fcb2dfac0695861f684beec", "size_in_bytes": 2944}, {"_path": "lib/site-packages/torchvision/transforms/v2/_utils.py", "path_type": "hardlink", "sha256": "0a2b27b43cbb8f41aea2b82650057cd284bb0350fccf1cc50f700616ba72b424", "sha256_in_prefix": "0a2b27b43cbb8f41aea2b82650057cd284bb0350fccf1cc50f700616ba72b424", "size_in_bytes": 8872}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__init__.py", "path_type": "hardlink", "sha256": "055e8ba9a8b48cdccc8efe530de48a7afd80829c0eefd42e3cad66dfa99a8d1a", "sha256_in_prefix": "055e8ba9a8b48cdccc8efe530de48a7afd80829c0eefd42e3cad66dfa99a8d1a", "size_in_bytes": 3624}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "bfcb5802bf07db27cabe5309cc704e35ccbe35822877f3b661dda22e20ec33d6", "sha256_in_prefix": "bfcb5802bf07db27cabe5309cc704e35ccbe35822877f3b661dda22e20ec33d6", "size_in_bytes": 4293}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_augment.cpython-310.pyc", "path_type": "hardlink", "sha256": "1dd0269bbb32532e9ae4b1c76fe194e8b06744b0fed5334090dfa1765ac0928b", "sha256_in_prefix": "1dd0269bbb32532e9ae4b1c76fe194e8b06744b0fed5334090dfa1765ac0928b", "size_in_bytes": 3232}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_color.cpython-310.pyc", "path_type": "hardlink", "sha256": "0d2ee061e845bcd17a09c204d24677f298fc73121fc8adff2ec60a972a5dd819", "sha256_in_prefix": "0d2ee061e845bcd17a09c204d24677f298fc73121fc8adff2ec60a972a5dd819", "size_in_bytes": 17742}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_deprecated.cpython-310.pyc", "path_type": "hardlink", "sha256": "a004e46f5ae6911f9278e118f7a459ad8f3a8526542c1aa1beffb061e571faba", "sha256_in_prefix": "a004e46f5ae6911f9278e118f7a459ad8f3a8526542c1aa1beffb061e571faba", "size_in_bytes": 1068}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_geometry.cpython-310.pyc", "path_type": "hardlink", "sha256": "50f5d4e8c50dc9b94463c23b382677dc35f21b8b2745d68dad6c571d2a4e1ea3", "sha256_in_prefix": "50f5d4e8c50dc9b94463c23b382677dc35f21b8b2745d68dad6c571d2a4e1ea3", "size_in_bytes": 50356}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_meta.cpython-310.pyc", "path_type": "hardlink", "sha256": "86f946d2aafdf5e45002f77a4c1822060fab8f3928e6ad7d6197cb7fb013d9cc", "sha256_in_prefix": "86f946d2aafdf5e45002f77a4c1822060fab8f3928e6ad7d6197cb7fb013d9cc", "size_in_bytes": 7261}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_misc.cpython-310.pyc", "path_type": "hardlink", "sha256": "64ec72211f7843ff3d21b3f5567186b1978d643fb143184ab3669af101c9689d", "sha256_in_prefix": "64ec72211f7843ff3d21b3f5567186b1978d643fb143184ab3669af101c9689d", "size_in_bytes": 11052}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_temporal.cpython-310.pyc", "path_type": "hardlink", "sha256": "9f953d1b50cda3a75b1c8f71dc59e51c0fb003be8838382dcb2363e731f56b14", "sha256_in_prefix": "9f953d1b50cda3a75b1c8f71dc59e51c0fb003be8838382dcb2363e731f56b14", "size_in_bytes": 1084}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_type_conversion.cpython-310.pyc", "path_type": "hardlink", "sha256": "8f0509b45227406810a40a7cc1e668dd894301089f78212ca77b3fec0f36717a", "sha256_in_prefix": "8f0509b45227406810a40a7cc1e668dd894301089f78212ca77b3fec0f36717a", "size_in_bytes": 1025}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/__pycache__/_utils.cpython-310.pyc", "path_type": "hardlink", "sha256": "0fc71c4f15fcb92df209fe4887d382a5f398b336182f9ac530ba781a401eff7c", "sha256_in_prefix": "0fc71c4f15fcb92df209fe4887d382a5f398b336182f9ac530ba781a401eff7c", "size_in_bytes": 4885}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/_augment.py", "path_type": "hardlink", "sha256": "af09620a7dd9f42330a58ef1734b21fb9b43fb0f5853d6853e89377bb0c0739e", "sha256_in_prefix": "af09620a7dd9f42330a58ef1734b21fb9b43fb0f5853d6853e89377bb0c0739e", "size_in_bytes": 3295}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/_color.py", "path_type": "hardlink", "sha256": "47c7668a750c6e1ba07a9c6bd274f329112f7ec8ad593a94ebc7c6116f148bd7", "sha256_in_prefix": "47c7668a750c6e1ba07a9c6bd274f329112f7ec8ad593a94ebc7c6116f148bd7", "size_in_bytes": 30990}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/_deprecated.py", "path_type": "hardlink", "sha256": "f97eda8135e9f899db169b29df1a15935799afb3b24fe7af06dd279542c0478d", "sha256_in_prefix": "f97eda8135e9f899db169b29df1a15935799afb3b24fe7af06dd279542c0478d", "size_in_bytes": 825}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/_geometry.py", "path_type": "hardlink", "sha256": "ad4371982a0b3645b81fe836b479095477981ab82a91e6399bbf57782684348f", "sha256_in_prefix": "ad4371982a0b3645b81fe836b479095477981ab82a91e6399bbf57782684348f", "size_in_bytes": 89743}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/_meta.py", "path_type": "hardlink", "sha256": "6ff305e1242b999355a5c37c5fef1e74ff82126436e3530a0d6a504c6e2999b2", "sha256_in_prefix": "6ff305e1242b999355a5c37c5fef1e74ff82126436e3530a0d6a504c6e2999b2", "size_in_bytes": 10826}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/_misc.py", "path_type": "hardlink", "sha256": "06030debbafb251a8d5831f52908a2a034c269630c31bf2bf39e1e2d1ae31c2f", "sha256_in_prefix": "06030debbafb251a8d5831f52908a2a034c269630c31bf2bf39e1e2d1ae31c2f", "size_in_bytes": 15657}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/_temporal.py", "path_type": "hardlink", "sha256": "b5246492a3aa510d105e310d17cd85d7a125d7e274203a052881feb2cfdca42e", "sha256_in_prefix": "b5246492a3aa510d105e310d17cd85d7a125d7e274203a052881feb2cfdca42e", "size_in_bytes": 1163}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/_type_conversion.py", "path_type": "hardlink", "sha256": "a187f82cc8220a5bc4670c1cdd66ca23b7c9fab445843ad54812a23c0e6fc62a", "sha256_in_prefix": "a187f82cc8220a5bc4670c1cdd66ca23b7c9fab445843ad54812a23c0e6fc62a", "size_in_bytes": 896}, {"_path": "lib/site-packages/torchvision/transforms/v2/functional/_utils.py", "path_type": "hardlink", "sha256": "dd3e62160abcc211d06e4eddba2cd89a8c6ca47ea6b643fe2fb92eeb6ef37c16", "sha256_in_prefix": "dd3e62160abcc211d06e4eddba2cd89a8c6ca47ea6b643fe2fb92eeb6ef37c16", "size_in_bytes": 5620}, {"_path": "lib/site-packages/torchvision/tv_tensors/__init__.py", "path_type": "hardlink", "sha256": "ed404865bada5728483be64278ab43e7d89ff395ca9004389e38d2e918c47c38", "sha256_in_prefix": "ed404865bada5728483be64278ab43e7d89ff395ca9004389e38d2e918c47c38", "size_in_bytes": 1544}, {"_path": "lib/site-packages/torchvision/tv_tensors/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "717399c067c688252864ed18d1836ee8d1c8134f9089812ba9658b090c33b49a", "sha256_in_prefix": "717399c067c688252864ed18d1836ee8d1c8134f9089812ba9658b090c33b49a", "size_in_bytes": 1445}, {"_path": "lib/site-packages/torchvision/tv_tensors/__pycache__/_bounding_boxes.cpython-310.pyc", "path_type": "hardlink", "sha256": "47d44cd7cb06d16dfa9c210ac468d4f87369a178bc0a76b2d1de276ad30e4da6", "sha256_in_prefix": "47d44cd7cb06d16dfa9c210ac468d4f87369a178bc0a76b2d1de276ad30e4da6", "size_in_bytes": 4408}, {"_path": "lib/site-packages/torchvision/tv_tensors/__pycache__/_dataset_wrapper.cpython-310.pyc", "path_type": "hardlink", "sha256": "cc11118fbe2961ef1da1d67742994faac653be46917f697c4642d5ef780ad316", "sha256_in_prefix": "cc11118fbe2961ef1da1d67742994faac653be46917f697c4642d5ef780ad316", "size_in_bytes": 19265}, {"_path": "lib/site-packages/torchvision/tv_tensors/__pycache__/_image.cpython-310.pyc", "path_type": "hardlink", "sha256": "90c0aa3193d884646158bdaa7b34e84118a6f77272fda758b335d9076de4c788", "sha256_in_prefix": "90c0aa3193d884646158bdaa7b34e84118a6f77272fda758b335d9076de4c788", "size_in_bytes": 2290}, {"_path": "lib/site-packages/torchvision/tv_tensors/__pycache__/_mask.cpython-310.pyc", "path_type": "hardlink", "sha256": "79d46cf642140617edca642bc426157b8f5b1714a69a96808949ceb7956da061", "sha256_in_prefix": "79d46cf642140617edca642bc426157b8f5b1714a69a96808949ceb7956da061", "size_in_bytes": 1821}, {"_path": "lib/site-packages/torchvision/tv_tensors/__pycache__/_torch_function_helpers.cpython-310.pyc", "path_type": "hardlink", "sha256": "7e2fd370af393b1f8b9959a8802dd9bcbce62855e1cb42afeea03eb993efbc77", "sha256_in_prefix": "7e2fd370af393b1f8b9959a8802dd9bcbce62855e1cb42afeea03eb993efbc77", "size_in_bytes": 2625}, {"_path": "lib/site-packages/torchvision/tv_tensors/__pycache__/_tv_tensor.cpython-310.pyc", "path_type": "hardlink", "sha256": "027c1dc3fad16dfc0242972c4e5620e04a159203b19584d3b8ea7deff5037f13", "sha256_in_prefix": "027c1dc3fad16dfc0242972c4e5620e04a159203b19584d3b8ea7deff5037f13", "size_in_bytes": 5210}, {"_path": "lib/site-packages/torchvision/tv_tensors/__pycache__/_video.cpython-310.pyc", "path_type": "hardlink", "sha256": "b9efbbc30cbe79e164084b943a58346df9acd8fca3c4de1f983e43a6a750e072", "sha256_in_prefix": "b9efbbc30cbe79e164084b943a58346df9acd8fca3c4de1f983e43a6a750e072", "size_in_bytes": 1822}, {"_path": "lib/site-packages/torchvision/tv_tensors/_bounding_boxes.py", "path_type": "hardlink", "sha256": "891ca4a0e9234a413b0140e9205340913c05d789195164780fbc3b75f7ba4731", "sha256_in_prefix": "891ca4a0e9234a413b0140e9205340913c05d789195164780fbc3b75f7ba4731", "size_in_bytes": 4574}, {"_path": "lib/site-packages/torchvision/tv_tensors/_dataset_wrapper.py", "path_type": "hardlink", "sha256": "dd2e57ff18337f16f805de00d39b374f0a56a034f988d7d42974b3ec534ecd8f", "sha256_in_prefix": "dd2e57ff18337f16f805de00d39b374f0a56a034f988d7d42974b3ec534ecd8f", "size_in_bytes": 24878}, {"_path": "lib/site-packages/torchvision/tv_tensors/_image.py", "path_type": "hardlink", "sha256": "afaebfe6f7d65481719d3537049ef846cbaff55a28175b1530edb26604180ec7", "sha256_in_prefix": "afaebfe6f7d65481719d3537049ef846cbaff55a28175b1530edb26604180ec7", "size_in_bytes": 1957}, {"_path": "lib/site-packages/torchvision/tv_tensors/_mask.py", "path_type": "hardlink", "sha256": "c7d92a7ba8a4f04bc48ca638e54b6e5ff091197b21e7e34d569a12b4214b25b7", "sha256_in_prefix": "c7d92a7ba8a4f04bc48ca638e54b6e5ff091197b21e7e34d569a12b4214b25b7", "size_in_bytes": 1490}, {"_path": "lib/site-packages/torchvision/tv_tensors/_torch_function_helpers.py", "path_type": "hardlink", "sha256": "53bafe406da3295fca61e51027684a3e56112f0ab3ff1844b3bfcbd685e906f3", "sha256_in_prefix": "53bafe406da3295fca61e51027684a3e56112f0ab3ff1844b3bfcbd685e906f3", "size_in_bytes": 2348}, {"_path": "lib/site-packages/torchvision/tv_tensors/_tv_tensor.py", "path_type": "hardlink", "sha256": "bfe7559be659b387dd4fa4d5700797eca9c0269b9d5547f9e55a9db50b60b611", "sha256_in_prefix": "bfe7559be659b387dd4fa4d5700797eca9c0269b9d5547f9e55a9db50b60b611", "size_in_bytes": 6380}, {"_path": "lib/site-packages/torchvision/tv_tensors/_video.py", "path_type": "hardlink", "sha256": "cd86d78f0167cecc7057c446b76042c3f016c520cf01260356d8528b860c893a", "sha256_in_prefix": "cd86d78f0167cecc7057c446b76042c3f016c520cf01260356d8528b860c893a", "size_in_bytes": 1420}, {"_path": "lib/site-packages/torchvision/utils.py", "path_type": "hardlink", "sha256": "7992cec6b15daf0fb342405f83235086e5247f281f445054a762ccdf8f2a0e91", "sha256_in_prefix": "7992cec6b15daf0fb342405f83235086e5247f281f445054a762ccdf8f2a0e91", "size_in_bytes": 27075}, {"_path": "lib/site-packages/torchvision/version.py", "path_type": "hardlink", "sha256": "0a354f23d21b4e7439ff4d8f8192938fd0e5dd51a260724964bc9345272ca7ae", "sha256_in_prefix": "0a354f23d21b4e7439ff4d8f8192938fd0e5dd51a260724964bc9345272ca7ae", "size_in_bytes": 202}], "paths_version": 1}, "requested_spec": "torchvision==0.18.1", "sha256": "203c102dc1d20541bdc0cdf1ce9105389e1501f3d216c0a546591ac7b2d86105", "size": 7998207, "subdir": "win-64", "timestamp": 1716986395000, "url": "https://conda.anaconda.org/pytorch/win-64/torchvision-0.18.1-py310_cu121.tar.bz2", "version": "0.18.1"}