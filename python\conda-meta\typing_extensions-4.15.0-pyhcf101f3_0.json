{"build": "pyhcf101f3_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.10", "python"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\typing_extensions-4.15.0-pyhcf101f3_0", "files": ["Lib/site-packages/typing_extensions-4.15.0.dist-info/INSTALLER", "Lib/site-packages/typing_extensions-4.15.0.dist-info/METADATA", "Lib/site-packages/typing_extensions-4.15.0.dist-info/RECORD", "Lib/site-packages/typing_extensions-4.15.0.dist-info/REQUESTED", "Lib/site-packages/typing_extensions-4.15.0.dist-info/WHEEL", "Lib/site-packages/typing_extensions-4.15.0.dist-info/direct_url.json", "Lib/site-packages/typing_extensions-4.15.0.dist-info/licenses/LICENSE", "Lib/site-packages/typing_extensions.py", "Lib/site-packages/__pycache__/typing_extensions.cpython-310.pyc"], "fn": "typing_extensions-4.15.0-pyhcf101f3_0.conda", "license": "PSF-2.0", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\typing_extensions-4.15.0-pyhcf101f3_0", "type": 1}, "md5": "0caa1af407ecff61170c9437a808404d", "name": "typing_extensions", "noarch": "python", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\typing_extensions-4.15.0-pyhcf101f3_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/typing_extensions-4.15.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "sha256_in_prefix": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "size_in_bytes": 6}, {"_path": "site-packages/typing_extensions-4.15.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c138378fe8f18934ac99de060535c53ec6c13aeed65e94c32641da7ee3192a72", "sha256_in_prefix": "c138378fe8f18934ac99de060535c53ec6c13aeed65e94c32641da7ee3192a72", "size_in_bytes": 3259}, {"_path": "site-packages/typing_extensions-4.15.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1be55aafc75705bcc3681457890e56459c2f684db98acdf5860ecbb01f2dac53", "sha256_in_prefix": "1be55aafc75705bcc3681457890e56459c2f684db98acdf5860ecbb01f2dac53", "size_in_bytes": 786}, {"_path": "site-packages/typing_extensions-4.15.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/typing_extensions-4.15.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1b68144734c4b66791f27add5d425f3620775585718a03d0f9b110ba3a4d88db", "sha256_in_prefix": "1b68144734c4b66791f27add5d425f3620775585718a03d0f9b110ba3a4d88db", "size_in_bytes": 82}, {"_path": "site-packages/typing_extensions-4.15.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "92e46b1a9bbfafe1d1b3fbce12ce83e7209a3f84414dea6ef70e08d13e0b8790", "sha256_in_prefix": "92e46b1a9bbfafe1d1b3fbce12ce83e7209a3f84414dea6ef70e08d13e0b8790", "size_in_bytes": 128}, {"_path": "site-packages/typing_extensions-4.15.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "sha256_in_prefix": "3b2f81fe21d181c499c59a256c8e1968455d6689d269aa85373bfb6af41da3bf", "size_in_bytes": 13936}, {"_path": "site-packages/typing_extensions.py", "path_type": "hardlink", "sha256": "433d11d170d3a24d2eb065ebc1bfe848cea7e3d7ce68567ab52bea2d4c2f7ed8", "sha256_in_prefix": "433d11d170d3a24d2eb065ebc1bfe848cea7e3d7ce68567ab52bea2d4c2f7ed8", "size_in_bytes": 160429}, {"_path": "Lib/site-packages/__pycache__/typing_extensions.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "032271135bca55aeb156cee361c81350c6f3fb203f57d024d7e5a1fc9ef18731", "size": 51692, "subdir": "noarch", "timestamp": 1756220668000, "url": "https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.15.0-pyhcf101f3_0.conda", "version": "4.15.0"}