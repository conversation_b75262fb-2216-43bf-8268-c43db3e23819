{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["brotli-python >=1.0.9", "h2 >=4,<5", "pysocks >=1.5.6,<2.0,!=1.5.7", "python >=3.9", "zstandard >=0.18.0"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\urllib3-2.5.0-pyhd8ed1ab_0", "files": ["Lib/site-packages/urllib3-2.5.0.dist-info/INSTALLER", "Lib/site-packages/urllib3-2.5.0.dist-info/METADATA", "Lib/site-packages/urllib3-2.5.0.dist-info/RECORD", "Lib/site-packages/urllib3-2.5.0.dist-info/REQUESTED", "Lib/site-packages/urllib3-2.5.0.dist-info/WHEEL", "Lib/site-packages/urllib3-2.5.0.dist-info/direct_url.json", "Lib/site-packages/urllib3-2.5.0.dist-info/licenses/LICENSE.txt", "Lib/site-packages/urllib3/__init__.py", "Lib/site-packages/urllib3/_base_connection.py", "Lib/site-packages/urllib3/_collections.py", "Lib/site-packages/urllib3/_request_methods.py", "Lib/site-packages/urllib3/_version.py", "Lib/site-packages/urllib3/connection.py", "Lib/site-packages/urllib3/connectionpool.py", "Lib/site-packages/urllib3/contrib/__init__.py", "Lib/site-packages/urllib3/contrib/emscripten/__init__.py", "Lib/site-packages/urllib3/contrib/emscripten/connection.py", "Lib/site-packages/urllib3/contrib/emscripten/emscripten_fetch_worker.js", "Lib/site-packages/urllib3/contrib/emscripten/fetch.py", "Lib/site-packages/urllib3/contrib/emscripten/request.py", "Lib/site-packages/urllib3/contrib/emscripten/response.py", "Lib/site-packages/urllib3/contrib/pyopenssl.py", "Lib/site-packages/urllib3/contrib/socks.py", "Lib/site-packages/urllib3/exceptions.py", "Lib/site-packages/urllib3/fields.py", "Lib/site-packages/urllib3/filepost.py", "Lib/site-packages/urllib3/http2/__init__.py", "Lib/site-packages/urllib3/http2/connection.py", "Lib/site-packages/urllib3/http2/probe.py", "Lib/site-packages/urllib3/poolmanager.py", "Lib/site-packages/urllib3/py.typed", "Lib/site-packages/urllib3/response.py", "Lib/site-packages/urllib3/util/__init__.py", "Lib/site-packages/urllib3/util/connection.py", "Lib/site-packages/urllib3/util/proxy.py", "Lib/site-packages/urllib3/util/request.py", "Lib/site-packages/urllib3/util/response.py", "Lib/site-packages/urllib3/util/retry.py", "Lib/site-packages/urllib3/util/ssl_.py", "Lib/site-packages/urllib3/util/ssl_match_hostname.py", "Lib/site-packages/urllib3/util/ssltransport.py", "Lib/site-packages/urllib3/util/timeout.py", "Lib/site-packages/urllib3/util/url.py", "Lib/site-packages/urllib3/util/util.py", "Lib/site-packages/urllib3/util/wait.py", "Lib/site-packages/urllib3/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/_base_connection.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/_collections.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/_request_methods.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/_version.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/connection.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/connectionpool.cpython-310.pyc", "Lib/site-packages/urllib3/contrib/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/urllib3/contrib/emscripten/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/urllib3/contrib/emscripten/__pycache__/connection.cpython-310.pyc", "Lib/site-packages/urllib3/contrib/emscripten/__pycache__/fetch.cpython-310.pyc", "Lib/site-packages/urllib3/contrib/emscripten/__pycache__/request.cpython-310.pyc", "Lib/site-packages/urllib3/contrib/emscripten/__pycache__/response.cpython-310.pyc", "Lib/site-packages/urllib3/contrib/__pycache__/pyopenssl.cpython-310.pyc", "Lib/site-packages/urllib3/contrib/__pycache__/socks.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/exceptions.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/fields.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/filepost.cpython-310.pyc", "Lib/site-packages/urllib3/http2/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/urllib3/http2/__pycache__/connection.cpython-310.pyc", "Lib/site-packages/urllib3/http2/__pycache__/probe.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/poolmanager.cpython-310.pyc", "Lib/site-packages/urllib3/__pycache__/response.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/connection.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/proxy.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/request.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/response.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/retry.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/ssl_.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/ssl_match_hostname.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/ssltransport.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/timeout.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/url.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/util.cpython-310.pyc", "Lib/site-packages/urllib3/util/__pycache__/wait.cpython-310.pyc"], "fn": "urllib3-2.5.0-pyhd8ed1ab_0.conda", "license": "MIT", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\urllib3-2.5.0-pyhd8ed1ab_0", "type": 1}, "md5": "436c165519e140cb08d246a4472a9d6a", "name": "urllib3", "noarch": "python", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\urllib3-2.5.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/urllib3-2.5.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/urllib3-2.5.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "99a6244c866dd1afa59040be84c6566c206670667225c60e791938fe7b93acd7", "sha256_in_prefix": "99a6244c866dd1afa59040be84c6566c206670667225c60e791938fe7b93acd7", "size_in_bytes": 6461}, {"_path": "site-packages/urllib3-2.5.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "08fded8c333525e0d7efde013e341cadd78f55615f81f780f4e79ad93a91195e", "sha256_in_prefix": "08fded8c333525e0d7efde013e341cadd78f55615f81f780f4e79ad93a91195e", "size_in_bytes": 5675}, {"_path": "site-packages/urllib3-2.5.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/urllib3-2.5.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/urllib3-2.5.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "e71ad0a3cf8330f399e18ad7bf155d7430c6ef9380ea73f14dabf87dc5f6f445", "sha256_in_prefix": "e71ad0a3cf8330f399e18ad7bf155d7430c6ef9380ea73f14dabf87dc5f6f445", "size_in_bytes": 103}, {"_path": "site-packages/urllib3-2.5.0.dist-info/licenses/LICENSE.txt", "path_type": "hardlink", "sha256": "130e3a64d5fdd5d096a752694634a7d9df284469de86e5732100268041e3d686", "sha256_in_prefix": "130e3a64d5fdd5d096a752694634a7d9df284469de86e5732100268041e3d686", "size_in_bytes": 1093}, {"_path": "site-packages/urllib3/__init__.py", "path_type": "hardlink", "sha256": "24ca35b60d67215d40789daf10d0bf4f17e5d1ee61e86ce5f43195935ad645ba", "sha256_in_prefix": "24ca35b60d67215d40789daf10d0bf4f17e5d1ee61e86ce5f43195935ad645ba", "size_in_bytes": 6979}, {"_path": "site-packages/urllib3/_base_connection.py", "path_type": "hardlink", "sha256": "4f57301f7461cecac187a073dc03865436e846c13bbde8a3a993d75d04d1d918", "sha256_in_prefix": "4f57301f7461cecac187a073dc03865436e846c13bbde8a3a993d75d04d1d918", "size_in_bytes": 5568}, {"_path": "site-packages/urllib3/_collections.py", "path_type": "hardlink", "sha256": "b4cedce89d622ad599615fd01986fcfabecdaf5e76e037a19ec6b451f87afe65", "sha256_in_prefix": "b4cedce89d622ad599615fd01986fcfabecdaf5e76e037a19ec6b451f87afe65", "size_in_bytes": 17295}, {"_path": "site-packages/urllib3/_request_methods.py", "path_type": "hardlink", "sha256": "802785f3948efd45385a83f0607228cffb70f9e33f1153a42c5a7c385b02ec30", "sha256_in_prefix": "802785f3948efd45385a83f0607228cffb70f9e33f1153a42c5a7c385b02ec30", "size_in_bytes": 9931}, {"_path": "site-packages/urllib3/_version.py", "path_type": "hardlink", "sha256": "665494901a3f3ddf7407aa4cd060ceee5daf8ad403dd008adf13d1fcad331490", "sha256_in_prefix": "665494901a3f3ddf7407aa4cd060ceee5daf8ad403dd008adf13d1fcad331490", "size_in_bytes": 511}, {"_path": "site-packages/urllib3/connection.py", "path_type": "hardlink", "sha256": "88fe2981226da6eb17c9895e8f3367fa08a1ff0582c4c53eab2e8e53591a6a97", "sha256_in_prefix": "88fe2981226da6eb17c9895e8f3367fa08a1ff0582c4c53eab2e8e53591a6a97", "size_in_bytes": 42613}, {"_path": "site-packages/urllib3/connectionpool.py", "path_type": "hardlink", "sha256": "64486e76c6bc048b9b0f63345e8c4106c8f16ec5f0320512707ee843d8be8f56", "sha256_in_prefix": "64486e76c6bc048b9b0f63345e8c4106c8f16ec5f0320512707ee843d8be8f56", "size_in_bytes": 43371}, {"_path": "site-packages/urllib3/contrib/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/urllib3/contrib/emscripten/__init__.py", "path_type": "hardlink", "sha256": "bba28d8338e51596ee0005daff26c247b810ef55491129c5f8821d0c0ef76ebc", "sha256_in_prefix": "bba28d8338e51596ee0005daff26c247b810ef55491129c5f8821d0c0ef76ebc", "size_in_bytes": 733}, {"_path": "site-packages/urllib3/contrib/emscripten/connection.py", "path_type": "hardlink", "sha256": "8fc0d1fdf944ee1b2816135f8aa1cb89a3da0ac55bcc6e388e06a1c2fb10e760", "sha256_in_prefix": "8fc0d1fdf944ee1b2816135f8aa1cb89a3da0ac55bcc6e388e06a1c2fb10e760", "size_in_bytes": 8771}, {"_path": "site-packages/urllib3/contrib/emscripten/emscripten_fetch_worker.js", "path_type": "hardlink", "sha256": "0837d817ff420e86edc7694689dc89d738c312fc0d4f917e75c8665565c38741", "sha256_in_prefix": "0837d817ff420e86edc7694689dc89d738c312fc0d4f917e75c8665565c38741", "size_in_bytes": 3655}, {"_path": "site-packages/urllib3/contrib/emscripten/fetch.py", "path_type": "hardlink", "sha256": "91ca34ea55a843e7dd15f379d7e9f3793cb03d50441e0f3d588b2ddf71f7c5c8", "sha256_in_prefix": "91ca34ea55a843e7dd15f379d7e9f3793cb03d50441e0f3d588b2ddf71f7c5c8", "size_in_bytes": 23484}, {"_path": "site-packages/urllib3/contrib/emscripten/request.py", "path_type": "hardlink", "sha256": "98bdbcb33cb52af137349856a2be633666aba7c830a650d4fbb8301996398344", "sha256_in_prefix": "98bdbcb33cb52af137349856a2be633666aba7c830a650d4fbb8301996398344", "size_in_bytes": 566}, {"_path": "site-packages/urllib3/contrib/emscripten/response.py", "path_type": "hardlink", "sha256": "ee854f10d6191eecc4191b46e341e89e91f9b402181ec19c1cf6c9b7667453e6", "sha256_in_prefix": "ee854f10d6191eecc4191b46e341e89e91f9b402181ec19c1cf6c9b7667453e6", "size_in_bytes": 9507}, {"_path": "site-packages/urllib3/contrib/pyopenssl.py", "path_type": "hardlink", "sha256": "5e9e589b4e558171a11dad02e3096eb6f1f163c4a72924ba58b6f6b79322bb4b", "sha256_in_prefix": "5e9e589b4e558171a11dad02e3096eb6f1f163c4a72924ba58b6f6b79322bb4b", "size_in_bytes": 19720}, {"_path": "site-packages/urllib3/contrib/socks.py", "path_type": "hardlink", "sha256": "fa26ab75ceb51b2a6c2730fa5bacae452eca542c9fa30710ae5ffbd7d1fb9483", "sha256_in_prefix": "fa26ab75ceb51b2a6c2730fa5bacae452eca542c9fa30710ae5ffbd7d1fb9483", "size_in_bytes": 7549}, {"_path": "site-packages/urllib3/exceptions.py", "path_type": "hardlink", "sha256": "a738ae9877f4570c77cf882f532ee8bbc9e5336c88617d0dde5df39c475e1795", "sha256_in_prefix": "a738ae9877f4570c77cf882f532ee8bbc9e5336c88617d0dde5df39c475e1795", "size_in_bytes": 9938}, {"_path": "site-packages/urllib3/fields.py", "path_type": "hardlink", "sha256": "1427fb5142d291fd7472e4d15164d0112cf1825d564fc7b6682cb791fc998a7b", "sha256_in_prefix": "1427fb5142d291fd7472e4d15164d0112cf1825d564fc7b6682cb791fc998a7b", "size_in_bytes": 10829}, {"_path": "site-packages/urllib3/filepost.py", "path_type": "hardlink", "sha256": "53c78d67e9a928a1e1ae56c7104893c7180ad7a21e8e111aeeecf8db2a80fdd2", "sha256_in_prefix": "53c78d67e9a928a1e1ae56c7104893c7180ad7a21e8e111aeeecf8db2a80fdd2", "size_in_bytes": 2388}, {"_path": "site-packages/urllib3/http2/__init__.py", "path_type": "hardlink", "sha256": "c73ac0487ed1e4035190f24ea2de651a70133aadca2aec97cc8e36adc9f09aab", "sha256_in_prefix": "c73ac0487ed1e4035190f24ea2de651a70133aadca2aec97cc8e36adc9f09aab", "size_in_bytes": 1741}, {"_path": "site-packages/urllib3/http2/connection.py", "path_type": "hardlink", "sha256": "e030740e46440b7c889211a3503207075ef0ad808b68bd008396c35b6fe618f2", "sha256_in_prefix": "e030740e46440b7c889211a3503207075ef0ad808b68bd008396c35b6fe618f2", "size_in_bytes": 12694}, {"_path": "site-packages/urllib3/http2/probe.py", "path_type": "hardlink", "sha256": "9e7024a9b8406a43a217be6bcfb5b4b9d677f047a1fee0fc7e357be0def71442", "sha256_in_prefix": "9e7024a9b8406a43a217be6bcfb5b4b9d677f047a1fee0fc7e357be0def71442", "size_in_bytes": 3014}, {"_path": "site-packages/urllib3/poolmanager.py", "path_type": "hardlink", "sha256": "a0ab203f512c008e0e5602bdfbd0f70185d94b91d857cc8a512a20f906c9f13b", "sha256_in_prefix": "a0ab203f512c008e0e5602bdfbd0f70185d94b91d857cc8a512a20f906c9f13b", "size_in_bytes": 23866}, {"_path": "site-packages/urllib3/py.typed", "path_type": "hardlink", "sha256": "51a0ae3c56b71fc5006a46edfb91bc48f69c95d4ce1af26fd7ca4f8d42798036", "sha256_in_prefix": "51a0ae3c56b71fc5006a46edfb91bc48f69c95d4ce1af26fd7ca4f8d42798036", "size_in_bytes": 93}, {"_path": "site-packages/urllib3/response.py", "path_type": "hardlink", "sha256": "4d54d2bba43553453b8681d8308471c6e878ceba1e328f1be442380ce035dc4a", "sha256_in_prefix": "4d54d2bba43553453b8681d8308471c6e878ceba1e328f1be442380ce035dc4a", "size_in_bytes": 46480}, {"_path": "site-packages/urllib3/util/__init__.py", "path_type": "hardlink", "sha256": "faa792d1071e8af6b3bc110a0cd142008fba00271d0ce1384ccbe8ed22cd9404", "sha256_in_prefix": "faa792d1071e8af6b3bc110a0cd142008fba00271d0ce1384ccbe8ed22cd9404", "size_in_bytes": 1001}, {"_path": "site-packages/urllib3/util/connection.py", "path_type": "hardlink", "sha256": "2633bbdb69731e5ccb5cf4e4afd65605d86c7979cc5633126f50c92d5ad74a74", "sha256_in_prefix": "2633bbdb69731e5ccb5cf4e4afd65605d86c7979cc5633126f50c92d5ad74a74", "size_in_bytes": 4444}, {"_path": "site-packages/urllib3/util/proxy.py", "path_type": "hardlink", "sha256": "b1e3fcf90e41e9b07474cb703e3f98719650df4bc7b8ba91bbeb48d096767f3b", "sha256_in_prefix": "b1e3fcf90e41e9b07474cb703e3f98719650df4bc7b8ba91bbeb48d096767f3b", "size_in_bytes": 1148}, {"_path": "site-packages/urllib3/util/request.py", "path_type": "hardlink", "sha256": "5ee02c1014f9f030196144f0a4c1f91ebdc0d4e3e830dbcd21822e9db22a6dcf", "sha256_in_prefix": "5ee02c1014f9f030196144f0a4c1f91ebdc0d4e3e830dbcd21822e9db22a6dcf", "size_in_bytes": 8411}, {"_path": "site-packages/urllib3/util/response.py", "path_type": "hardlink", "sha256": "bd013adfdba81218f5be98c4771bb994d22124249466477ba6a965508d0164e0", "sha256_in_prefix": "bd013adfdba81218f5be98c4771bb994d22124249466477ba6a965508d0164e0", "size_in_bytes": 3374}, {"_path": "site-packages/urllib3/util/retry.py", "path_type": "hardlink", "sha256": "6e3fb6614a9b9712e5bfc4c78397f1c30f83339e1709b8e0657210ef55e2a026", "sha256_in_prefix": "6e3fb6614a9b9712e5bfc4c78397f1c30f83339e1709b8e0657210ef55e2a026", "size_in_bytes": 18459}, {"_path": "site-packages/urllib3/util/ssl_.py", "path_type": "hardlink", "sha256": "8f19d0de6b189156a8909556a87367015755b4376276b4c70deca4e74830a9a4", "sha256_in_prefix": "8f19d0de6b189156a8909556a87367015755b4376276b4c70deca4e74830a9a4", "size_in_bytes": 19786}, {"_path": "site-packages/urllib3/util/ssl_match_hostname.py", "path_type": "hardlink", "sha256": "0e2ec353bce892896d6a94ff1744a3db57df631c1a4bf704e5aa4eb70b9e7b20", "sha256_in_prefix": "0e2ec353bce892896d6a94ff1744a3db57df631c1a4bf704e5aa4eb70b9e7b20", "size_in_bytes": 5845}, {"_path": "site-packages/urllib3/util/ssltransport.py", "path_type": "hardlink", "sha256": "133e0ef2947fbd3f1d6a7fc5bea0584ba7600df05710c7d57ebcdc754a167e2e", "sha256_in_prefix": "133e0ef2947fbd3f1d6a7fc5bea0584ba7600df05710c7d57ebcdc754a167e2e", "size_in_bytes": 8847}, {"_path": "site-packages/urllib3/util/timeout.py", "path_type": "hardlink", "sha256": "e1e4f5155799654ee1ee6603d49ab639735ee1fc5e91d36f868594919bac4690", "sha256_in_prefix": "e1e4f5155799654ee1ee6603d49ab639735ee1fc5e91d36f868594919bac4690", "size_in_bytes": 10346}, {"_path": "site-packages/urllib3/util/url.py", "path_type": "hardlink", "sha256": "59187e4cc617a2c9a0a7c9bc953e07e6ca681f0e7252395c3027d4e77024a00b", "sha256_in_prefix": "59187e4cc617a2c9a0a7c9bc953e07e6ca681f0e7252395c3027d4e77024a00b", "size_in_bytes": 15205}, {"_path": "site-packages/urllib3/util/util.py", "path_type": "hardlink", "sha256": "8f795b64ad633f28b00f7e13f08809cdd5846554fee04fb4bd82098bd52378d0", "sha256_in_prefix": "8f795b64ad633f28b00f7e13f08809cdd5846554fee04fb4bd82098bd52378d0", "size_in_bytes": 1146}, {"_path": "site-packages/urllib3/util/wait.py", "path_type": "hardlink", "sha256": "fe987c22b511deca8faa2d0ea29420254947e30ce419e3390a2c80ed7186b662", "sha256_in_prefix": "fe987c22b511deca8faa2d0ea29420254947e30ce419e3390a2c80ed7186b662", "size_in_bytes": 4423}, {"_path": "Lib/site-packages/urllib3/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/_base_connection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/_collections.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/_request_methods.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/_version.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/connection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/connectionpool.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/contrib/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/contrib/emscripten/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/contrib/emscripten/__pycache__/connection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/contrib/emscripten/__pycache__/fetch.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/contrib/emscripten/__pycache__/request.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/contrib/emscripten/__pycache__/response.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/contrib/__pycache__/pyopenssl.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/contrib/__pycache__/socks.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/exceptions.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/fields.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/filepost.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/http2/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/http2/__pycache__/connection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/http2/__pycache__/probe.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/poolmanager.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/__pycache__/response.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/__init__.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/connection.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/proxy.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/request.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/response.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/retry.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/ssl_.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/ssl_match_hostname.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/ssltransport.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/timeout.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/url.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/util.cpython-310.pyc", "path_type": "pyc_file"}, {"_path": "Lib/site-packages/urllib3/util/__pycache__/wait.cpython-310.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "4fb9789154bd666ca74e428d973df81087a697dbb987775bc3198d2215f240f8", "size": 101735, "subdir": "noarch", "timestamp": 1750271478000, "url": "https://conda.anaconda.org/conda-forge/noarch/urllib3-2.5.0-pyhd8ed1ab_0.conda", "version": "2.5.0"}