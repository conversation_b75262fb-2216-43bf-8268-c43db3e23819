{"build": "py310h29418f3_3", "build_number": 3, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["cffi >=1.11", "python >=3.10,<3.11.0a0", "python_abi 3.10.* *_cp310", "ucrt >=10.0.20348.0", "vc >=14.3,<15", "vc14_runtime >=14.44.35208"], "extracted_package_dir": "C:\\ProgramData\\anaconda3\\pkgs\\zstandard-0.23.0-py310h29418f3_3", "files": ["Lib/site-packages/zstandard-0.23.0-py3.10.egg-info/PKG-INFO", "Lib/site-packages/zstandard-0.23.0-py3.10.egg-info/SOURCES.txt", "Lib/site-packages/zstandard-0.23.0-py3.10.egg-info/dependency_links.txt", "Lib/site-packages/zstandard-0.23.0-py3.10.egg-info/requires.txt", "Lib/site-packages/zstandard-0.23.0-py3.10.egg-info/top_level.txt", "Lib/site-packages/zstandard/__init__.py", "Lib/site-packages/zstandard/__init__.pyi", "Lib/site-packages/zstandard/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/zstandard/__pycache__/backend_cffi.cpython-310.pyc", "Lib/site-packages/zstandard/_cffi.cp310-win_amd64.pyd", "Lib/site-packages/zstandard/backend_c.cp310-win_amd64.pyd", "Lib/site-packages/zstandard/backend_cffi.py", "Lib/site-packages/zstandard/py.typed"], "fn": "zstandard-0.23.0-py310h29418f3_3.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "C:\\ProgramData\\anaconda3\\pkgs\\zstandard-0.23.0-py310h29418f3_3", "type": 1}, "md5": "c7ced46235127f2ec7ea29b95840c343", "name": "zstandard", "package_tarball_full_path": "C:\\ProgramData\\anaconda3\\pkgs\\zstandard-0.23.0-py310h29418f3_3.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/zstandard-0.23.0-py3.10.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "e41a0fe1e2c7a0860f851a2baebfec8ef3f848a8b3c8df7dd7a59e900488473f", "sha256_in_prefix": "e41a0fe1e2c7a0860f851a2baebfec8ef3f848a8b3c8df7dd7a59e900488473f", "size_in_bytes": 3274}, {"_path": "Lib/site-packages/zstandard-0.23.0-py3.10.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "2902c4078db8dc1081804d32ed07d8472efcde5971844f7a10624f60a8224299", "sha256_in_prefix": "2902c4078db8dc1081804d32ed07d8472efcde5971844f7a10624f60a8224299", "size_in_bytes": 2739}, {"_path": "Lib/site-packages/zstandard-0.23.0-py3.10.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/zstandard-0.23.0-py3.10.egg-info/requires.txt", "path_type": "hardlink", "sha256": "11409f2a84863ef7a7294f76de5f540c867eb619e0070540b5f1213827a5f87b", "sha256_in_prefix": "11409f2a84863ef7a7294f76de5f540c867eb619e0070540b5f1213827a5f87b", "size_in_bytes": 75}, {"_path": "Lib/site-packages/zstandard-0.23.0-py3.10.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "27ec23f78a4f69d6388a915a6a7ad8065acc6e53927a01184bca3f2dda20ae95", "sha256_in_prefix": "27ec23f78a4f69d6388a915a6a7ad8065acc6e53927a01184bca3f2dda20ae95", "size_in_bytes": 10}, {"_path": "Lib/site-packages/zstandard/__init__.py", "path_type": "hardlink", "sha256": "c7debceb9c9bc6fef625ee1958501e841ce46db5dc696858b2aeb764036b8164", "sha256_in_prefix": "c7debceb9c9bc6fef625ee1958501e841ce46db5dc696858b2aeb764036b8164", "size_in_bytes": 7102}, {"_path": "Lib/site-packages/zstandard/__init__.pyi", "path_type": "hardlink", "sha256": "92f3fa52cf483d7e12b1f839070684dff13bbae135633554a3d853cd851c3470", "sha256_in_prefix": "92f3fa52cf483d7e12b1f839070684dff13bbae135633554a3d853cd851c3470", "size_in_bytes": 13938}, {"_path": "Lib/site-packages/zstandard/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "7b6370ea856a3482fd8f9d51194502e7276175326a619a7c55a1c96018354f09", "sha256_in_prefix": "7b6370ea856a3482fd8f9d51194502e7276175326a619a7c55a1c96018354f09", "size_in_bytes": 4984}, {"_path": "Lib/site-packages/zstandard/__pycache__/backend_cffi.cpython-310.pyc", "path_type": "hardlink", "sha256": "9b1d932acf1ec24f19b83775fd24e17ffb3b096f2a0b167a5ba3a45018de35ba", "sha256_in_prefix": "9b1d932acf1ec24f19b83775fd24e17ffb3b096f2a0b167a5ba3a45018de35ba", "size_in_bytes": 124822}, {"_path": "Lib/site-packages/zstandard/_cffi.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "382297566036aeb3714533343aba3b5e28c069ae052afdce1a78315315ae23f6", "sha256_in_prefix": "382297566036aeb3714533343aba3b5e28c069ae052afdce1a78315315ae23f6", "size_in_bytes": 655360}, {"_path": "Lib/site-packages/zstandard/backend_c.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "bec234d40f506597f47e00e89e5994b78e84af4e3cd6bea16649e0476f95080e", "sha256_in_prefix": "bec234d40f506597f47e00e89e5994b78e84af4e3cd6bea16649e0476f95080e", "size_in_bytes": 517120}, {"_path": "Lib/site-packages/zstandard/backend_cffi.py", "path_type": "hardlink", "sha256": "db6751fcd4fcc5bd283a2b94e414bb6250c11269669d47c42b94719744cd4e29", "sha256_in_prefix": "db6751fcd4fcc5bd283a2b94e414bb6250c11269669d47c42b94719744cd4e29", "size_in_bytes": 152504}, {"_path": "Lib/site-packages/zstandard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}], "paths_version": 1}, "requested_spec": "None", "sha256": "1282801d99392c8e674151633c3120c12452a4ca6c2141b90b164c6b8a7f1724", "size": 333571, "subdir": "win-64", "timestamp": 1756075855000, "url": "https://conda.anaconda.org/conda-forge/win-64/zstandard-0.23.0-py310h29418f3_3.conda", "version": "0.23.0"}